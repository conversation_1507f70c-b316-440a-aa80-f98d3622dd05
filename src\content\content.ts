/**
 * Content Script for Airtm Monitor Pro Extension
 * Runs on Airtm peer transfers page and handles GraphQL interception
 */

import { initializeFetchInterceptor, getStoredOffers, updateDuplicateDetectionConfig } from './fetch-interceptor'
import type { ExtensionMessage } from '../types'

// Target page URL pattern
const TARGET_PAGE_PATTERN = /https:\/\/app\.airtm\.com\/peer-transfers\/available/

// Global variable to store current keyboard listener
let currentKeyboardListener: ((event: KeyboardEvent) => void) | null = null

/**
 * Initialize content script
 */
async function initializeContentScript(): Promise<void> {
  try {
    console.log('🚀 Airtm Monitor Pro: Content script initializing...')
    console.log('📍 Current URL:', window.location.href)
    console.log('🎯 Target pattern:', TARGET_PAGE_PATTERN.toString())

    // Check if extension context is valid
    if (!isExtensionContextValid()) {
      console.log('⚠️ Extension context invalidated, content script will not initialize')
      return
    }

    // Check if we're on the correct page
    if (!TARGET_PAGE_PATTERN.test(window.location.href)) {
      console.log('❌ Not on target page, content script will not activate')
      console.log('💡 Expected URL pattern: https://app.airtm.com/peer-transfers/available')
      return
    }

    console.log('✅ On target page, initializing fetch interceptor...')
    console.log('🔧 Window.fetch available:', typeof window.fetch !== 'undefined')

    // Initialize fetch interceptor
    initializeFetchInterceptor()

    // Setup message listeners
    setupMessageListeners()

    // Setup keyboard shortcuts
    setupKeyboardShortcuts()

    // Setup page event listeners
    setupPageEventListeners()

    console.log('🎉 Airtm Monitor Pro: Content script initialized successfully')
    console.log('👂 Now listening for GraphQL requests...')

  } catch (error) {
    console.error('❌ Error initializing content script:', error)
    const errorMessage = error instanceof Error ? error.message : String(error)
    if (errorMessage.includes('Extension context invalidated')) {
      console.log('🔄 Extension context was invalidated during initialization')
    }
  }
}

/**
 * Check if extension context is still valid
 */
function isExtensionContextValid(): boolean {
  try {
    return !!(chrome.runtime && chrome.runtime.id)
  } catch (error) {
    return false
  }
}

/**
 * Setup message listeners for communication with background script
 */
function setupMessageListeners(): void {
  if (!isExtensionContextValid()) {
    console.log('⚠️ Extension context invalidated, skipping message listener setup')
    return
  }

  chrome.runtime.onMessage.addListener((message: ExtensionMessage, _sender, sendResponse) => {
    if (!isExtensionContextValid()) {
      console.log('⚠️ Extension context invalidated during message handling')
      return false
    }

    console.log('Content script received message:', message)

    switch (message.type) {
      case 'ACCEPT_OFFER':
        handleAcceptOffer(message.data?.offerId)
          .then(result => sendResponse({ success: true, result }))
          .catch(error => sendResponse({ success: false, error: error.message }))
        return true // Keep message channel open for async response

      case 'REJECT_OFFER':
        handleRejectOffer(message.data?.offerId)
          .then(result => sendResponse({ success: true, result }))
          .catch(error => sendResponse({ success: false, error: error.message }))
        return true

      case 'GET_OFFERS':
        getStoredOffers()
          .then(offers => sendResponse({ success: true, offers }))
          .catch(error => sendResponse({ success: false, error: error.message }))
        return true

      case 'STATUS_UPDATE':
        handleStatusUpdate(message.data)
        sendResponse({ success: true })
        break

      case 'HIGHLIGHT_OFFER':
        handleHighlightOffer(message.data?.offer)
        sendResponse({ success: true })
        break

      case 'FOCUS_OFFER':
        handleFocusOffer(message.data?.offerId)
          .then(() => sendResponse({ success: true }))
          .catch(error => sendResponse({ success: false, error: error.message }))
        return true
        
      case 'UPDATE_SETTINGS':
        handleSettingsUpdate(message.data)
        sendResponse({ success: true })
        break
        
      case 'EXECUTE_COMMAND':
        if (message.command) {
          handleExecuteCommand(message.command)
            .then(() => sendResponse({ success: true }))
            .catch(error => sendResponse({ success: false, error: error.message }))
        } else {
          sendResponse({ success: false, error: 'Command is required' })
        }
        return true

      default:
        console.log('Unknown message type:', message.type)
        sendResponse({ success: false, error: 'Unknown message type' })
    }
  })
}

/**
 * Setup keyboard shortcuts
 */
function setupKeyboardShortcuts(): void {
  // Remove existing listener if it exists
  if (currentKeyboardListener) {
    document.removeEventListener('keydown', currentKeyboardListener)
    currentKeyboardListener = null
  }

  // Create new listener
  currentKeyboardListener = (event: KeyboardEvent) => {
    // Check for extension shortcuts
    if (event.ctrlKey && event.shiftKey) {
      switch (event.key.toLowerCase()) {
        case 'a':
          event.preventDefault()
          handleQuickAccept()
          break
        case 'r':
          event.preventDefault()
          handleQuickReject()
          break
        case 'd':
          event.preventDefault()
          handleShowDetails()
          break
        case 'c':
          event.preventDefault()
          handleCycleOffers()
          break
      }
    }
  }

  // Add the new listener
  document.addEventListener('keydown', currentKeyboardListener)
}

/**
 * Setup page event listeners
 */
function setupPageEventListeners(): void {
  // Listen for custom events from fetch interceptor
  window.addEventListener('airtm-offers-detected', (event: any) => {
    console.log('Offers detected event:', event.detail)
    
    // Notify background script with error handling
    if (isExtensionContextValid()) {
      try {
        chrome.runtime.sendMessage({
          type: 'OFFERS_UPDATE',
          data: event.detail,
          metadata: {
            timestamp: new Date().toISOString(),
            source: 'content-script'
          }
        }).catch((error) => {
          console.log('⚠️ Failed to send message to background script:', error)
        })
      } catch (error) {
        console.log('⚠️ Extension context invalidated, cannot send message:', error)
      }
    } else {
      console.log('⚠️ Extension context invalidated, skipping message send')
    }
  })

  // Listen for page navigation changes
  let currentUrl = window.location.href
  const observer = new MutationObserver(() => {
    if (window.location.href !== currentUrl) {
      currentUrl = window.location.href
      console.log('Page navigation detected:', currentUrl)
      
      // Re-initialize if navigated back to target page
      if (TARGET_PAGE_PATTERN.test(currentUrl)) {
        setTimeout(() => initializeFetchInterceptor(), 1000)
      }
    }
  })

  observer.observe(document.body, {
    childList: true,
    subtree: true
  })
}

/**
 * Handle offer acceptance
 */
async function handleAcceptOffer(offerId: string): Promise<boolean> {
  if (!offerId) {
    throw new Error('Offer ID is required')
  }

  console.log('Attempting to accept offer:', offerId)

  try {
    // Find the offer element on the page
    const offerElement = findOfferElement(offerId)
    if (!offerElement) {
      throw new Error(`Offer element not found for ID: ${offerId}`)
    }

    // Find and click the accept button
    const acceptButton = offerElement.querySelector('[data-testid="accept-button"], .accept-btn, button[aria-label*="accept" i]')
    if (!acceptButton) {
      throw new Error('Accept button not found')
    }

    // Click the button
    (acceptButton as HTMLElement).click()

    console.log('Offer acceptance initiated:', offerId)
    return true

  } catch (error) {
    console.error('Error accepting offer:', error)
    throw error
  }
}

/**
 * Handle offer rejection
 */
async function handleRejectOffer(offerId: string): Promise<boolean> {
  if (!offerId) {
    throw new Error('Offer ID is required')
  }

  console.log('Attempting to reject offer:', offerId)

  try {
    // Find the offer element on the page
    const offerElement = findOfferElement(offerId)
    if (!offerElement) {
      throw new Error(`Offer element not found for ID: ${offerId}`)
    }

    // Find and click the reject button
    const rejectButton = offerElement.querySelector('[data-testid="reject-button"], .reject-btn, button[aria-label*="reject" i]')
    if (!rejectButton) {
      throw new Error('Reject button not found')
    }

    // Click the button
    (rejectButton as HTMLElement).click()

    console.log('Offer rejection initiated:', offerId)
    return true

  } catch (error) {
    console.error('Error rejecting offer:', error)
    throw error
  }
}

/**
 * Find offer element on page by ID
 */
function findOfferElement(offerId: string): Element | null {
  // Try different selectors to find the offer element
  const selectors = [
    `[data-offer-id="${offerId}"]`,
    `[data-id="${offerId}"]`,
    `#offer-${offerId}`,
    `.offer-${offerId}`
  ]

  for (const selector of selectors) {
    const element = document.querySelector(selector)
    if (element) {
      return element
    }
  }

  // Fallback: search by text content
  const allOfferElements = document.querySelectorAll('[class*="offer"], [data-testid*="offer"]')
  for (const element of allOfferElements) {
    if (element.textContent?.includes(offerId)) {
      return element
    }
  }

  return null
}

/**
 * Handle execute command from Chrome extension shortcuts
 */
async function handleExecuteCommand(command: string): Promise<void> {
  try {
    console.log('Executing command:', command)
    
    switch (command) {
      case 'accept_offer':
        await handleQuickAccept()
        break
      case 'reject_offer':
        await handleQuickReject()
        break
      case 'open_offer_details':
        handleShowDetails()
        break
      case 'cycle_offers':
        handleCycleOffers()
        break
      default:
        console.warn('Unknown command:', command)
    }
  } catch (error) {
    console.error('Error executing command:', command, error)
    throw error
  }
}

/**
 * Handle quick accept (keyboard shortcut)
 */
async function handleQuickAccept(): Promise<void> {
  try {
    const offers = await getStoredOffers()
    if (offers.length === 0) {
      console.log('No offers available for quick accept')
      return
    }

    // Accept the first offer
    const firstOffer = offers[0]
    await handleAcceptOffer(firstOffer.id)
    
  } catch (error) {
    console.error('Error in quick accept:', error)
  }
}

/**
 * Handle quick reject (keyboard shortcut)
 */
async function handleQuickReject(): Promise<void> {
  try {
    const offers = await getStoredOffers()
    if (offers.length === 0) {
      console.log('No offers available for quick reject')
      return
    }

    // Reject the first offer
    const firstOffer = offers[0]
    await handleRejectOffer(firstOffer.id)
    
  } catch (error) {
    console.error('Error in quick reject:', error)
  }
}

/**
 * Handle show details (keyboard shortcut)
 */
function handleShowDetails(): void {
  console.log('Show details shortcut triggered')
  
  // Send message to open popup or show details
  chrome.runtime.sendMessage({
    type: 'SHOW_DETAILS',
    metadata: {
      timestamp: new Date().toISOString(),
      source: 'keyboard-shortcut'
    }
  })
}

/**
 * Handle cycle offers (keyboard shortcut)
 */
function handleCycleOffers(): void {
  console.log('Cycle offers shortcut triggered')
  
  // Send message to cycle through offers
  chrome.runtime.sendMessage({
    type: 'CYCLE_OFFERS',
    metadata: {
      timestamp: new Date().toISOString(),
      source: 'keyboard-shortcut'
    }
  })
}

/**
 * Handle status updates from background script
 */
function handleStatusUpdate(data: any): void {
  console.log('Status update received:', data)
  
  // Could update page UI or show notifications
  if (data.monitoring) {
    console.log('Monitoring is active')
  } else {
    console.log('Monitoring is inactive')
  }
}

/**
 * Handle highlighting new offers on the page
 */
function handleHighlightOffer(offer: any): void {
  if (!offer) return
  
  console.log('Highlighting offer:', offer.id)
  
  // Inject CSS for highlighting if not already done
  injectHighlightStyles()
  
  // Find and highlight the offer element
  highlightOfferElement(offer)
  
  // Play notification sound
  playNotificationSound()
  
  // Removed floating notification panel as requested
}

/**
 * Handle focusing on a specific offer (scroll to and highlight)
 */
async function handleFocusOffer(offerId: string): Promise<void> {
  if (!offerId) {
    throw new Error('Offer ID is required')
  }

  console.log('Focusing on offer:', offerId)

  try {
    // Inject CSS for highlighting if not already done
    injectHighlightStyles()

    // Find the specific offer element
    const offerElement = findOfferElement(offerId)
    
    if (offerElement) {
      // Add special focus highlight class
      offerElement.classList.add('airtm-monitor-highlight', 'airtm-monitor-focus')
      
      // Scroll to the offer with smooth animation
      offerElement.scrollIntoView({ 
        behavior: 'smooth', 
        block: 'center',
        inline: 'center'
      })
      
      // Add pulsing effect for better visibility
      const focusStyle = document.createElement('style')
      focusStyle.id = 'airtm-focus-styles'
      focusStyle.textContent = `
        .airtm-monitor-focus {
          animation: airtm-focus-pulse 3s infinite !important;
          border: 4px solid #ef4444 !important;
          box-shadow: 0 0 30px rgba(239, 68, 68, 0.8) !important;
        }
        
        .airtm-monitor-focus::before {
          content: '🎯 DETECTED OFFER!' !important;
          background: linear-gradient(135deg, #ef4444, #dc2626) !important;
        }
        
        @keyframes airtm-focus-pulse {
          0% { 
            box-shadow: 0 0 30px rgba(239, 68, 68, 0.8);
            transform: scale(1);
          }
          50% { 
            box-shadow: 0 0 50px rgba(239, 68, 68, 1);
            transform: scale(1.02);
          }
          100% { 
            box-shadow: 0 0 30px rgba(239, 68, 68, 0.8);
            transform: scale(1);
          }
        }
      `
      
      // Remove existing focus styles
      const existingFocusStyle = document.getElementById('airtm-focus-styles')
      if (existingFocusStyle) {
        existingFocusStyle.remove()
      }
      
      document.head.appendChild(focusStyle)
      
      // Removed floating notification as requested
      
      // Remove focus highlight after 15 seconds
      setTimeout(() => {
        offerElement.classList.remove('airtm-monitor-focus')
        const focusStyleElement = document.getElementById('airtm-focus-styles')
        if (focusStyleElement) {
          focusStyleElement.remove()
        }
      }, 15000)
      
      console.log('Successfully focused on offer:', offerId)
    } else {
      // If specific offer not found, try to find and highlight any new offers
      console.log('Specific offer not found, highlighting first available offer')
      
      const firstOffer = document.querySelector('[data-testid*="offer"], [class*="offer"], .MuiCard-root')
      if (firstOffer) {
        firstOffer.classList.add('airtm-monitor-highlight')
        firstOffer.scrollIntoView({ behavior: 'smooth', block: 'center' })
        
        setTimeout(() => {
          firstOffer.classList.remove('airtm-monitor-highlight')
        }, 10000)
      }
    }
    
  } catch (error) {
    console.error('Error focusing on offer:', error)
    throw error
  }
}

/**
 * Inject CSS styles for offer highlighting
 */
function injectHighlightStyles(): void {
  if (document.getElementById('airtm-monitor-styles')) return
  
  const style = document.createElement('style')
  style.id = 'airtm-monitor-styles'
  style.textContent = `
    .airtm-monitor-highlight {
      animation: airtm-pulse 2s infinite;
      border: 3px solid #10b981 !important;
      border-radius: 8px !important;
      box-shadow: 0 0 20px rgba(16, 185, 129, 0.5) !important;
      background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(34, 197, 94, 0.1)) !important;
      position: relative !important;
    }
    
    .airtm-monitor-highlight::before {
      content: '🔥 NEW OFFER!';
      position: absolute;
      top: -10px;
      right: -10px;
      background: linear-gradient(135deg, #10b981, #22c55e);
      color: white;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: bold;
      z-index: 1000;
      animation: airtm-bounce 1s infinite;
    }
    
    @keyframes airtm-pulse {
      0% { box-shadow: 0 0 20px rgba(16, 185, 129, 0.5); }
      50% { box-shadow: 0 0 30px rgba(16, 185, 129, 0.8); }
      100% { box-shadow: 0 0 20px rgba(16, 185, 129, 0.5); }
    }
    
    @keyframes airtm-bounce {
      0%, 100% { transform: translateY(0); }
      50% { transform: translateY(-5px); }
    }
    
    .airtm-floating-notification {
      position: fixed;
      top: 20px;
      right: 20px;
      background: linear-gradient(135deg, #10b981, #22c55e);
      color: white;
      padding: 16px 20px;
      border-radius: 12px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
      z-index: 10000;
      animation: airtm-slide-in 0.5s ease-out;
      max-width: 300px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
    
    .airtm-floating-notification h3 {
      margin: 0 0 8px 0;
      font-size: 16px;
      font-weight: bold;
    }
    
    .airtm-floating-notification p {
      margin: 0;
      font-size: 14px;
      opacity: 0.9;
    }
    
    @keyframes airtm-slide-in {
      from {
        transform: translateX(100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }
  `
  document.head.appendChild(style)
}

/**
 * Find and highlight the specific offer element
 */
function highlightOfferElement(offer: any): void {
  console.log('Attempting to highlight offer:', offer)
  
  // Wait for DOM to be ready
  setTimeout(() => {
    let highlighted = false
    
    // Try different selectors to find offer elements
    const selectors = [
      '[data-testid*="offer"]',
      '[class*="offer"]',
      '[class*="card"]',
      '.MuiCard-root',
      '.MuiPaper-root',
      '[data-cy*="offer"]',
      '.offer-card',
      '.peer-transfer-card'
    ]
    
    for (const selector of selectors) {
      if (highlighted) break
      
      const elements = document.querySelectorAll(selector)
      console.log(`Found ${elements.length} elements with selector: ${selector}`)
      
      elements.forEach((element: Element) => {
        if (highlighted) return
        
        // Check if this element contains offer data matching our offer
        const text = element.textContent || ''
        const offerAmount = offer.grossAmount || offer.amount
        const offerCurrency = offer.currency?.symbol || offer.walletCurrency?.symbol
        // More flexible matching - check for amount or currency
        let matches = false
        
        if (offerAmount) {
          // Try different amount formats
          const amountStr = offerAmount.toString()
          const formattedAmount = parseFloat(amountStr).toLocaleString()
          if (text.includes(amountStr) || text.includes(formattedAmount)) {
            matches = true
          }
        }
        
        if (!matches && offerCurrency && text.includes(offerCurrency)) {
          matches = true
        }
        
        // Rate field has been removed from schema
        
        if (matches) {
          element.classList.add('airtm-monitor-highlight')
          
          // Scroll into view
          element.scrollIntoView({ behavior: 'smooth', block: 'center' })
          
          // Remove highlight after 10 seconds
          setTimeout(() => {
            element.classList.remove('airtm-monitor-highlight')
          }, 10000)
          
          console.log('✅ Highlighted offer element:', element)
          highlighted = true
          return
        }
      })
    }
    
    // If no specific element found, highlight the most recent offer element
    if (!highlighted) {
      console.log('No specific match found, highlighting most recent offer')
      const offerElements = document.querySelectorAll('[data-testid*="offer"], [class*="offer"], .MuiCard-root')
      
      if (offerElements.length > 0) {
        // Highlight the first (most recent) offer
        const firstOffer = offerElements[0]
        firstOffer.classList.add('airtm-monitor-highlight')
        firstOffer.scrollIntoView({ behavior: 'smooth', block: 'center' })
        
        setTimeout(() => {
          firstOffer.classList.remove('airtm-monitor-highlight')
        }, 10000)
        
        console.log('✅ Highlighted first available offer element:', firstOffer)
        highlighted = true
      }
    }
    
    if (!highlighted) {
      console.warn('⚠️ Could not find any offer elements to highlight')
    }
  }, 500)
}

/**
 * Handle settings update from background script
 */
function handleSettingsUpdate(settings: any): void {
  console.log('📝 Received settings update:', settings)
  
  if (settings.duplicateDetection) {
    updateDuplicateDetectionConfig(settings.duplicateDetection)
  }
  
  // Re-setup keyboard shortcuts if hotkeys were updated
  if (settings.hotkeys) {
    console.log('🔄 Hotkeys updated, re-setting up keyboard shortcuts')
    // Remove existing listeners and re-setup with new hotkeys
    setupKeyboardShortcuts()
  }
}

// Sound management variables
let currentAudio: HTMLAudioElement | null = null
let lastSoundTime = 0
const SOUND_COOLDOWN = 0 // Set to 0 for instant sound playback

/**
 * Play notification sound with interruption logic - stops current sound and plays new one
 */
function playNotificationSound(): void {
  const now = Date.now()
  
  // Stop any currently playing sound
  if (currentAudio) {
    try {
      currentAudio.pause()
      currentAudio.currentTime = 0
      console.log('🔇 Previous sound interrupted for new offer')
    } catch (error) {
      console.log('Error stopping previous sound:', error)
    }
    currentAudio = null
  }
  
  // Check cooldown to prevent too rapid succession
  if (now - lastSoundTime < SOUND_COOLDOWN) {
    console.log('🔇 Sound skipped - too rapid succession (debounced)')
    return
  }
  
  lastSoundTime = now
  try {
    currentAudio = new Audio(chrome.runtime.getURL('sounds/beep.mp3'))
    currentAudio.volume = 0.5
    
    // Clear reference when sound ends
    currentAudio.onended = () => {
      currentAudio = null
      console.log('🔊 Sound playback completed')
    }
    
    // Clear reference on error
    currentAudio.onerror = () => {
      currentAudio = null
      console.log('🔇 Sound playback error')
    }
    
    currentAudio.play().then(() => {
      console.log('🔊 New notification sound started')
    }).catch(e => {
      console.log('Could not play sound:', e)
      currentAudio = null
    })
  } catch (error) {
    console.log('Sound not available:', error)
    currentAudio = null
  }
}

// Removed showFloatingNotification and showFocusNotification functions as requested

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeContentScript)
} else {
  initializeContentScript()
}

// Periodic context health check
let contextHealthCheckInterval: number | null = null;
let lastContextState = true;

function startContextHealthCheck(): void {
  // Check context every 5 seconds
  contextHealthCheckInterval = setInterval(() => {
    const currentContextState = isExtensionContextValid();

    // If context state changed
    if (currentContextState !== lastContextState) {
      if (currentContextState) {
        console.log('✅ Extension context restored during health check');
        // Context was restored, try to send any fallback data
        try {
          const fallbackData = localStorage.getItem('airtm_offers_fallback');
          if (fallbackData) {
            console.log('📤 Sending fallback data after context restoration...');
            const data = JSON.parse(fallbackData);
            chrome.runtime.sendMessage({
              type: 'OFFERS_UPDATE',
              data: {
                offers: data.offers,
                timestamp: data.timestamp,
                source: 'health_check_recovery'
              }
            }).then(() => {
              localStorage.removeItem('airtm_offers_fallback');
              console.log('✅ Fallback data sent and cleared during health check');
            }).catch((error) => {
              console.error('❌ Failed to send fallback data during health check:', error);
            });
          }
        } catch (error) {
          console.error('❌ Error processing fallback data during health check:', error);
        }
      } else {
        console.warn('⚠️ Extension context lost during health check');
      }
      lastContextState = currentContextState;
    }
  }, 5000); // Check every 5 seconds
}

// Start health check
startContextHealthCheck();

// Clean up on page unload
window.addEventListener('beforeunload', () => {
  if (contextHealthCheckInterval) {
    clearInterval(contextHealthCheckInterval);
  }
});
