/**
 * Utility functions for extension context validation
 * Standardized across all files based on comprehensive analysis
 */

/**
 * Check if extension context is still valid
 * Standardized implementation used across background, content, and fetch-interceptor
 */
export function isExtensionContextValid(): boolean {
  try {
    // Check both chrome object and runtime.id availability
    return !!(chrome?.runtime?.id)
  } catch (error) {
    return false
  }
}

/**
 * Validate extension context and throw descriptive error if invalid
 */
export function validateExtensionContext(): void {
  if (!isExtensionContextValid()) {
    throw new Error('Extension context is invalid. Please reload the extension or refresh the page.')
  }
}

/**
 * Check if error is due to extension context invalidation
 */
export function isContextInvalidationError(error: unknown): boolean {
  const errorMessage = error instanceof Error ? error.message : String(error)
  return errorMessage?.includes('Could not establish connection') || 
         errorMessage?.includes('Receiving end does not exist') ||
         errorMessage?.includes('Extension context invalidated')
}

/**
 * Safe wrapper for chrome runtime message sending with context validation
 */
export async function safeRuntimeSendMessage(message: any): Promise<any> {
  validateExtensionContext()
  
  try {
    return await chrome.runtime.sendMessage(message)
  } catch (error) {
    if (isContextInvalidationError(error)) {
      throw new Error('Extension context lost during message sending. Please reload the extension.')
    }
    throw error
  }
}

/**
 * Safe wrapper for chrome tabs message sending with context validation
 */
export async function safeTabsSendMessage(tabId: number, message: any): Promise<any> {
  validateExtensionContext()
  
  try {
    return await chrome.tabs.sendMessage(tabId, message)
  } catch (error) {
    if (isContextInvalidationError(error)) {
      throw new Error('Extension context lost during tab message sending. Please reload the extension.')
    }
    throw error
  }
}
