var Ne=Object.defineProperty;var $e=(e,t,o)=>t in e?Ne(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o;var M=(e,t,o)=>($e(e,typeof t!="symbol"?t+"":t,o),o);import{D as G}from"./index-357a2da0.js";import{i as Ce,a as Ee}from"./extension-context-5094bf00.js";const I=class I{constructor(){M(this,"activeNotifications",new Map);M(this,"settings",{autoCloseDelay:3e4,playSound:!0})}static getInstance(){return I.instance||(I.instance=new I),I.instance}updateSettings(t){this.settings={...this.settings,...t}}async showNotification(t,o){const r={...this.settings,...o};try{await this.showChromeNotification(t,r),await this.highlightOfferOnPage(t),r.playSound&&await this.playNotificationSound()}catch(n){console.error("Error showing notification:",n)}}async showChromeNotification(t,o){var a,l;if(typeof chrome>"u"||!chrome.notifications)throw new Error("Chrome notifications not available");const r=`offer_${t.id}_${Date.now()}`,n=`New ${t.operationType} Offer`,s=`${t.grossAmount} ${((a=t.currency)==null?void 0:a.symbol)||((l=t.walletCurrency)==null?void 0:l.symbol)}`;await chrome.notifications.create(r,{type:"basic",iconUrl:"icons/icon48.png",title:n,message:s,contextMessage:t.peer?`From: ${t.peer.firstName} ${t.peer.lastName}`:void 0,buttons:[{title:"Accept"},{title:"Ignore"}],requireInteraction:!0}),this.activeNotifications.set(t.id,r),setTimeout(()=>{this.closeNotification(t.id)},o.autoCloseDelay)}async highlightOfferOnPage(t){var o;try{const r=await chrome.tabs.query({active:!0,currentWindow:!0});(o=r[0])!=null&&o.id&&await chrome.tabs.sendMessage(r[0].id,{type:"HIGHLIGHT_OFFER",data:{offer:t}})}catch(r){console.log("Could not highlight offer on page:",r)}}closeNotification(t){const o=this.activeNotifications.get(t);if(o){try{typeof o=="string"&&o.startsWith("offer_")&&chrome.notifications.clear(o)}catch(r){console.error("Error closing notification:",r)}this.activeNotifications.delete(t)}}closeAllNotifications(){for(const[t]of this.activeNotifications)this.closeNotification(t)}async playNotificationSound(){var t;try{if(typeof chrome<"u"&&chrome.offscreen){try{await chrome.offscreen.createDocument({url:"sounds/beep.html",reasons:["AUDIO_PLAYBACK"],justification:"Play notification sound"})}catch(o){if(!((t=o.message)!=null&&t.includes("Only a single offscreen document")))throw o}chrome.runtime.sendMessage({type:"PLAY_SOUND"})}}catch(o){console.error("Error playing notification sound:",o)}}handleMessage(t,o){var r,n;switch(t.type){case"ACCEPT_OFFER":this.handleOfferAction("accept",(r=t.data)==null?void 0:r.offerId);break;case"IGNORE_OFFER":this.handleOfferAction("ignore",(n=t.data)==null?void 0:n.offerId);break}}async handleOfferAction(t,o){if(o)try{if(this.closeNotification(o),typeof chrome<"u"&&chrome.runtime){const r=t==="accept"?"ACCEPT_OFFER":"REJECT_OFFER";await chrome.runtime.sendMessage({type:r,data:{offerId:o}})}}catch(r){console.error(`Error handling ${t} action:`,r)}}static isSupported(){return typeof chrome<"u"&&!!chrome.notifications}getAvailableMethods(){const t=[];return typeof chrome<"u"&&chrome.notifications&&t.push("chrome"),t}};M(I,"instance");let W=I;const j=W.getInstance();class Pe{constructor(t={}){M(this,"config");M(this,"defaultAliases",{paypal:["paypal","pp","pay pal"],zelle:["zelle","zell","zele"],venmo:["venmo","venm","vemo"],cashapp:["cashapp","cash app","cash-app","$cashtag"],wise:["wise","transferwise","transfer wise"],revolut:["revolut","revolt","revol"],skrill:["skrill","skril","skrilla"],neteller:["neteller","neteler","netelr"],perfectmoney:["perfectmoney","perfect money","pm","perfect-money"],webmoney:["webmoney","web money","wm","web-money"],bitcoin:["bitcoin","btc","bit coin"],ethereum:["ethereum","eth","ether"],usdt:["usdt","tether","usd-t"],binance:["binance","bnb","binance pay"],bank:["bank","banking","bank transfer","wire"],credit:["credit","credit card","cc","visa","mastercard"],debit:["debit","debit card","dc"]});this.config={threshold:.7,enableAliases:!0,customAliases:{},...t}}normalizePaymentMethod(t){return t?t.replace(/^CATEGORY_TREE:AIRTM_/i,"").replace(/^E_TRANSFER_/i,"").replace(/^AIRTM_/i,"").replace(/[_-]/g," ").toLowerCase().trim().replace(/\s+/g," "):""}levenshteinDistance(t,o){const r=Array(o.length+1).fill(null).map(()=>Array(t.length+1).fill(null));for(let n=0;n<=t.length;n++)r[0][n]=n;for(let n=0;n<=o.length;n++)r[n][0]=n;for(let n=1;n<=o.length;n++)for(let s=1;s<=t.length;s++){const a=t[s-1]===o[n-1]?0:1;r[n][s]=Math.min(r[n][s-1]+1,r[n-1][s]+1,r[n-1][s-1]+a)}return r[o.length][t.length]}calculateSimilarity(t,o){if(t===o)return 1;if(!t||!o)return 0;const r=Math.max(t.length,o.length);return 1-this.levenshteinDistance(t,o)/r}wordBasedMatch(t,o){const r=t.split(" ").filter(a=>a.length>0),n=o.split(" ").filter(a=>a.length>0);if(n.length===0)return 0;let s=0;for(const a of n)r.some(l=>l.includes(a)||a.includes(l)||this.calculateSimilarity(l,a)>.8)&&s++;return s/n.length}getAliases(t){const o=this.normalizePaymentMethod(t),r=[o];if(this.config.enableAliases){for(const[,n]of Object.entries(this.config.customAliases))if(n.some(s=>this.normalizePaymentMethod(s)===o)){r.push(...n.map(s=>this.normalizePaymentMethod(s)));break}for(const[,n]of Object.entries(this.defaultAliases))if(n.some(s=>this.normalizePaymentMethod(s)===o)){r.push(...n.map(s=>this.normalizePaymentMethod(s)));break}}return[...new Set(r)]}matchPaymentMethod(t,o){const r=this.normalizePaymentMethod(t),n=this.getAliases(o);let s=0,a="";for(const l of n){if(r===l)return{isMatch:!0,score:1,matchedAlias:l};if(r.includes(l)||l.includes(r)){const c=Math.max(l.length/r.length,r.length/l.length)*.9;c>s&&(s=c,a=l)}const w=this.wordBasedMatch(r,l)*.85;w>s&&(s=w,a=l);const d=this.calculateSimilarity(r,l)*.8;d>s&&(s=d,a=l)}return{isMatch:s>=this.config.threshold,score:s,matchedAlias:s>=this.config.threshold?a:void 0}}matchAnyPaymentMethod(t,o){let r={isMatch:!1,score:0};for(const n of o){const s=this.matchPaymentMethod(t,n);if(s.score>r.score&&(r=s),s.score===1)break}return r}updateConfig(t){this.config={...this.config,...t}}getConfig(){return{...this.config}}}let i=G;const Te=new Pe,_={totalOffers:0,newOffers:0,acceptedOffers:0,rejectedOffers:0,averageRate:0,lastUpdate:new Date().toISOString()};let $=new Map,ye=Date.now();const De=2*60*1e3,Ie=60*60*1e3;async function ve(){try{const e=await chrome.storage.local.get("notified_offers");e.notified_offers?($=new Map(Object.entries(e.notified_offers)),console.log(`Loaded ${$.size} notified offers from storage`)):($=new Map,console.log("No previously notified offers found in storage"))}catch(e){console.error("Error loading notified offers:",e),$=new Map}}async function _e(){try{const e=Object.fromEntries($);await chrome.storage.local.set({notified_offers:e})}catch(e){console.error("Error saving notified offers:",e)}}let we=!1;async function Z(){var e,t;if(we){console.log("Background service worker already initialized, skipping...");return}console.log("Airtm Monitor Pro: Background service worker initializing...");try{if(typeof chrome>"u"||!chrome.runtime)throw new Error("Chrome runtime APIs not available");await ve(),await ot(),Ue(),nt(),at(),await it(),Re(),we=!0,console.log("Background service worker initialized successfully"),Ke()}catch(o){console.error("Error initializing background service worker:",o);try{(e=chrome.action)==null||e.setBadgeText({text:"!"}),(t=chrome.action)==null||t.setBadgeBackgroundColor({color:"#ff0000"})}catch(r){console.error("Could not set error badge:",r)}}}function Ue(){chrome.runtime.onMessage.hasListeners()&&chrome.runtime.onMessage.removeListener,chrome.runtime.onMessage.addListener((e,t,o)=>{var r,n,s;if(console.log("Background received message:",e.type,"from:",((r=t.tab)==null?void 0:r.url)||"popup/options"),!e||typeof e.type!="string"){console.error("Invalid message received:",e),o({success:!1,error:"Invalid message format"});return}try{switch(e.type){case"OFFERS_UPDATE":return Le(e.data).then(()=>o({success:!0})).catch(a=>{console.error("Error handling offers update:",a),o({success:!1,error:a.message})}),!0;case"SETTINGS_UPDATE":return rt(e.data).then(()=>o({success:!0})).catch(a=>{console.error("Error handling settings update:",a),o({success:!1,error:a.message})}),!0;case"ACCEPT_OFFER":return H((n=e.data)==null?void 0:n.offerId).then(a=>o({success:!0,result:a})).catch(a=>{console.error("Error handling accept offer:",a),o({success:!1,error:a.message})}),!0;case"REJECT_OFFER":return Se((s=e.data)==null?void 0:s.offerId).then(a=>o({success:!0,result:a})).catch(a=>{console.error("Error handling reject offer:",a),o({success:!1,error:a.message})}),!0;case"OPERATION_DETAILS_UPDATE":return Ze(e.data).then(()=>o({success:!0})).catch(a=>{console.error("Error handling operation details update:",a),o({success:!1,error:a.message})}),!0;case"OPERATION_ACCEPTED":return Ve(e.data).then(()=>o({success:!0})).catch(a=>{console.error("Error handling operation accepted:",a),o({success:!1,error:a.message})}),!0;case"OPERATION_NOT_AVAILABLE":return qe(e.data).then(()=>o({success:!0})).catch(a=>{console.error("Error handling operation not available:",a),o({success:!1,error:a.message})}),!0;case"OPERATION_ACCEPT_ERROR":return Qe(e.data).then(()=>o({success:!0})).catch(a=>{console.error("Error handling operation accept error:",a),o({success:!1,error:a.message})}),!0;case"SEND_TELEGRAM_OPERATION_UPDATE":return tt(e.data).then(()=>o({success:!0})).catch(a=>{console.error("Error sending Telegram operation update:",a),o({success:!1,error:a.message})}),!0;case"OPERATION_DECLINED":return Xe(e.data).then(()=>o({success:!0})).catch(a=>{console.error("Error handling operation declined:",a),o({success:!1,error:a.message})}),!0;case"OPERATION_DECLINE_ERROR":return et(e.data).then(()=>o({success:!0})).catch(a=>{console.error("Error handling operation decline error:",a),o({success:!1,error:a.message})}),!0;case"GET_POPUP_DATA":return ke().then(a=>o({success:!0,data:a})).catch(a=>{console.error("Error handling get popup data:",a),o({success:!1,error:a.message})}),!0;case"PING":console.log("Ping received, responding with alive status"),o({success:!0,message:"Background script is alive",timestamp:new Date().toISOString()});break;default:console.warn("Unknown message type:",e.type),o({success:!1,error:"Unknown message type: "+e.type})}}catch(a){console.error("Error in message listener:",a),o({success:!1,error:"Internal error: "+(a instanceof Error?a.message:"Unknown error")})}}),chrome.runtime.onConnect.addListener(e=>{console.log("Port connected:",e.name),e.onDisconnect.addListener(()=>{console.log("Port disconnected:",e.name),chrome.runtime.lastError&&console.error("Port disconnect error:",chrome.runtime.lastError)})}),chrome.commands&&chrome.commands.onCommand&&chrome.commands.onCommand.addListener(e=>{console.log("Chrome command received:",e),chrome.tabs.query({url:"*://app.airtm.com/peer-transfers/available*"},t=>{t.length>0&&t[0].id?chrome.tabs.sendMessage(t[0].id,{type:"EXECUTE_COMMAND",command:e}).catch(o=>{console.log("Could not send command to content script:",o)}):console.log("No active Airtm tab found for command:",e)})})}async function ke(){try{const t=(await chrome.storage.local.get("current_offers")).current_offers||[];return console.log("Sending popup data:",{offersCount:t.length,settings:i,stats:_}),{offers:t,settings:i,stats:_}}catch(e){throw console.error("Error getting popup data:",e),e}}function Me(){const e=Date.now();if(e-ye<De)return;const t=e-Ie;let o=0;for(const[r,n]of $.entries())n<t&&($.delete(r),o++);ye=e,o>0&&(console.log(`🧹 Cleaned up ${o} old notified offers (older than 1 hour)`),_e())}function Fe(e){return $.has(e)}function xe(e){$.set(e,Date.now()),_e()}async function Le(e){try{const{offers:t,timestamp:o}=e;if(!Array.isArray(t))throw new Error("Invalid offers data");if(console.log("Processing "+t.length+" offers"),await chrome.storage.local.set({current_offers:t}),Me(),!i.monitoring){console.log("Monitoring disabled, skipping offer processing"),Ae(t),_.lastUpdate=o||new Date().toISOString();try{chrome.runtime.sendMessage({type:"OFFERS_PROCESSED",data:{offers:t,stats:_}}).catch(s=>{console.log("Could not notify popup (popup may be closed):",s.message)})}catch(s){console.log("Error sending OFFERS_PROCESSED message:",s)}return}Ae(t);const r=Be(t);console.log(r.length+" offers passed filters");const n=r.filter(s=>!Fe(s.id));console.log(n.length+" new offers (not previously notified)"),n.length>0&&i.soundEnabled&&i.notificationsEnabled&&(console.log("Playing sound notification once for "+n.length+" new offers"),await be());for(const s of n)xe(s.id),await ze(s,!1);_.lastUpdate=o||new Date().toISOString();try{chrome.runtime.sendMessage({type:"OFFERS_PROCESSED",data:{offers:t,stats:_}}).catch(s=>{console.log("Could not notify popup (popup may be closed):",s.message)})}catch(s){console.log("Error sending OFFERS_PROCESSED message:",s)}}catch(t){console.error("Error handling offers update:",t)}}function Be(e){return e.filter(t=>{var r,n,s,a,l,w;const o=typeof t.grossAmount=="string"?parseFloat(t.grossAmount):t.grossAmount;if(o<i.minAmount||o>i.maxAmount)return!1;if(i.preferredCurrencies.length>0){const d=((r=t.currency)==null?void 0:r.symbol)||((n=t.walletCurrency)==null?void 0:n.symbol)||"";if(!i.preferredCurrencies.includes(d))return!1}if(i.paymentMethods.length>0){const d=((l=(a=(s=t.makerPaymentMethod)==null?void 0:s.version)==null?void 0:a.category)==null?void 0:l.translationTag)||"";if(i.fuzzyMatching.enabled){const c=Te.matchAnyPaymentMethod(d,i.paymentMethods);if(c.isMatch)console.log(`Offer ${t.id} matched payment method "${d}" with alias "${c.matchedAlias}" (score: ${c.score.toFixed(2)})`);else return console.log(`Offer ${t.id} filtered out: payment method "${d}" doesn't match any configured methods (best score: ${c.score.toFixed(2)})`),!1}else{let c=d;if(c.startsWith("CATEGORY_TREE:AIRTM_")&&(c=c.replace("CATEGORY_TREE:AIRTM_","")),c.startsWith("E_TRANSFER_")&&(c=c.replace("E_TRANSFER_","")),!i.paymentMethods.some(y=>c.includes(y)))return console.log(`Offer ${t.id} filtered out: payment method "${c}" doesn't match any configured methods (legacy matching)`),!1}}if(i.countries.length>0){const d=((w=t.peer)==null?void 0:w.country)||"";if(!i.countries.includes(d))return!1}if(i.keywords.length>0){const d=JSON.stringify(t).toLowerCase();if(!i.keywords.some(c=>d.includes(c.toLowerCase())))return!1}if(i.blacklistKeywords.length>0){const d=JSON.stringify(t).toLowerCase();if(i.blacklistKeywords.some(c=>d.includes(c.toLowerCase())))return console.log(`Offer ${t.id} filtered out by blacklist keyword`),!1}return!0})}async function Ye(e){try{const t=await chrome.tabs.query({url:"*://app.airtm.com/peer-transfers/available*"});if(t.length===0){console.log("No Airtm tab found, opening new tab");const r=await chrome.tabs.create({url:"https://app.airtm.com/peer-transfers/available",active:!0});if(await new Promise(n=>setTimeout(n,3e3)),r.windowId)try{await chrome.windows.update(r.windowId,{focused:!0,state:"maximized"}),console.log("✅ New window maximized and focused")}catch(n){console.warn("⚠️ Failed to maximize new window:",n);try{await chrome.windows.update(r.windowId,{focused:!0}),console.log("✅ New window focused (without maximizing)")}catch(s){console.error("❌ Failed to focus new window:",s)}}return}const o=t[0];if(o.windowId){try{await chrome.windows.update(o.windowId,{focused:!0,state:"maximized"}),console.log("✅ Window maximized and focused for offer:",e.id)}catch(n){console.warn("⚠️ Failed to maximize window:",n);try{await chrome.windows.update(o.windowId,{focused:!0}),console.log("✅ Window focused (without maximizing) for offer:",e.id)}catch(s){console.error("❌ Failed to focus window:",s)}}let r=!1;for(let n=1;n<=3;n++)try{await chrome.tabs.update(o.id,{active:!0}),console.log(`✅ Tab activated (attempt ${n}) for offer:`,e.id),r=!0;break}catch(s){console.warn(`⚠️ Tab activation attempt ${n} failed:`,s),n<3&&await new Promise(a=>setTimeout(a,500))}r||console.error("❌ Failed to activate tab after 3 attempts"),await new Promise(n=>setTimeout(n,1e3));try{await chrome.tabs.sendMessage(o.id,{type:"FOCUS_OFFER",data:{offerId:e.id}}),console.log("✅ Focus message sent to content script for offer:",e.id)}catch(n){console.log("⚠️ Could not send focus message to content script:",n)}}else console.error("❌ No window ID found for Airtm tab")}catch(t){console.error("❌ Error maximizing window and focusing offer:",t)}}async function ze(e,t=!0){try{console.log("Processing offer "+e.id),await Ye(e),i.notificationsEnabled&&await Ge(e,t),console.log("Checking Telegram settings:",{botToken:i.telegramBotToken?"***SET***":"NOT SET",chatId:i.telegramChatId?"***SET***":"NOT SET"}),i.telegramBotToken&&i.telegramChatId?(console.log("Sending Telegram message for offer:",e.id),await U(e)):console.log("Telegram not configured - skipping message"),i.webhookUrl?(console.log("Sending webhook message for offer:",e.id),await We(e)):console.log("Webhook not configured - skipping webhook"),i.autoAccept&&await H(e.id)}catch(o){console.error("Error processing offer "+e.id+":",o)}}async function Ge(e,t=!0){var o,r;try{j.updateSettings({autoCloseDelay:3e4,playSound:i.soundEnabled&&t}),await j.showNotification(e)}catch(n){console.error("Error sending notification:",n);try{const s=v(e),a="New "+e.operationType+" Offer";let l=`${s.amount} ${s.currency}`;s.conversionNote&&(l+=` ${s.conversionNote}`),await chrome.notifications.create({type:"basic",iconUrl:"icons/icon48.png",title:a,message:l,contextMessage:"From: "+(((o=e.peer)==null?void 0:o.firstName)||"")+" "+(((r=e.peer)==null?void 0:r.lastName)||""),buttons:[{title:"Accept"},{title:"Reject"}]}),i.soundEnabled&&t&&await be()}catch(s){console.error("Fallback notification also failed:",s)}}}async function U(e){try{const t=typeof e=="string"?e:Y(e),o="https://api.telegram.org/bot"+i.telegramBotToken+"/sendMessage";console.log("Sending Telegram message to:",i.telegramChatId),console.log("Message content:",t);const r=await fetch(o,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({chat_id:i.telegramChatId,text:t,parse_mode:"Markdown"})}),n=await r.json();r.ok?console.log("✅ Telegram message sent successfully:",n):console.error("❌ Telegram API error:",n)}catch(t){console.error("❌ Error sending Telegram message:",t)}}async function We(e){try{const t={timestamp:new Date().toISOString(),offer:e,event:"new_offer",metadata:{extensionVersion:"1.0.0",source:"airtm-monitor-pro"}};console.log("Sending webhook to:",i.webhookUrl),console.log("Webhook payload:",t);const o=await fetch(i.webhookUrl,{method:"POST",headers:{"Content-Type":"application/json","User-Agent":"Airtm-Monitor-Pro/1.0.0"},body:JSON.stringify(t)});o.ok?console.log("✅ Webhook sent successfully:",o.status):console.error("❌ Webhook failed with status:",o.status,await o.text())}catch(t){console.error("❌ Error sending webhook:",t)}}function je(e){return{USA:"🇺🇸",US:"🇺🇸",IND:"🇮🇳",IN:"🇮🇳",GBR:"🇬🇧",GB:"🇬🇧",UK:"🇬🇧",CAN:"🇨🇦",CA:"🇨🇦",AUS:"🇦🇺",AU:"🇦🇺",DEU:"🇩🇪",DE:"🇩🇪",GER:"🇩🇪",FRA:"🇫🇷",FR:"🇫🇷",ESP:"🇪🇸",ES:"🇪🇸",ITA:"🇮🇹",IT:"🇮🇹",BRA:"🇧🇷",BR:"🇧🇷",MEX:"🇲🇽",MX:"🇲🇽",ARG:"🇦🇷",AR:"🇦🇷",COL:"🇨🇴",CO:"🇨🇴",VEN:"🇻🇪",VE:"🇻🇪",PER:"🇵🇪",PE:"🇵🇪",CHL:"🇨🇱",CL:"🇨🇱",URY:"🇺🇾",UY:"🇺🇾",ECU:"🇪🇨",EC:"🇪🇨",BOL:"🇧🇴",BO:"🇧🇴",PRY:"🇵🇾",PY:"🇵🇾",CHN:"🇨🇳",CN:"🇨🇳",JPN:"🇯🇵",JP:"🇯🇵",KOR:"🇰🇷",KR:"🇰🇷",RUS:"🇷🇺",RU:"🇷🇺",TUR:"🇹🇷",TR:"🇹🇷",EGY:"🇪🇬",EG:"🇪🇬",ZAF:"🇿🇦",ZA:"🇿🇦",NGA:"🇳🇬",NG:"🇳🇬",KEN:"🇰🇪",KE:"🇰🇪",GHA:"🇬🇭",GH:"🇬🇭",MAR:"🇲🇦",MA:"🇲🇦",TUN:"🇹🇳",TN:"🇹🇳",DZA:"🇩🇿",DZ:"🇩🇿",MOZ:"🇲🇿",MZ:"🇲🇿",PAN:"🇵🇦",PA:"🇵🇦",IDN:"🇮🇩",ID:"🇮🇩",THA:"🇹🇭",TH:"🇹🇭",VNM:"🇻🇳",VN:"🇻🇳",PHL:"🇵🇭",PH:"🇵🇭",MYS:"🇲🇾",MY:"🇲🇾",SGP:"🇸🇬",SG:"🇸🇬",PAK:"🇵🇰",PK:"🇵🇰",BGD:"🇧🇩",BD:"🇧🇩",LKA:"🇱🇰",LK:"🇱🇰",NPL:"🇳🇵",NP:"🇳🇵",AFG:"🇦🇫",AF:"🇦🇫",IRN:"🇮🇷",IR:"🇮🇷",IRQ:"🇮🇶",IQ:"🇮🇶",SAU:"🇸🇦",SA:"🇸🇦",ARE:"🇦🇪",AE:"🇦🇪",QAT:"🇶🇦",QA:"🇶🇦",KWT:"🇰🇼",KW:"🇰🇼",BHR:"🇧🇭",BH:"🇧🇭",OMN:"🇴🇲",OM:"🇴🇲",JOR:"🇯🇴",JO:"🇯🇴",LBN:"🇱🇧",LB:"🇱🇧",SYR:"🇸🇾",SY:"🇸🇾",ISR:"🇮🇱",IL:"🇮🇱",PSE:"🇵🇸",PS:"🇵🇸"}[e.toUpperCase()]||e}function Oe(e){var S,T,R,f,A,E,b,k,F,x,L;const t=e.walletCurrency,o=e.currency,r=e.makerPaymentMethod||e.takerPaymentMethod,n=((S=r==null?void 0:r.categoryId)==null?void 0:S.toLowerCase().includes("usdc"))||((f=(R=(T=r==null?void 0:r.version)==null?void 0:T.category)==null?void 0:R.translationTag)==null?void 0:f.toLowerCase().includes("usdc")),s=((A=e.metadata)==null?void 0:A.walletCurrencyPrecision)||(t==null?void 0:t.precision),a=((E=e.metadata)==null?void 0:E.localCurrencyPrecision)||(o==null?void 0:o.precision),l=s===6||a===6,w=((b=e.metadata)==null?void 0:b.isForThirdPartyPaymentMethod)===!1,d=((k=r==null?void 0:r.categoryId)==null?void 0:k.toLowerCase())||"",c=((L=(x=(F=r==null?void 0:r.version)==null?void 0:F.category)==null?void 0:x.translationTag)==null?void 0:L.toLowerCase())||"",y=d.includes("gift-card")||c.includes("gift_card"),u=d.includes("bank")||c.includes("bank"),g=d.includes("card")||c.includes("card"),p=d.includes("transfer")||c.includes("transfer"),m=y||u||g||p;let h=null,C=null;return e.operationType==="BUY"&&m?(h="withdrawal",C="wallet",{isUSDC:!1,usdcField:C,operationType:h}):e.operationType==="SELL"&&m?(h="deposit",C="wallet",{isUSDC:!1,usdcField:C,operationType:h}):n||l||w?(e.operationType==="SELL"&&s===6?(h="withdrawal",C="wallet"):e.operationType==="BUY"&&a===6?(h="deposit",C="local"):(s===6||a===6)&&(h="exchange",C=s===6?"wallet":"local"),{isUSDC:!0,usdcField:C,operationType:h}):{isUSDC:!1,usdcField:null,operationType:null}}function v(e){var t,o,r,n,s,a,l,w,d;try{const c=Oe(e);let y=((t=e.walletCurrency)==null?void 0:t.symbol)||"$",u=((o=e.currency)==null?void 0:o.symbol)||((r=e.walletCurrency)==null?void 0:r.symbol)||"Unknown";if(c.isUSDC&&(c.usdcField==="wallet"?y="USDC":c.usdcField==="local"&&(u="USDC")),(y===u||!e.currency)&&!c.isUSDC){const A=parseFloat(e.grossAmount||"0");return{amount:O(A),currency:y}}const g=e.rateInfo;if(g&&g.fundsToSendTaker&&g.fundsToReceiveTaker){let A,E,b="";return e.operationType==="BUY"?(A=parseFloat(g.fundsToReceiveTaker),E=parseFloat(g.fundsToSendTaker),c.operationType==="deposit"&&(b="(USDC deposit)")):(A=parseFloat(g.fundsToSendTaker),E=parseFloat(g.fundsToReceiveTaker),c.operationType==="withdrawal"&&(b=`(withdrawal, fees: $${(E-A).toFixed(2)})`)),console.log(`Using rateInfo amounts for ${e.operationType}: ${E} ${y} ↔ ${A} ${u} (from rateInfo)${c.isUSDC?" [USDC operation]":""}`),c.operationType==="withdrawal"?{amount:O(E),currency:y,originalAmount:O(A),originalCurrency:u,exchangeRate:(A/E).toFixed(4),conversionNote:b,operationType:"withdrawal"}:{amount:O(A),currency:u,originalAmount:O(E),originalCurrency:y,exchangeRate:(A/E).toFixed(4),conversionNote:b,operationType:c.operationType}}let p,m=null,h="";e.netAmount&&parseFloat(e.netAmount)>0?p=parseFloat(e.netAmount):p=parseFloat(e.grossAmount||"0");const C=e.metadata,S=e.takerPaymentMethod;if(e.rate?(m=parseFloat(e.rate),h="operation.rate"):(n=e.displayRate)!=null&&n.rate?(m=parseFloat(e.displayRate.rate),h="displayRate.rate"):g&&g.exchangeRate?(m=parseFloat(g.exchangeRate),h="rateInfo.exchangeRate"):(s=C==null?void 0:C.displayRateInfo)!=null&&s.exchangeRate?(m=parseFloat(C.displayRateInfo.exchangeRate),h="metadata.displayRateInfo.exchangeRate"):(a=S==null?void 0:S.rateInfo)!=null&&a.exchangeRate&&(m=parseFloat(S.rateInfo.exchangeRate),h="takerPaymentMethod.rateInfo.exchangeRate"),(!m||m<=0)&&c.isUSDC&&(m=1,h="usdc_parity_default"),!m||m<=0)return{amount:O(p),currency:y,conversionNote:"(rate pending)"};let T;const R=((l=e.displayRate)==null?void 0:l.direction)||"TO_LOCAL_CURRENCY";R==="TO_LOCAL_CURRENCY"?T=p*m:R==="FROM_LOCAL_CURRENCY"?T=p/m:(e.operationType,T=p*m),console.log(`Currency conversion: ${p} ${y} → ${T} ${u} (rate: ${m}, source: ${h}, direction: ${R})${c.isUSDC?" [USDC operation]":""}`);const f={amount:O(T),currency:u,originalAmount:O(p),originalCurrency:y,exchangeRate:m.toString()};return c.isUSDC&&(f.operationType=c.operationType,c.operationType==="withdrawal"?(f.amount=O(p),f.currency=y,f.originalAmount=O(T),f.originalCurrency=u,f.conversionNote=`(withdrawal to ${u})`):c.operationType==="deposit"?f.conversionNote=`(deposit from ${y})`:c.operationType==="exchange"&&(f.conversionNote="(USDC exchange)")),f}catch(c){console.error("Error in currency conversion:",c);const y=parseFloat(e.grossAmount||"0"),u=((w=e.currency)==null?void 0:w.symbol)||((d=e.walletCurrency)==null?void 0:d.symbol)||"Unknown";return{amount:O(y),currency:u,conversionNote:"(conversion error)"}}}function O(e){return e>=1e3?e.toLocaleString("en-US",{minimumFractionDigits:0,maximumFractionDigits:2}):e.toFixed(2).replace(/\.?0+$/,"")}function Ke(){var m,h;console.log("🧪 Testing currency conversion with afteraccepting.txt example...");const e={id:"0d77a20b-f2ab-4dd6-8924-9fd102a37652",hash:"F2ABQP4DD6DK8924",operationType:"SELL",status:"ACCEPTED",isMine:!1,createdAt:"2025-06-12T16:23:51.645Z",updatedAt:"2025-06-12T16:24:00.386Z",grossAmount:"11",netAmount:"10.38",metadata:{isForThirdPartyPaymentMethod:!0,localCurrencyPrecision:2,walletCurrencyPrecision:2},walletCurrency:{symbol:"$",id:"USD",name:"US Dollar",precision:2,__typename:"Catalogs__Currency"},currency:{symbol:"£",id:"EGP",name:"Egyptian Pound",precision:2,__typename:"Catalogs__Currency"},peer:{id:"90e670af-de7f-45ba-8de2-e2698558271d",firstName:"احمد",lastName:"محمد السيد عبدالحافظ",createdAt:"2022-04-23T18:23:27.468Z",country:"EGY",countryInfo:{id:"EGY",__typename:"Catalogs__Country"},numbers:{id:"90e670af-de7f-45ba-8de2-e2698558271d",score:4.98,completedOperations:162,__typename:"Numbers__User"},__typename:"Auth__OperationUser"},rate:"49.57",displayRate:{rate:"47.42396313364055299539",direction:"TO_LOCAL_CURRENCY",__typename:"Operations__DisplayRate"},rateInfo:{fundsToSendTaker:"514.55",fundsToReceiveTaker:"10.85",grossAmount:"545.28",netAmount:"514.55",exchangeRate:"49.5709"},__typename:"Operations__Sell"},t=v(e);console.log("📊 Currency Conversion Test Results:"),console.log(`   Input: ${e.grossAmount} ${(m=e.walletCurrency)==null?void 0:m.symbol} (gross)`),console.log(`   Input: ${e.netAmount} ${(h=e.walletCurrency)==null?void 0:h.symbol} (net)`),console.log("   Expected Local: 514.55 £"),console.log("   Expected Wallet: 10.85 $"),console.log(`   Actual Result: ${t.amount} ${t.currency}`),console.log(`   Original Amount: ${t.originalAmount} ${t.originalCurrency}`),console.log(`   Exchange Rate: ${t.exchangeRate}`),console.log(`   Conversion Note: ${t.conversionNote||"None"}`);const o=514.55,r=parseFloat(t.amount.replace(/,/g,"")),n=Math.abs(r-o)<.01;console.log(`✅ Test Result: ${n?"PASSED":"FAILED"}`),n||console.log(`   Expected: ${o}, Got: ${r}`);const s=Y(e);console.log("📱 Telegram Message:"),console.log(s),console.log(`
🧪 Testing same-currency scenario (USD to USD)...`);const a={id:"055c2bee-d063-4ced-acac-27b623954fa5",hash:"D063YU4CEDVGACAC",operationType:"SELL",status:"CREATED",isMine:!1,createdAt:"2025-06-14T13:19:58.291Z",updatedAt:null,grossAmount:"62.16",netAmount:"58.87",metadata:{isForThirdPartyPaymentMethod:!0,localCurrencyPrecision:2,walletCurrencyPrecision:2},walletCurrency:{symbol:"$",id:"USD",name:"US Dollar",precision:2,__typename:"Catalogs__Currency"},currency:{symbol:"$",id:"USD",name:"US Dollar",precision:2,__typename:"Catalogs__Currency"},peer:{id:"8021567e-1d57-446e-ae65-ffe04f622241",firstName:"IRIS DANIELA",lastName:"S.",createdAt:"2025-03-17T00:20:06.523Z",country:"PAN",countryInfo:{id:"PAN",__typename:"Catalogs__Country"},numbers:{id:"8021567e-1d57-446e-ae65-ffe04f622241",score:4.47,completedOperations:32,__typename:"Numbers__User"},__typename:"Auth__OperationUser"},rate:"1",rateInfo:{fundsToReceiveTaker:"61.23",fundsToSendTaker:"58.86"},displayRate:{direction:"TO_WALLET_CURRENCY",rate:"1.04026503567787971458",__typename:"Operations__DisplayRate"},makerPaymentMethod:{version:{category:{translationTag:"CATEGORY_TREE:AIRTM_MOBILE_YAPPY"}}},__typename:"Operations__Sell"},l=v(a);console.log("📊 Same Currency Test Results:"),console.log("   Expected: 62.16 $ (no conversion)"),console.log(`   Actual Result: ${l.amount} ${l.currency}`),console.log(`   Original Amount: ${l.originalAmount||"None"}`),console.log(`   Conversion Note: ${l.conversionNote||"None"}`);const w=Y(a);console.log("📱 Same Currency Telegram Message:"),console.log(w),console.log(`
🧪 Testing BUY operation scenario (CNY to USD)...`);const d={id:"8d0a8483-3ea0-43b9-9a93-c48f22abe919",hash:"3EA0YE43B9EC9A93",operationType:"BUY",status:"CREATED",isMine:!1,createdAt:"2025-06-14T13:49:43.184Z",updatedAt:null,grossAmount:"278.97",netAmount:"269.84",metadata:{isForThirdPartyPaymentMethod:null,localCurrencyPrecision:2,walletCurrencyPrecision:2},walletCurrency:{symbol:"$",id:"USD",name:"US Dollar",precision:2,__typename:"Catalogs__Currency"},currency:{symbol:"¥",id:"CNY",name:"Chinese Yuan",precision:2,__typename:"Catalogs__Currency"},peer:{id:"d37923bd-23c8-4f2c-99fc-15c665e090ea",firstName:"ALY ADRIANO",lastName:"S.",createdAt:"2025-02-11T17:11:19.413Z",country:"MOZ",countryInfo:{id:"MOZ",__typename:"Catalogs__Country"},numbers:{id:"d37923bd-23c8-4f2c-99fc-15c665e090ea",score:4.28,completedOperations:82,__typename:"Numbers__User"},__typename:"Auth__OperationUser"},rate:"7.1692",rateInfo:{fundsToReceiveTaker:"2000",fundsToSendTaker:"273.75",netAmount:"1934.55",grossAmount:"2000"},displayRate:{direction:"TO_LOCAL_CURRENCY",rate:"7.30593607305936073059",__typename:"Operations__DisplayRate"},makerPaymentMethod:{version:{category:{translationTag:"CATEGORY_TREE:AIRTM_E_TRANSFER_ALIPAY"}}},__typename:"Operations__Buy"},c=v(d);console.log("📊 BUY Operation Test Results:"),console.log("   Expected: 2,000 ¥ (273.75 $)"),console.log(`   Actual Result: ${c.amount} ${c.currency}`),console.log(`   Original Amount: ${c.originalAmount} ${c.originalCurrency}`),console.log(`   Exchange Rate: ${c.exchangeRate}`);const y=Y(d);console.log("📱 BUY Operation Telegram Message:"),console.log(y),console.log(`
🧪 Testing USDC withdrawal scenario...`);const u={id:"withdrawal-001",hash:"WITHDRAWAL001",operationType:"SELL",status:"CREATED",isMine:!0,createdAt:"2025-06-14T15:30:00.000Z",updatedAt:null,grossAmount:"14.23",netAmount:"13.85",metadata:{isForThirdPartyPaymentMethod:!1,walletCurrencyPrecision:6,localCurrencyPrecision:2},walletCurrency:{symbol:"$",id:"USD",name:"US Dollar",precision:6,__typename:"Catalogs__Currency"},currency:{symbol:"$",id:"USD",name:"US Dollar",precision:2,__typename:"Catalogs__Currency"},peer:{id:"withdrawal-peer",firstName:"Bank",lastName:"Withdrawal",createdAt:"2025-01-01T00:00:00.000Z",country:"USA",countryInfo:{id:"USA",__typename:"Catalogs__Country"},numbers:{id:"withdrawal-peer",score:5,completedOperations:1e3,__typename:"Numbers__User"},__typename:"Auth__OperationUser"},rate:"1.0",rateInfo:{fundsToReceiveTaker:"14.23",fundsToSendTaker:"13.50",grossAmount:"14.23",netAmount:"13.85"},displayRate:{direction:"TO_LOCAL_CURRENCY",rate:"1.0",__typename:"Operations__DisplayRate"},makerPaymentMethod:{categoryId:"airtm:bank:withdrawal",version:{category:{translationTag:"CATEGORY_TREE:AIRTM_BANK_WITHDRAWAL"}}},__typename:"Operations__Sell"},g=v(u);console.log("📊 USDC Withdrawal Test Results:"),console.log("   Expected: -14.23 USDC → 13.50 USD (fees: $0.73)"),console.log(`   Actual Result: ${g.amount} ${g.currency}`),console.log(`   Original Amount: ${g.originalAmount} ${g.originalCurrency}`),console.log(`   Operation Type: ${g.operationType}`),console.log(`   Conversion Note: ${g.conversionNote||"None"}`);const p=Y(u);console.log("📱 USDC Withdrawal Telegram Message:"),console.log(p),console.log(`
🧪 Currency conversion tests completed.`)}function Y(e){var A,E,b,k,F,x,L,J,V,q,Q,X,ee,te,oe,re,ne,ae,se,ie,ce,le,ue,de,me,ge,fe,pe,he;const t=v(e),o=Oe(e),r=((k=(b=(E=(A=e.makerPaymentMethod)==null?void 0:A.version)==null?void 0:E.image)==null?void 0:b.urls)==null?void 0:k.logo)||((J=(L=(x=(F=e.makerPaymentMethod)==null?void 0:F.version)==null?void 0:x.image)==null?void 0:L.urls)==null?void 0:J.medium)||"",n=((Q=(q=(V=e.peer)==null?void 0:V.preferences)==null?void 0:q.profile)==null?void 0:Q.avatar)||"",s=((oe=(te=(ee=(X=e.peer)==null?void 0:X.countryInfo)==null?void 0:ee.image)==null?void 0:te.urls)==null?void 0:oe.avatar)||"",a=((((re=e.peer)==null?void 0:re.firstName)||"")+" "+(((ne=e.peer)==null?void 0:ne.lastName)||"")).trim(),l=((se=(ae=e.peer)==null?void 0:ae.numbers)==null?void 0:se.score)||0,w=((ce=(ie=e.peer)==null?void 0:ie.numbers)==null?void 0:ce.completedOperations)||0,d=((le=e.peer)==null?void 0:le.country)||"Unknown",c=je(d);let u=((me=(de=(ue=e.makerPaymentMethod)==null?void 0:ue.version)==null?void 0:de.category)==null?void 0:me.translationTag)||"Unknown";u.startsWith("CATEGORY_TREE:AIRTM_")&&(u=u.replace("CATEGORY_TREE:AIRTM_","")),u.startsWith("E_TRANSFER_")&&(u=u.replace("E_TRANSFER_","")),u.startsWith("GIFT_CARD_")&&(u=u.replace("GIFT_CARD_","")),u=u.replace(/_/g," ").toLowerCase().replace(/\b\w/g,D=>D.toUpperCase());const g=((ge=e.makerPaymentMethod)==null?void 0:ge.categoryId)||"";if(g.includes("gift-card")){const D=g.split(":");if(D.length>2){let N=D[D.length-1].replace(/[-_]/g," ").replace(/\b\w/g,z=>z.toUpperCase());N.toLowerCase()==="ebay"&&(N="eBay"),N.toLowerCase()==="paypal"&&(N="PayPal"),N.toLowerCase()==="amazon"&&(N="Amazon"),u=`${N} Gift Card`}}let p="";o.operationType==="withdrawal"?(p=`-${t.amount} ${t.currency}`,t.originalAmount&&t.originalCurrency&&(p+=` → ${t.originalAmount} ${t.originalCurrency}`)):(p=`${t.amount} ${t.currency}`,t.originalAmount&&t.originalCurrency&&t.originalCurrency!==t.currency&&(p+=` (${t.originalAmount} ${t.originalCurrency})`)),t.conversionNote&&(p+=` ${t.conversionNote}`);let m="";const h=g.includes("gift-card"),C=g.includes("bank")||u.includes("bank"),S=g.includes("card")||u.includes("card");o.operationType==="withdrawal"?h?m="🎁 ":m="💸 ":o.operationType==="deposit"?C?m="🏦 ":S?m="💳 ":m="💰 ":o.isUSDC&&(m="🪙 ");const T=new Date(e.createdAt).toLocaleString("en-US",{month:"short",day:"numeric",year:"numeric",hour:"numeric",minute:"2-digit",hour12:!0}),R=new Date(e.createdAt).toLocaleDateString("en-US",{month:"short",year:"numeric"});let f="";if(r&&(f+=`🖼️ [Service Logo](${r})
`),f+=`📋 ${u}

`,f+=`${m}${p} : ${u}

`,n?f+=`👤 [Avatar](${n}) `:f+="👤 ",f+=`${a} ${l}⭐ (${w} trades)
`,s?f+=`🏳️ [Flag](${s}) ${d} | ${R} | ${T}
`:f+=`${c} ${d} | ${R} | ${T}
`,(fe=e.displayRate)!=null&&fe.rate){const D=parseFloat(e.displayRate.rate).toFixed(4),N=o.usdcField==="wallet"?"USDC":((pe=e.walletCurrency)==null?void 0:pe.symbol)||"$",z=o.usdcField==="local"?"USDC":((he=e.currency)==null?void 0:he.symbol)||"$";f+=`💱 $1 ${N} = $${D} ${z}`}return f}let P=!1,B=null;async function be(){return B||(B=(async()=>{var e;try{if(!P)try{await chrome.offscreen.createDocument({url:"sounds/beep.html",reasons:[chrome.offscreen.Reason.AUDIO_PLAYBACK],justification:"Play notification sound for new offers"}),P=!0}catch(t){if((e=t.message)!=null&&e.includes("Only a single offscreen document may be created"))P=!0;else throw t}try{await chrome.runtime.sendMessage({type:"PLAY_SOUND"})}catch(t){throw console.log("Message to offscreen document failed:",t),P=!1,t}}catch(t){console.error("Error playing notification sound:",t),P=!1}finally{try{P&&(await chrome.offscreen.closeDocument(),P=!1)}catch{P=!1}B=null}})(),B)}async function H(e){try{if(!Ce())throw console.warn("⚠️ Extension context invalid during offer acceptance, cannot proceed"),new Error("Extension context is invalid. Please reload the extension.");const t=await chrome.tabs.query({url:"*://app.airtm.com/peer-transfers/available*"});if(t.length===0)throw new Error("Airtm tab not found");const o=await chrome.tabs.sendMessage(t[0].id,{type:"ACCEPT_OFFER",data:{offerId:e}});if(o!=null&&o.success)return _.acceptedOffers++,console.log("Offer "+e+" accepted successfully"),He(e,t[0].id),!0;throw new Error((o==null?void 0:o.error)||"Failed to accept offer")}catch(t){if(console.error("Error accepting offer "+e+":",t),Ee(t)&&(console.warn("⚠️ Connection error detected, likely due to extension context invalidation"),i.telegramBotToken&&i.telegramChatId))try{await U(`⚠️ Extension Context Issue

Failed to accept offer ${e}
Reason: Extension context lost

Please reload the extension or refresh the page.`)}catch(o){console.error("Failed to send Telegram notification about context issue:",o)}throw t}}async function Ze(e){try{console.log("📋 Processing operation details update:",e);const{operation:t,timestamp:o,source:r}=e;if(!t||!t.id){console.warn("Invalid operation data received");return}await chrome.storage.local.set({[`operation_${t.id}`]:{...t,lastUpdated:o,source:r}}),i.telegramBotToken&&i.telegramChatId&&await Je(t),console.log(`✅ Operation ${t.id} details updated - Status: ${t.status}`)}catch(t){console.error("❌ Error handling operation details update:",t)}}function He(e,t){console.log(`🔍 Starting URL monitoring for accepted offer: ${e}`);let o=0;const r=30,n=setInterval(async()=>{try{o++;const a=(await chrome.tabs.get(t)).url;if(console.log(`🔍 URL check ${o}/${r}: ${a}`),a&&/https:\/\/app\.airtm\.com\/operations\/[A-Z0-9]+/.test(a)){console.log("✅ Operation URL detected:",a);const w=a.split("/operations/")[1];i.telegramBotToken&&i.telegramChatId&&await U(`🎯 Offer ${e} accepted successfully!
📋 Operation ID: ${w}
🔗 URL: ${a}
⏰ Status: Accepted`),await chrome.storage.local.set({[`accepted_operation_${e}`]:{operationId:w,url:a,timestamp:new Date().toISOString(),status:"accepted"}}),clearInterval(n);return}o>=r&&(console.log("⚠️ Operation URL not detected within timeout"),i.telegramBotToken&&i.telegramChatId&&await U(`❌ Offer ${e} acceptance failed or timed out
⏰ Status: Not Available`),await chrome.storage.local.set({[`failed_operation_${e}`]:{timestamp:new Date().toISOString(),status:"not_available",reason:"url_not_detected"}}),clearInterval(n))}catch(s){console.error("❌ Error during URL monitoring:",s),clearInterval(n)}},100)}async function Je(e){var t;try{const o=(t=e.metadata)==null?void 0:t.displayRateInfo,r={id:e.id||"",hash:e.hash||"",operationType:e.operationType||"UNKNOWN",status:e.status||"UNKNOWN",isMine:e.isMine||!1,createdAt:e.createdAt||"",updatedAt:e.updatedAt||null,grossAmount:e.grossAmount||"0",netAmount:e.netAmount||"0",metadata:{...e.metadata,displayRateInfo:o},walletCurrency:e.walletCurrency||{symbol:"$"},peer:e.secondParty||e.peer||{firstName:"Unknown",lastName:""},rate:e.rate,displayRate:e.displayRate,rateInfo:{...e.rateInfo},currency:e.currency,makerPaymentMethod:e.makerPaymentMethod,__typename:e.__typename||"Operations__Unknown"},n=v(r),s=e.secondParty?`${e.secondParty.firstName||""} ${e.secondParty.lastName||""}`.trim():e.peer?`${e.peer.firstName||""} ${e.peer.lastName||""}`.trim():"Unknown";let a=`${n.amount} ${n.currency}`;n.conversionNote&&(a+=` ${n.conversionNote}`),n.originalAmount&&n.originalCurrency&&n.originalCurrency!==n.currency&&(a+=` (${n.originalAmount} ${n.originalCurrency})`);const l=`📋 Operation Update
🆔 ID: ${e.hash||e.id}
📊 Status: ${e.status}
💰 Amount: ${a}
🔄 Type: ${e.operationType}
👤 Partner: ${s}
⏰ Updated: ${new Date().toLocaleString()}`;await U(l)}catch(o){console.error("❌ Error sending Telegram operation update:",o)}}async function Ve(e){try{console.log("✅ Processing operation acceptance:",e),await chrome.storage.local.set({[`accepted_operation_${e.operationId}`]:{...e,processedAt:new Date().toISOString()}}),_.acceptedOffers++,console.log("✅ Operation acceptance processed successfully")}catch(t){console.error("❌ Error handling operation accepted:",t)}}async function qe(e){try{console.log("🚫 Processing operation not available:",e),await chrome.storage.local.set({[`unavailable_operation_${e.operationId||"unknown"}`]:{...e,processedAt:new Date().toISOString()}}),console.log("🚫 Operation not available processed successfully")}catch(t){console.error("❌ Error handling operation not available:",t)}}async function Qe(e){try{console.log("⚠️ Processing operation accept error:",e),await chrome.storage.local.set({[`error_operation_${Date.now()}`]:{...e,processedAt:new Date().toISOString()}}),console.log("⚠️ Operation accept error processed successfully")}catch(t){console.error("❌ Error handling operation accept error:",t)}}async function Xe(e){try{console.log("🚫 Processing operation decline:",e),await chrome.storage.local.set({[`declined_operation_${Date.now()}`]:{...e,processedAt:new Date().toISOString()}}),_.rejectedOffers++,console.log("🚫 Operation decline processed successfully")}catch(t){console.error("❌ Error handling operation declined:",t)}}async function et(e){try{console.log("⚠️ Processing operation decline error:",e),await chrome.storage.local.set({[`decline_error_operation_${Date.now()}`]:{...e,processedAt:new Date().toISOString()}}),console.log("⚠️ Operation decline error processed successfully")}catch(t){console.error("❌ Error handling operation decline error:",t)}}async function tt(e){try{console.log("📱 Sending Telegram operation status update:",e);const{operationId:t,status:o}=e;let r;o==="accepted"?r=`✅ *Operation Accepted*

🆔 Operation ID: ${t}
📊 Status: Successfully accepted
⏰ Time: ${new Date().toLocaleString()}

🎉 Great! The operation was accepted successfully.`:o==="Not Available"?r=`🚫 *Operation Not Available*

🆔 Operation ID: ${t}
📊 Status: No longer available
⏰ Time: ${new Date().toLocaleString()}

😔 The operation was taken by someone else or expired.`:r=`📋 *Operation Update*

🆔 Operation ID: ${t}
📊 Status: ${o}
⏰ Time: ${new Date().toLocaleString()}`,await U(r),console.log("📱 Telegram operation status update sent successfully")}catch(t){console.error("❌ Error sending Telegram operation status update:",t)}}async function Se(e){try{if(!Ce())throw console.warn("⚠️ Extension context invalid during offer rejection, cannot proceed"),new Error("Extension context is invalid. Please reload the extension.");const t=await chrome.tabs.query({url:"*://app.airtm.com/peer-transfers/available*"});if(t.length===0)throw new Error("Airtm tab not found");const o=await chrome.tabs.sendMessage(t[0].id,{type:"REJECT_OFFER",data:{offerId:e}});if(o!=null&&o.success)return _.rejectedOffers++,console.log("Offer "+e+" rejected successfully"),!0;throw new Error((o==null?void 0:o.error)||"Failed to reject offer")}catch(t){if(console.error("Error rejecting offer "+e+":",t),Ee(t)&&(console.warn("⚠️ Connection error detected, likely due to extension context invalidation"),i.telegramBotToken&&i.telegramChatId))try{await U(`⚠️ Extension Context Issue

Failed to reject offer ${e}
Reason: Extension context lost

Please reload the extension or refresh the page.`)}catch(o){console.error("Failed to send Telegram notification about context issue:",o)}throw t}}function Ae(e){_.totalOffers=e.length,_.newOffers=e.filter(t=>{const o=new Date(t.createdAt),r=new Date(Date.now()-5*60*1e3);return o>r}).length,_.averageRate=0}async function ot(){try{const e=await chrome.storage.sync.get("settings");i={...G,...e.settings},i.monitoring=!0,K(),await chrome.storage.sync.set({settings:i}),console.log("Settings loaded and monitoring enabled:",i)}catch(e){console.error("Error loading settings:",e),i={...G,monitoring:!0},K()}}async function rt(e){try{i={...i,...e},e.fuzzyMatching&&K(),await chrome.storage.sync.set({settings:i}),console.log("Settings updated:",i),Re()}catch(t){throw console.error("Error updating settings:",t),t}}function Re(){i.monitoring?(chrome.action.setBadgeText({text:"ON"}),chrome.action.setBadgeBackgroundColor({color:"#10b981"})):(chrome.action.setBadgeText({text:"OFF"}),chrome.action.setBadgeBackgroundColor({color:"#6b7280"}))}function K(){try{const e=i.fuzzyMatching||{enabled:!1,threshold:.8,enableAliases:!0,customAliases:{}};Te.updateConfig({threshold:e.threshold,enableAliases:e.enableAliases,customAliases:e.customAliases}),console.log("Fuzzy matcher configuration updated:",e)}catch(e){console.error("Error updating fuzzy matcher configuration:",e)}}function nt(){chrome.alarms.onAlarm.addListener(e=>{switch(console.log("Alarm triggered:",e.name),e.name){case"monitoring-check":break;case"cleanup-storage":st();break}}),chrome.alarms.create("cleanup-storage",{periodInMinutes:60})}function at(){chrome.notifications.onButtonClicked.addListener(async(e,t)=>{try{const o=e.match(/offer_(.+)_\d+/),r=o?o[1]:e;t===0?await H(r):t===1&&await Se(r),chrome.notifications.clear(e)}catch(o){console.error("Error handling notification button click:",o)}}),chrome.runtime.onMessage.addListener((e,t,o)=>{(e.type==="ACCEPT_OFFER"||e.type==="REJECT_OFFER"||e.type==="IGNORE_OFFER")&&j.handleMessage(e,t)})}async function st(){try{console.log("Cleaning up storage...");const t=(await chrome.storage.local.get(["airtm_offers_timestamp"])).airtm_offers_timestamp;if(t){const o=Date.now()-new Date(t).getTime(),r=24*60*60*1e3;o>r&&(await chrome.storage.local.remove(["airtm_offers","airtm_offers_timestamp"]),console.log("Old offers cleaned up"))}}catch(e){console.error("Error cleaning up storage:",e)}}async function it(){try{console.log("🔍 Checking for existing Airtm tabs...");const e=await chrome.tabs.query({url:"*://app.airtm.com/peer-transfers/available*"});if(e.length===0){console.log("📭 No existing Airtm tabs found");return}console.log(`🎯 Found ${e.length} existing Airtm tab(s)`),console.log("ℹ️ Content scripts will be automatically injected by Chrome based on manifest.json");for(const t of e)t.id&&t.url&&console.log(`📋 Found Airtm tab ${t.id}: ${t.url}`)}catch(e){console.error("❌ Error checking existing tabs:",e)}}function ct(){const e=()=>{var t;try{(t=chrome==null?void 0:chrome.runtime)!=null&&t.getPlatformInfo&&chrome.runtime.getPlatformInfo(()=>{chrome.runtime.lastError&&console.warn("Keep alive error:",chrome.runtime.lastError)})}catch(o){console.warn("Keep alive failed:",o)}};e(),setInterval(e,2e4)}Z().then(()=>{ct()});chrome.runtime.onStartup.addListener(()=>{console.log("Service worker starting up..."),Z()});chrome.runtime.onInstalled.addListener(e=>{console.log("Extension installed/updated:",e.reason),Z()});
