import{c as i,j as e,R as q,X as T,r as w,C as W,P as Y,a as se}from"./globals-983b80eb.js";import{A as te}from"./AirTMPaymentMethodMatcher-d2f6c65f.js";/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ae=[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]],re=i("activity",ae);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const le=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m4.9 4.9 14.2 14.2",key:"1m5liu"}]],ne=i("ban",le);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const oe=[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M17 17H4a1 1 0 0 1-.74-1.673C4.59 13.956 6 12.499 6 8a6 6 0 0 1 .258-1.742",key:"178tsu"}],["path",{d:"m2 2 20 20",key:"1ooewy"}],["path",{d:"M8.668 3.01A6 6 0 0 1 18 8c0 2.687.77 4.653 1.707 6.05",key:"1hqiys"}]],ie=i("bell-off",oe);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ce=[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]],X=i("bell",ce);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const de=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],xe=i("chevron-down",de);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const me=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],he=i("chevron-up",me);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ue=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]],be=i("circle-alert",ue);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pe=[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]],fe=i("circle-check-big",pe);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ge=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]],ye=i("circle-x",ge);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const je=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]],Q=i("clock",je);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const we=[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],Ne=i("eye",we);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ve=[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]],L=i("funnel",ve);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ke=[["rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",key:"zuxfzm"}],["rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",key:"1okwgv"}]],Ce=i("pause",ke);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ae=[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]],_e=i("play",Ae);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Me=[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]],J=i("search",Me);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ze=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],Ee=i("settings",ze);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Se=[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]],Z=i("shield",Se);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $e=[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]],Te=i("star",$e);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Le=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]],Pe=i("trash-2",Le);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Re=[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]],Fe=i("trending-down",Re);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const De=[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]],R=i("trending-up",De);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Oe=[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["path",{d:"M16 9a5 5 0 0 1 0 6",key:"1q6k2b"}],["path",{d:"M19.364 18.364a9 9 0 0 0 0-12.728",key:"ijwkga"}]],Ke=i("volume-2",Oe);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ie=[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["line",{x1:"22",x2:"16",y1:"9",y2:"15",key:"1ewh16"}],["line",{x1:"16",x2:"22",y1:"9",y2:"15",key:"5ykzw1"}]],Be=i("volume-x",Ie),Ge=({offer:s,isSelected:l,onClick:m,isBlacklisted:f=!1})=>{var M,S,$,z,E,x,j,F,D,O,K,I,B,G;const a=((M=s.currency)==null?void 0:M.symbol)||((S=s.walletCurrency)==null?void 0:S.symbol)||"USD",b=s.operationType||"BUY",p=typeof s.grossAmount=="string"?parseFloat(s.grossAmount)||0:s.grossAmount||0,h=typeof s.netAmount=="string"?parseFloat(s.netAmount)||0:s.netAmount||0,d=s.peer?`${s.peer.firstName} ${s.peer.lastName}`:"Unknown User",o=((z=($=s.peer)==null?void 0:$.numbers)==null?void 0:z.score)||0,u=((x=(E=s.peer)==null?void 0:E.numbers)==null?void 0:x.completedOperations)||0,g=((F=(j=s.peer)==null?void 0:j.securityHub)==null?void 0:F.facialVerified)&&((O=(D=s.peer)==null?void 0:D.securityHub)==null?void 0:O.documentVerified)||!1;let c=((B=(I=(K=s.makerPaymentMethod)==null?void 0:K.version)==null?void 0:I.category)==null?void 0:B.translationTag)||"Unknown";c.startsWith("CATEGORY_TREE:AIRTM_")&&(c=c.replace("CATEGORY_TREE:AIRTM_","")),c.startsWith("E_TRANSFER_")&&(c=c.replace("E_TRANSFER_","")),c=c.replace(/_/g," ").toLowerCase().replace(/\b\w/g,A=>A.toUpperCase());const N=s.status||"CREATED",k=s.createdAt?new Date(s.createdAt):new Date,n=Date.now()-k.getTime()<5*60*1e3,C=A=>{const P=new Date().getTime()-A.getTime(),H=Math.floor(P/(1e3*60)),V=Math.floor(P/(1e3*60*60)),U=Math.floor(P/(1e3*60*60*24));return U>0?`${U}d ago`:V>0?`${V}h ago`:H>0?`${H}m ago`:"Just now"},r=(A=>{switch(A){case"BUY":return{color:"text-emerald-400",bgColor:"bg-emerald-500/10",borderColor:"border-emerald-500/20",icon:R,label:"Buy"};case"SELL":return{color:"text-red-400",bgColor:"bg-red-500/10",borderColor:"border-red-500/20",icon:Fe,label:"Sell"};default:return{color:"text-blue-400",bgColor:"bg-blue-500/10",borderColor:"border-blue-500/20",icon:R,label:A}}})(b),t=r.icon,v=A=>{switch(A){case"CREATED":return"text-emerald-400";case"PAUSED":return"text-yellow-400";case"INACTIVE":return"text-red-400";default:return"text-gray-400"}};return e.jsxs("div",{onClick:m,className:`
        relative rounded-xl border transition-all duration-200 cursor-pointer
        hover:scale-[1.02] hover:shadow-lg hover:shadow-black/20
        ${l?"border-emerald-500 bg-gradient-to-br from-emerald-500/10 to-gray-800 shadow-lg shadow-emerald-500/20":f?"border-red-500/50 bg-gradient-to-br from-red-500/5 to-gray-800 opacity-60":"border-gray-700 bg-gradient-to-br from-gray-800 to-gray-900 hover:border-gray-600"}
        ${n?"ring-2 ring-emerald-500/30":""}
      `,children:[f&&e.jsx("div",{className:"absolute top-2 right-2 z-10",children:e.jsxs("div",{className:"flex items-center space-x-1 bg-red-500/20 border border-red-500/30 rounded-lg px-2 py-1",children:[e.jsx(ne,{className:"w-3 h-3 text-red-400"}),e.jsx("span",{className:"text-xs text-red-400 font-medium",children:"Blocked"})]})}),n&&!f&&e.jsx("div",{className:"absolute top-2 right-2 z-10",children:e.jsxs("div",{className:"flex items-center space-x-1 bg-emerald-500/20 border border-emerald-500/30 rounded-lg px-2 py-1",children:[e.jsx("div",{className:"w-2 h-2 bg-emerald-400 rounded-full animate-pulse"}),e.jsx("span",{className:"text-xs text-emerald-400 font-medium",children:"New"})]})}),e.jsxs("div",{className:"p-4",children:[e.jsxs("div",{className:"flex items-start justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:`${r.bgColor} ${r.borderColor} border rounded-lg p-2`,children:e.jsx(t,{className:`w-4 h-4 ${r.color}`})}),e.jsx("div",{children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-lg font-bold text-white",children:a}),e.jsx("span",{className:`text-xs px-2 py-1 rounded-full ${r.bgColor} ${r.color} font-medium`,children:r.label})]})})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:`text-xs font-medium ${v(N)}`,children:N}),e.jsxs("div",{className:"text-xs text-gray-500 flex items-center space-x-1 mt-1",children:[e.jsx(Q,{className:"w-3 h-3"}),e.jsx("span",{children:C(k)})]})]})]}),e.jsxs("div",{className:"mb-3",children:[e.jsx("div",{className:"text-sm text-gray-400 mb-1",children:"Amount Range"}),e.jsxs("div",{className:"text-white font-semibold",children:[h.toLocaleString()," - ",p.toLocaleString()," ",a]}),p>0&&e.jsxs("div",{className:"text-xs text-gray-500",children:["Available: ",p.toLocaleString()," ",a]})]}),e.jsx("div",{className:"flex items-center justify-between mb-3",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:"w-8 h-8 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-full flex items-center justify-center",children:e.jsx("span",{className:"text-white text-xs font-bold",children:d.charAt(0).toUpperCase()})}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx("span",{className:"text-sm font-medium text-white",children:d}),g&&e.jsx(Z,{className:"w-3 h-3 text-blue-400"})]}),e.jsxs("div",{className:"flex items-center space-x-2 text-xs text-gray-400",children:[e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx(Te,{className:"w-3 h-3 text-yellow-400"}),e.jsx("span",{children:o.toFixed(1)})]}),e.jsx("span",{children:"•"}),e.jsxs("span",{children:[u," trades"]})]})]})]})}),e.jsxs("div",{className:"mb-3",children:[e.jsx("div",{className:"text-sm text-gray-400 mb-1",children:"Payment Method"}),e.jsx("div",{className:"text-white font-medium",children:c})]}),e.jsxs("div",{className:"flex items-center justify-between pt-3 border-t border-gray-700",children:[e.jsxs("div",{className:"text-xs text-gray-500",children:["ID: ",(G=s.hash)==null?void 0:G.substring(0,8),"..."]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[N==="CREATED"&&e.jsx("div",{className:"w-2 h-2 bg-emerald-400 rounded-full animate-pulse"}),e.jsx("span",{className:"text-xs text-gray-400",children:"Click to select"})]})]})]})]})},He=(s,l,m)=>{if(!l.length)return s;const f=new te({threshold:m.fuzzyMatching.threshold,enableAliases:m.fuzzyMatching.enableAliases,customAliases:m.fuzzyMatching.customAliases});return s.filter(a=>{var p,h,d;const b=((d=(h=(p=a.makerPaymentMethod)==null?void 0:p.version)==null?void 0:h.category)==null?void 0:d.translationTag)||"";if(m.fuzzyMatching.enabled)return!f.matchAnyPaymentMethod(b,l).isMatch;{let o=b.toLowerCase();return o.startsWith("category_tree:airtm_")&&(o=o.replace("category_tree:airtm_","")),o.startsWith("e_transfer_")&&(o=o.replace("e_transfer_","")),!l.some(u=>o.includes(u.toLowerCase()))}})},Ve=({offers:s,selectedOffer:l,onOfferSelect:m,settings:f})=>{var k;const[a,b]=q.useState(""),[p,h]=q.useState(!1),d=He(s,f.blacklistKeywords||[],f),u=d.filter(n=>{var _,r,t,v,M;return a?[n.peer?`${n.peer.firstName} ${n.peer.lastName}`:"",((t=(r=(_=n.makerPaymentMethod)==null?void 0:_.version)==null?void 0:r.category)==null?void 0:t.translationTag)||"",n.hash||"",n.status||"",((v=n.currency)==null?void 0:v.symbol)||((M=n.walletCurrency)==null?void 0:M.symbol)||"",""].join(" ").toLowerCase().includes(a.toLowerCase()):!0}),g=((k=f.blacklistKeywords)==null?void 0:k.length)>0||a.length>0,y=s.length-d.length,c=()=>{b("")},N=()=>{h(!p)};return e.jsxs("div",{className:"flex-1 flex flex-col bg-white/5 backdrop-blur-sm overflow-hidden rounded-xl border border-white/10",children:[e.jsxs("div",{className:"bg-white/10 border-b border-white/10 px-6 py-4 space-y-4",children:[e.jsxs("div",{className:"relative",children:[e.jsx(J,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search offers...",value:a,onChange:n=>b(n.target.value),className:`
              w-full pl-10 pr-10 py-3 bg-white/10 border border-white/20 rounded-xl
              text-white placeholder-gray-400 text-sm backdrop-blur-sm
              focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500
              transition-all duration-200
            `}),a&&e.jsx("button",{onClick:c,className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors",children:e.jsx(T,{className:"w-4 h-4"})})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[y>0&&e.jsxs("div",{className:"flex items-center space-x-2 bg-red-500/20 border border-red-500/30 rounded-xl px-4 py-2 backdrop-blur-sm",children:[e.jsx(L,{className:"w-3 h-3 text-red-400"}),e.jsxs("span",{className:"text-xs text-red-400 font-medium",children:[y," blocked"]}),e.jsx("button",{onClick:N,className:"text-xs text-red-300 hover:text-red-200 underline",children:p?"Hide":"Show"})]}),a&&e.jsxs("div",{className:"flex items-center space-x-2 bg-blue-500/20 border border-blue-500/30 rounded-xl px-4 py-2 backdrop-blur-sm",children:[e.jsx(J,{className:"w-3 h-3 text-blue-400"}),e.jsxs("span",{className:"text-xs text-blue-400 font-medium",children:[u.length," found"]})]})]}),e.jsxs("div",{className:"text-xs text-gray-400",children:[u.length," of ",s.length," offers"]})]})]}),e.jsx("div",{className:"flex-1 overflow-y-auto custom-scrollbar-modern",children:u.length===0?e.jsxs("div",{className:"flex flex-col items-center justify-center h-full text-center p-8",children:[e.jsx(be,{className:"w-12 h-12 text-gray-500 mb-4"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-300 mb-2",children:s.length===0?"No offers available":"No matching offers"}),e.jsx("p",{className:"text-sm text-gray-500 max-w-sm",children:s.length===0?"Start monitoring to see offers appear here":g?"Try adjusting your search or filter criteria":"Check back later for new offers"}),(a||y>0)&&e.jsx("button",{onClick:()=>{b(""),h(!1)},className:"mt-4 px-6 py-3 bg-emerald-500 hover:bg-emerald-600 text-white text-sm rounded-xl transition-colors border border-emerald-400/30",children:"Clear filters"})]}):e.jsx("div",{className:"p-6 space-y-4",children:(p?s.filter(n=>!d.includes(n)):u).map((n,C)=>e.jsx("div",{className:"animate-fade-in",style:{animationDelay:`${C*50}ms`},children:e.jsx(Ge,{offer:n,isSelected:(l==null?void 0:l.hash)===n.hash,onClick:()=>m(n),isBlacklisted:p&&!d.includes(n)})},n.hash))})}),u.length>0&&e.jsx("div",{className:"bg-white/10 border-t border-white/10 px-6 py-3 backdrop-blur-sm",children:e.jsxs("div",{className:"flex items-center justify-between text-xs text-gray-400",children:[e.jsxs("span",{children:[g?`Filtered: ${u.length}`:`Total: ${s.length}`," offers"]}),e.jsx("span",{children:l?"Offer selected":"Select an offer to view details"})]})})]})},Ue=({settings:s,onSettingsUpdate:l,selectedOffer:m,onAcceptOffer:f,onRejectOffer:a})=>{var z,E;const[b,p]=w.useState(!1),[h,d]=w.useState(""),[o,u]=w.useState(!1),[g,y]=w.useState([]),[c,N]=w.useState(!1),k=()=>{l({...s,monitoring:!s.monitoring})},n=()=>{l({...s,autoAccept:!s.autoAccept})},C=()=>{l({...s,notificationsEnabled:!s.notificationsEnabled})},_=()=>{l({...s,soundEnabled:!s.soundEnabled})},r=()=>{h.trim()&&!s.blacklistKeywords.includes(h.trim())&&(c?(g.includes(h.trim())||y([...g,h.trim()]),d("")):(l({...s,blacklistKeywords:[...s.blacklistKeywords,h.trim()]}),d(""),u(!1)))},t=()=>{if(g.length>0){const x=g.filter(j=>!s.blacklistKeywords.includes(j));l({...s,blacklistKeywords:[...s.blacklistKeywords,...x]}),y([]),N(!1),d(""),u(!1)}},v=()=>{y([]),N(!1),d(""),u(!1)},M=x=>{y(g.filter(j=>j!==x))},S=x=>{l({...s,blacklistKeywords:s.blacklistKeywords.filter(j=>j!==x)})},$=x=>{x.key==="Enter"?c?h.trim()?r():t():h.trim()&&(N(!0),r()):x.key==="Escape"&&(c?v():(d(""),u(!1)))};return console.log("ControlPanel rendering with settings:",s),e.jsxs("div",{className:"bg-white/5 backdrop-blur-sm border-t border-white/10 rounded-t-xl",style:{minHeight:"200px",border:"2px solid red"},children:[e.jsxs("div",{className:"p-6",children:[m&&e.jsxs("div",{className:"mb-6 p-4 bg-gradient-to-r from-emerald-500/20 to-blue-500/20 border border-emerald-400/30 rounded-xl backdrop-blur-sm",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:"w-2 h-2 bg-emerald-400 rounded-full animate-pulse"}),e.jsx("span",{className:"text-sm font-medium text-white",children:"Selected Offer"})]}),e.jsxs("div",{className:"text-xs text-gray-400",children:[((z=m.currency)==null?void 0:z.symbol)||((E=m.walletCurrency)==null?void 0:E.symbol)||"N/A"," • ",m.operationType]})]}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsxs("button",{onClick:()=>f(m.hash),className:"flex-1 flex items-center justify-center space-x-2 bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 font-medium",children:[e.jsx(W,{className:"w-4 h-4"}),e.jsx("span",{children:"Accept"})]}),e.jsxs("button",{onClick:()=>a(m.hash),className:"flex-1 flex items-center justify-center space-x-2 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 font-medium",children:[e.jsx(T,{className:"w-4 h-4"}),e.jsx("span",{children:"Reject"})]})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-6",children:[e.jsxs("button",{onClick:k,className:`flex items-center justify-center space-x-2 p-4 rounded-xl transition-all duration-200 font-medium ${s.monitoring?"bg-emerald-500 hover:bg-emerald-600 text-white shadow-lg shadow-emerald-500/25 border border-emerald-400/30":"bg-white/10 hover:bg-white/20 text-gray-300 border border-white/20"}`,children:[s.monitoring?e.jsx(Ce,{className:"w-4 h-4"}):e.jsx(_e,{className:"w-4 h-4"}),e.jsx("span",{className:"text-sm",children:s.monitoring?"Pause":"Start"})]}),e.jsxs("button",{onClick:n,className:`flex items-center justify-center space-x-2 p-4 rounded-xl transition-all duration-200 font-medium ${s.autoAccept?"bg-blue-500 hover:bg-blue-600 text-white shadow-lg shadow-blue-500/25 border border-blue-400/30":"bg-white/10 hover:bg-white/20 text-gray-300 border border-white/20"}`,children:[e.jsx(Z,{className:`w-4 h-4 ${s.autoAccept?"animate-pulse":""}`}),e.jsx("span",{className:"text-sm",children:"Auto"})]})]}),e.jsxs("button",{onClick:()=>p(!b),className:"w-full flex items-center justify-between p-4 bg-white/10 hover:bg-white/20 rounded-xl transition-all duration-200 text-white border border-white/20",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(Ee,{className:"w-4 h-4"}),e.jsx("span",{className:"font-medium",children:"Settings"})]}),b?e.jsx(he,{className:"w-4 h-4"}):e.jsx(xe,{className:"w-4 h-4"})]})]}),b&&e.jsxs("div",{className:"border-t border-white/10 p-6 space-y-6 animate-fade-in",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsxs("h3",{className:"text-sm font-semibold text-white flex items-center space-x-2",children:[e.jsx(X,{className:"w-4 h-4"}),e.jsx("span",{children:"Notifications"})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("button",{onClick:C,className:`flex items-center justify-center space-x-2 p-3 rounded-xl transition-all duration-200 text-sm ${s.notificationsEnabled?"bg-yellow-500 hover:bg-yellow-600 text-white border border-yellow-400/30":"bg-white/10 hover:bg-white/20 text-gray-300 border border-white/20"}`,children:[s.notificationsEnabled?e.jsx(X,{className:"w-4 h-4"}):e.jsx(ie,{className:"w-4 h-4"}),e.jsx("span",{children:"Alerts"})]}),e.jsxs("button",{onClick:_,className:`flex items-center justify-center space-x-2 p-3 rounded-xl transition-all duration-200 text-sm ${s.soundEnabled?"bg-purple-500 hover:bg-purple-600 text-white border border-purple-400/30":"bg-white/10 hover:bg-white/20 text-gray-300 border border-white/20"}`,children:[s.soundEnabled?e.jsx(Ke,{className:"w-4 h-4"}):e.jsx(Be,{className:"w-4 h-4"}),e.jsx("span",{children:"Sound"})]})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("h3",{className:"text-sm font-semibold text-white flex items-center space-x-2",children:[e.jsx(L,{className:"w-4 h-4"}),e.jsxs("span",{children:["Blacklist (",s.blacklistKeywords.length,")"]})]}),e.jsxs("button",{onClick:()=>u(!o),className:"flex items-center space-x-1 text-emerald-400 hover:text-emerald-300 transition-colors duration-200 text-sm",children:[e.jsx(Y,{className:"w-4 h-4"}),e.jsx("span",{children:"Add"})]})]}),o&&e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex space-x-2",children:[e.jsx("input",{type:"text",value:h,onChange:x=>d(x.target.value),onKeyDown:$,placeholder:c?"Add another keyword or press Enter to finish...":"Enter keyword...",className:`flex-1 bg-white/10 border rounded-xl px-4 py-3 text-white text-sm focus:outline-none focus:ring-2 focus:border-emerald-500 backdrop-blur-sm transition-all duration-200 ${c?"border-yellow-400/50 focus:ring-yellow-500":"border-white/20 focus:ring-emerald-500"}`,autoFocus:!0}),e.jsx("button",{onClick:r,disabled:!h.trim(),className:"bg-emerald-500 hover:bg-emerald-600 disabled:bg-gray-500 disabled:cursor-not-allowed text-white px-4 py-3 rounded-xl transition-colors duration-200",children:e.jsx(W,{className:"w-4 h-4"})}),c?e.jsx("button",{onClick:t,className:"bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-3 rounded-xl transition-colors duration-200",title:"Finish grouping",children:e.jsx(Y,{className:"w-4 h-4"})}):null,e.jsx("button",{onClick:()=>{c?v():(d(""),u(!1))},className:"bg-white/10 hover:bg-white/20 text-white px-4 py-3 rounded-xl transition-colors duration-200 border border-white/20",children:e.jsx(T,{className:"w-4 h-4"})})]}),c&&e.jsxs("div",{className:"bg-yellow-500/20 border border-yellow-400/30 rounded-xl p-3",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("span",{className:"text-sm font-medium text-yellow-300",children:"Grouping Mode Active"}),e.jsxs("span",{className:"text-xs text-yellow-400",children:[g.length," keyword(s) in group"]})]}),e.jsx("p",{className:"text-xs text-yellow-200/80 mb-3",children:"Type keywords and press Enter to add them. Press Enter on empty input to finish grouping."}),g.length>0&&e.jsxs("div",{className:"space-y-1",children:[e.jsx("span",{className:"text-xs text-yellow-300 font-medium",children:"Keywords in group:"}),e.jsx("div",{className:"flex flex-wrap gap-1",children:g.map((x,j)=>e.jsxs("div",{className:"flex items-center space-x-1 bg-yellow-500/30 rounded-lg px-2 py-1 text-xs text-yellow-100",children:[e.jsx("span",{children:x}),e.jsx("button",{onClick:()=>M(x),className:"text-yellow-300 hover:text-yellow-100 transition-colors duration-200",children:e.jsx(T,{className:"w-3 h-3"})})]},j))})]})]})]}),s.blacklistKeywords.length>0&&e.jsx("div",{className:"max-h-32 overflow-y-auto space-y-2 custom-scrollbar-modern",children:s.blacklistKeywords.map((x,j)=>e.jsxs("div",{className:"flex items-center justify-between bg-white/10 rounded-xl px-4 py-3 border border-white/20",children:[e.jsx("span",{className:"text-sm text-white",children:x}),e.jsx("button",{onClick:()=>S(x),className:"text-red-400 hover:text-red-300 transition-colors duration-200",children:e.jsx(Pe,{className:"w-3 h-3"})})]},j))}),s.blacklistKeywords.length===0&&e.jsx("div",{className:"text-center py-4 text-gray-500 text-sm",children:"No keywords added"})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("h3",{className:"text-sm font-semibold text-white flex items-center space-x-2",children:[e.jsx(L,{className:"w-4 h-4"}),e.jsx("span",{children:"Smart Matching"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-300",children:"Enable fuzzy matching"}),e.jsx("button",{onClick:()=>l({...s,fuzzyMatching:{...s.fuzzyMatching,enabled:!s.fuzzyMatching.enabled}}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200 ${s.fuzzyMatching.enabled?"bg-emerald-500":"bg-gray-600"}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200 ${s.fuzzyMatching.enabled?"translate-x-6":"translate-x-1"}`})})]}),s.fuzzyMatching.enabled&&e.jsxs("div",{className:"space-y-3 bg-white/5 rounded-xl p-4 border border-white/10",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-300",children:"Match Threshold"}),e.jsx("span",{className:"text-xs text-emerald-400 font-mono",children:s.fuzzyMatching.threshold})]}),e.jsx("input",{type:"range",min:"0.1",max:"1.0",step:"0.1",value:s.fuzzyMatching.threshold,onChange:x=>l({...s,fuzzyMatching:{...s.fuzzyMatching,threshold:parseFloat(x.target.value)}}),className:"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"}),e.jsxs("div",{className:"flex justify-between text-xs text-gray-500",children:[e.jsx("span",{children:"Strict"}),e.jsx("span",{children:"Loose"})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-300",children:"Use payment aliases"}),e.jsx("button",{onClick:()=>l({...s,fuzzyMatching:{...s.fuzzyMatching,enableAliases:!s.fuzzyMatching.enableAliases}}),className:`relative inline-flex h-5 w-9 items-center rounded-full transition-colors duration-200 ${s.fuzzyMatching.enableAliases?"bg-emerald-500":"bg-gray-600"}`,children:e.jsx("span",{className:`inline-block h-3 w-3 transform rounded-full bg-white transition-transform duration-200 ${s.fuzzyMatching.enableAliases?"translate-x-5":"translate-x-1"}`})})]}),e.jsxs("div",{className:"text-xs text-gray-500 bg-blue-500/10 border border-blue-500/20 rounded-lg p-2",children:[e.jsx("span",{className:"text-blue-300 font-medium",children:"💡 Tip:"})," Fuzzy matching helps catch AirTM payment methods with slight variations in naming."]})]})]}),e.jsx("div",{className:"pt-4 border-t border-white/10",children:e.jsxs("div",{className:"flex items-center justify-between text-xs text-gray-400",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(re,{className:"w-3 h-3"}),e.jsxs("span",{children:["Status: ",s.monitoring?"Active":"Paused"]})]}),e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx("div",{className:`w-2 h-2 rounded-full ${s.monitoring?"bg-emerald-400 animate-pulse":"bg-gray-500"}`}),e.jsx("span",{children:s.monitoring?"Monitoring":"Stopped"})]})]})})]})]})},qe=({stats:s,filteredCount:l,isFiltered:m})=>{const f=[{label:"Total",value:s.total,icon:R,color:"text-blue-400",bgColor:"bg-blue-500/10",borderColor:"border-blue-500/20"},{label:"New",value:s.new,icon:Ne,color:"text-emerald-400",bgColor:"bg-emerald-500/10",borderColor:"border-emerald-500/20"},{label:"Accepted",value:s.accepted,icon:fe,color:"text-green-400",bgColor:"bg-green-500/10",borderColor:"border-green-500/20"},{label:"Rejected",value:s.rejected,icon:ye,color:"text-red-400",bgColor:"bg-red-500/10",borderColor:"border-red-500/20"}];return e.jsxs("div",{className:"space-y-4",children:[m&&e.jsxs("div",{className:"flex items-center justify-between bg-emerald-50/80 border border-emerald-200/60 rounded-xl px-4 py-3 backdrop-blur-sm",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-8 h-8 bg-emerald-500 rounded-lg flex items-center justify-center shadow-sm",children:e.jsx(L,{className:"w-4 h-4 text-white"})}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm font-semibold text-emerald-700",children:"Filter Active"}),e.jsx("p",{className:"text-xs text-emerald-600",children:"Custom criteria applied"})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"text-lg font-bold text-emerald-700",children:l}),e.jsxs("div",{className:"text-xs text-emerald-600",children:["of ",s.total," offers"]})]})]}),e.jsx("div",{className:"grid grid-cols-2 gap-4",children:f.map((a,b)=>{const p=a.icon;return e.jsxs("div",{className:"bg-white/60 backdrop-blur-sm border border-slate-200/60 rounded-xl p-4 hover:bg-white/80 hover:border-slate-300/60 transition-all duration-300 hover:shadow-lg hover:-translate-y-0.5 group",style:{animationDelay:`${b*100}ms`},children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsx("div",{className:`w-10 h-10 ${a.bgColor} rounded-xl flex items-center justify-center shadow-sm group-hover:scale-110 transition-transform duration-300`,children:e.jsx(p,{className:`w-5 h-5 ${a.color}`})}),e.jsx("div",{className:`w-3 h-3 ${a.color.replace("text-","bg-")} rounded-full opacity-60 group-hover:opacity-100 transition-opacity duration-300`})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:`text-2xl font-bold ${a.color} leading-none`,children:a.value.toLocaleString()}),e.jsx("div",{className:"text-sm text-slate-600 font-medium",children:a.label})]}),e.jsx("div",{className:"mt-3 h-2 bg-slate-200/60 rounded-full overflow-hidden",children:e.jsx("div",{className:`h-full ${a.color.replace("text-","bg-")} transition-all duration-1000 ease-out rounded-full`,style:{width:s.total>0?`${a.value/s.total*100}%`:"0%",animationDelay:`${b*200+500}ms`}})})]},a.label)})}),e.jsx("div",{className:"bg-slate-50/60 backdrop-blur-sm border border-slate-200/60 rounded-xl p-4",children:e.jsxs("div",{className:"flex items-center justify-between text-sm",children:[e.jsxs("div",{className:"flex items-center space-x-2 text-slate-600",children:[e.jsx(Q,{className:"w-4 h-4"}),e.jsxs("span",{className:"font-medium",children:["Last updated: ",new Date().toLocaleTimeString()]})]}),e.jsx("div",{className:"flex items-center space-x-4 text-slate-700",children:s.total>0&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-lg font-bold text-green-600",children:[Math.round(s.accepted/(s.accepted+s.rejected)*100)||0,"%"]}),e.jsx("div",{className:"text-xs text-slate-500",children:"Success Rate"})]}),e.jsx("div",{className:"w-px h-8 bg-slate-300"}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-lg font-bold text-blue-600",children:s.total-s.accepted-s.rejected}),e.jsx("div",{className:"text-xs text-slate-500",children:"Pending"})]})]})})]})})]})};function We(){const[s,l]=w.useState([]),[m,f]=w.useState(null),[a,b]=w.useState(null),[p,h]=w.useState(!0),[d,o]=w.useState(null),[u,g]=w.useState(null);w.useEffect(()=>{y(),c()},[]);const y=async()=>{var r;h(!0);try{console.log("Popup: Loading initial data...");const t=await chrome.runtime.sendMessage({type:"GET_POPUP_DATA"});console.log("Popup: Received response:",t),t&&t.success&&t.data?(console.log("Popup: Setting offers:",((r=t.data.offers)==null?void 0:r.length)||0),console.log("Popup: Setting settings:",t.data.settings),console.log("Popup: Setting stats:",t.data.stats),l(t.data.offers||[]),f(t.data.settings||null),b(t.data.stats||{totalOffers:0,newOffers:0,averageRate:0})):(console.error("Popup: Invalid response from background:",t),o((t==null?void 0:t.error)||"Failed to load data from background script"))}catch(t){console.error("Popup: Error loading initial data:",t),o("Failed to communicate with extension")}finally{h(!1)}},c=()=>{const r=t=>{t.type==="OFFERS_UPDATED"&&(l(t.offers),b(t.stats))};return chrome.runtime.onMessage.addListener(r),()=>chrome.runtime.onMessage.removeListener(r)},N=async r=>{try{const t=await chrome.runtime.sendMessage({type:"ACCEPT_OFFER",offerId:r.id});t.success||o(t.error||"Failed to accept offer")}catch{o("Failed to accept offer")}},k=async r=>{try{const t=await chrome.runtime.sendMessage({type:"REJECT_OFFER",offerId:r.id});t.success||o(t.error||"Failed to reject offer")}catch{o("Failed to reject offer")}},n=async r=>{try{const t=await chrome.runtime.sendMessage({type:"UPDATE_SETTINGS",settings:r});t.success?f(r):o(t.error||"Failed to update settings")}catch{o("Failed to update settings")}},C=()=>{chrome.runtime.openOptionsPage()},_=()=>{y()};return p?e.jsxs("div",{className:"w-[520px] h-[680px] bg-gradient-to-br from-white via-gray-50 to-blue-50 flex items-center justify-center relative overflow-hidden",children:[e.jsx("div",{className:"absolute inset-0 opacity-30",children:e.jsx("div",{className:"absolute inset-0",style:{backgroundImage:`
              radial-gradient(circle at 25% 25%, rgba(99, 102, 241, 0.08) 0%, transparent 50%),
              radial-gradient(circle at 75% 75%, rgba(168, 85, 247, 0.08) 0%, transparent 50%)
            `}})}),e.jsx("div",{className:"absolute inset-0 bg-white/50 backdrop-blur-sm"}),e.jsxs("div",{className:"relative z-10 text-center",children:[e.jsx("div",{className:"w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-3xl flex items-center justify-center shadow-xl mb-6 mx-auto border border-white/30",children:e.jsx("span",{className:"text-white font-bold text-2xl",children:"A"})}),e.jsx("div",{className:"relative mb-6",children:e.jsxs("div",{className:"w-20 h-20 mx-auto",children:[e.jsx("div",{className:"absolute inset-0 rounded-full border-4 border-gray-200/60"}),e.jsx("div",{className:"absolute inset-0 rounded-full border-4 border-transparent border-t-indigo-500 animate-spin"}),e.jsx("div",{className:"absolute inset-2 rounded-full border-3 border-transparent border-t-purple-400 animate-spin",style:{animationDirection:"reverse",animationDuration:"2s"}})]})}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("h2",{className:"text-xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent",children:"Airtm Monitor Pro"}),e.jsx("p",{className:"text-gray-600 font-medium animate-pulse",children:"Loading offers..."}),e.jsxs("div",{className:"flex items-center justify-center space-x-2 mt-4",children:[e.jsx("div",{className:"w-2 h-2 bg-indigo-500 rounded-full animate-bounce"}),e.jsx("div",{className:"w-2 h-2 bg-purple-500 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),e.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]})]})]})]}):e.jsxs("div",{className:"w-[600px] h-[750px] bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 relative overflow-hidden",children:[e.jsxs("div",{className:"absolute inset-0",children:[e.jsx("div",{className:"absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(120,119,198,0.1),transparent_50%),radial-gradient(circle_at_80%_80%,rgba(255,119,198,0.1),transparent_50%),radial-gradient(circle_at_40%_40%,rgba(99,102,241,0.05),transparent_50%)]"}),e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-white/80 via-transparent to-white/40"})]}),e.jsxs("div",{className:"relative z-10 h-full flex flex-col",children:[e.jsx("div",{className:"bg-white/90 backdrop-blur-xl border-b border-slate-200/50 shadow-sm",children:e.jsx("div",{className:"px-6 py-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"w-12 h-12 bg-gradient-to-br from-indigo-600 via-purple-600 to-pink-600 rounded-2xl flex items-center justify-center shadow-lg border border-white/20",children:e.jsx("span",{className:"text-white font-bold text-xl",children:"A"})}),e.jsx("div",{className:"absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white shadow-sm animate-pulse"})]}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent",children:"Airtm Monitor Pro"}),e.jsx("p",{className:"text-sm text-slate-500 font-medium",children:"Real-time Trading Dashboard"})]})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("button",{onClick:_,className:"group relative px-4 py-2.5 bg-white/80 border border-slate-200/80 rounded-xl hover:bg-white hover:border-indigo-300/60 transition-all duration-300 shadow-sm hover:shadow-md",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("svg",{className:"w-4 h-4 text-indigo-600 group-hover:rotate-180 transition-transform duration-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),e.jsx("span",{className:"text-sm font-medium text-slate-700",children:"Refresh"})]})}),e.jsx("button",{onClick:C,className:"group relative px-4 py-2.5 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-xl hover:from-indigo-700 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("svg",{className:"w-4 h-4 group-hover:rotate-90 transition-transform duration-300",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]}),e.jsx("span",{className:"text-sm font-medium",children:"Settings"})]})})]})]})})}),d&&e.jsx("div",{className:"mx-6 mt-4 animate-in slide-in-from-top duration-300",children:e.jsx("div",{className:"bg-red-50/90 backdrop-blur-sm border border-red-200/60 rounded-xl p-4 shadow-sm",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx("div",{className:"w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center shadow-sm flex-shrink-0 mt-0.5",children:e.jsx("svg",{className:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h4",{className:"text-sm font-semibold text-red-800 mb-1",children:"Error"}),e.jsx("p",{className:"text-sm text-red-700",children:d})]})]}),e.jsx("button",{onClick:()=>o(null),className:"text-red-400 hover:text-red-600 transition-colors duration-200 p-1 rounded-lg hover:bg-red-100/50",children:e.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})})}),e.jsx("div",{className:"px-6 py-4",children:e.jsx("div",{className:"bg-white/70 backdrop-blur-xl border border-slate-200/50 rounded-2xl shadow-sm",children:e.jsx("div",{className:"p-4",children:e.jsx(qe,{stats:a?{total:a.totalOffers,new:a.newOffers,accepted:a.acceptedOffers,rejected:a.rejectedOffers}:{total:0,new:0,accepted:0,rejected:0}})})})}),e.jsx("div",{className:"flex-1 px-6 pb-6 min-h-0",children:e.jsx("div",{className:"h-full bg-white/70 backdrop-blur-xl border border-slate-200/50 rounded-2xl shadow-sm overflow-hidden",children:e.jsxs("div",{className:"h-full flex flex-col",children:[e.jsx("div",{className:"px-6 py-4 border-b border-slate-200/50 bg-slate-50/50",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-semibold text-slate-800",children:"Available Offers"}),e.jsxs("div",{className:"flex items-center space-x-2 text-sm text-slate-500",children:[e.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),e.jsx("span",{children:"Live Updates"})]})]})}),e.jsx("div",{className:"flex-1 overflow-hidden",children:e.jsx("div",{className:"h-full overflow-y-auto custom-scrollbar-modern",children:e.jsx("div",{className:"p-4",children:e.jsx(Ve,{offers:s,selectedOffer:u,onOfferSelect:g,settings:m||{monitoring:!1,telegramBotToken:"",telegramChatId:"",webhookUrl:"",soundEnabled:!1,notificationsEnabled:!1,autoAccept:!1,minAmount:0,maxAmount:999999,preferredCurrencies:[],keywords:[],blacklistKeywords:[],paymentMethods:[],countries:[],hotkeys:{accept_offer:{key:"Enter",description:"Accept selected offer"},reject_offer:{key:"Delete",description:"Reject selected offer"},open_offer_details:{key:"Ctrl+Shift+D",description:"Open offer details"},cycle_offers:{key:"Ctrl+Shift+C",description:"Cycle offers"}},fuzzyMatching:{enabled:!1,threshold:.8,enableAliases:!0,customAliases:{}}}})})})})]})})}),e.jsx("div",{className:"px-6 py-4",children:e.jsx("div",{className:"bg-white/70 backdrop-blur-xl border border-slate-200/50 rounded-2xl shadow-sm",children:e.jsx("div",{className:"p-4",children:e.jsx(Ue,{settings:m||{monitoring:!1,telegramBotToken:"",telegramChatId:"",webhookUrl:"",soundEnabled:!1,notificationsEnabled:!1,autoAccept:!1,minAmount:0,maxAmount:999999,preferredCurrencies:[],keywords:[],blacklistKeywords:[],paymentMethods:[],countries:[],hotkeys:{accept_offer:{key:"Enter",description:"Accept selected offer"},reject_offer:{key:"Delete",description:"Reject selected offer"},open_offer_details:{key:"Ctrl+Shift+D",description:"Open offer details"},cycle_offers:{key:"Ctrl+Shift+C",description:"Cycle offers"}},fuzzyMatching:{enabled:!1,threshold:.8,enableAliases:!0,customAliases:{}}},onSettingsUpdate:n,onAcceptOffer:r=>{const t=s.find(v=>v.id===r);t&&N(t)},onRejectOffer:r=>{const t=s.find(v=>v.id===r);t&&k(t)},selectedOffer:u})})})})]})]})}const ee=document.getElementById("root");if(!ee)throw new Error("Root element not found");const Ye=se(ee);Ye.render(e.jsx(We,{}));
