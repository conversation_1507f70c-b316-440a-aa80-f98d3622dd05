<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=600, height=850, initial-scale=1.0, user-scalable=no">
  <title>Airtm Monitor Pro</title>
  <style>
    /* Inline critical CSS for popup dimensions */
    html, body {
      width: 600px !important;
      height: 850px !important;
      min-width: 600px !important;
      min-height: 850px !important;
      max-width: 600px !important;
      max-height: 850px !important;
      margin: 0 !important;
      padding: 0 !important;
      overflow: hidden !important;
    }
    #root {
      width: 600px !important;
      height: 850px !important;
      min-width: 600px !important;
      min-height: 850px !important;
    }
  </style>
  <script type="module" crossorigin src="/assets/popup-d07e9de2.js"></script>
  <link rel="modulepreload" crossorigin href="/assets/extension-context-ed07ef7d.js">
  <link rel="stylesheet" href="/src/options/styles.css">
</head>
<body class="popup">
  <div id="root"></div>
  
</body>
</html>
