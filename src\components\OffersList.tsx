import React from 'react';
import { Search, Filter, X, AlertCircle } from 'lucide-react';
import OfferCard from './OfferCard';
import type { Offer, ExtensionSettings } from '../types';
import { AirTMPaymentMethodMatcher } from '../utils/AirTMPaymentMethodMatcher';

interface OffersListProps {
  offers: Offer[];
  selectedOffer: Offer | null;
  onOfferSelect: (offer: Offer) => void;
  settings: ExtensionSettings;
}

// Filter offers based on blacklist keywords (payment methods)
const filterOffersByBlacklist = (offers: Offer[], blacklistKeywords: string[], settings: ExtensionSettings): Offer[] => {
  if (!blacklistKeywords.length) return offers;
  
  // Create a fuzzy matcher for blacklist filtering
  const blacklistMatcher = new AirTMPaymentMethodMatcher({
    threshold: settings.fuzzyMatching.threshold,
    enableAliases: settings.fuzzyMatching.enableAliases,
    customAliases: settings.fuzzyMatching.customAliases
  });
  
  return offers.filter(offer => {
    const rawPaymentMethod = offer.makerPaymentMethod?.version?.category?.translationTag || '';
    
    if (settings.fuzzyMatching.enabled) {
      // Use fuzzy matching for blacklist
      const matchResult = blacklistMatcher.matchAnyPaymentMethod(
        rawPaymentMethod,
        blacklistKeywords
      );
      return !matchResult.isMatch; // Filter out if it matches blacklist
    } else {
      // Use legacy exact matching
      let cleanPaymentMethod = rawPaymentMethod.toLowerCase();
      
      // Clean up payment method names
      if (cleanPaymentMethod.startsWith('category_tree:airtm_')) {
        cleanPaymentMethod = cleanPaymentMethod.replace('category_tree:airtm_', '');
      }
      if (cleanPaymentMethod.startsWith('e_transfer_')) {
        cleanPaymentMethod = cleanPaymentMethod.replace('e_transfer_', '');
      }
      
      return !blacklistKeywords.some(keyword => 
        cleanPaymentMethod.includes(keyword.toLowerCase())
      );
    }
  });
};

const OffersList: React.FC<OffersListProps> = ({
  offers,
  selectedOffer,
  onOfferSelect,
  settings
}) => {
  const [searchTerm, setSearchTerm] = React.useState('');
  const [showFiltered, setShowFiltered] = React.useState(false);

  // Apply blacklist filter
  const blacklistFiltered = filterOffersByBlacklist(offers, settings.blacklistKeywords || [], settings);
  
  // Apply search filter
  const searchFiltered = blacklistFiltered.filter(offer => {
    if (!searchTerm) return true;
    
    const searchText = [
      offer.peer ? `${offer.peer.firstName} ${offer.peer.lastName}` : '',
      offer.makerPaymentMethod?.version?.category?.translationTag || '',
      offer.hash || '',
      offer.status || '',
      offer.currency?.symbol || offer.walletCurrency?.symbol || '',
''
    ].join(' ').toLowerCase();
    
    return searchText.includes(searchTerm.toLowerCase());
  });

  const filteredOffers = searchFiltered;
  const isFiltered = settings.blacklistKeywords?.length > 0 || searchTerm.length > 0;
  const blacklistedCount = offers.length - blacklistFiltered.length;

  const clearSearch = () => {
    setSearchTerm('');
  };

  const toggleFilteredView = () => {
    setShowFiltered(!showFiltered);
  };

  return (
    <div className="offers-list-container">
      {/* Search and Filter Header */}
      <div className="offers-list-header">
        {/* Search Bar */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search offers..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="
              w-full pl-10 pr-10 py-3 bg-white/10 border border-white/20 rounded-xl
              text-white placeholder-gray-400 text-sm backdrop-blur-sm
              focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500
              transition-all duration-200
            "
          />
          {searchTerm && (
            <button
              onClick={clearSearch}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors"
            >
              <X className="w-4 h-4" />
            </button>
          )}
        </div>

        {/* Filter Status and Controls */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {/* Blacklist Filter Status */}
            {blacklistedCount > 0 && (
              <div className="flex items-center space-x-2 bg-red-500/20 border border-red-500/30 rounded-xl px-4 py-2 backdrop-blur-sm">
                <Filter className="w-3 h-3 text-red-400" />
                <span className="text-xs text-red-400 font-medium">
                  {blacklistedCount} blocked
                </span>
                <button
                  onClick={toggleFilteredView}
                  className="text-xs text-red-300 hover:text-red-200 underline"
                >
                  {showFiltered ? 'Hide' : 'Show'}
                </button>
              </div>
            )}

            {/* Search Results Count */}
            {searchTerm && (
              <div className="flex items-center space-x-2 bg-blue-500/20 border border-blue-500/30 rounded-xl px-4 py-2 backdrop-blur-sm">
                <Search className="w-3 h-3 text-blue-400" />
                <span className="text-xs text-blue-400 font-medium">
                  {filteredOffers.length} found
                </span>
              </div>
            )}
          </div>

          {/* Total Count */}
          <div className="text-xs text-gray-400">
            {filteredOffers.length} of {offers.length} offers
          </div>
        </div>
      </div>

      {/* Offers List - Scrollable Content */}
      <div className="offers-list-scroll">
        {filteredOffers.length === 0 ? (
          <div className="offers-empty-state">
            <AlertCircle className="w-12 h-12 text-gray-500 mb-4" />
            <h3 className="text-lg font-semibold text-gray-300 mb-2">
              {offers.length === 0 ? 'No offers available' : 'No matching offers'}
            </h3>
            <p className="text-sm text-gray-500 max-w-sm">
              {offers.length === 0
                ? 'Start monitoring to see offers appear here'
                : isFiltered
                  ? 'Try adjusting your search or filter criteria'
                  : 'Check back later for new offers'
              }
            </p>
            {(searchTerm || blacklistedCount > 0) && (
              <button
                onClick={() => {
                  setSearchTerm('');
                  setShowFiltered(false);
                }}
                className="mt-4 px-6 py-3 bg-emerald-500 hover:bg-emerald-600 text-white text-sm rounded-xl transition-colors border border-emerald-400/30"
              >
                Clear filters
              </button>
            )}
          </div>
        ) : (
          <div className="offers-list-items">
            {(showFiltered ? offers.filter(offer => !blacklistFiltered.includes(offer)) : filteredOffers).map((offer, index) => (
              <div
                key={offer.hash}
                className="offer-item-wrapper"
                style={{ animationDelay: `${index * 50}ms` }}
              >
                <OfferCard
                  offer={offer}
                  isSelected={selectedOffer?.hash === offer.hash}
                  onClick={() => onOfferSelect(offer)}
                  isBlacklisted={showFiltered && !blacklistFiltered.includes(offer)}
                />
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Footer Info */}
      {filteredOffers.length > 0 && (
        <div className="offers-list-footer">
          <div className="flex items-center justify-between text-xs text-gray-400">
            <span>
              {isFiltered ? `Filtered: ${filteredOffers.length}` : `Total: ${offers.length}`} offers
            </span>
            <span>
              {selectedOffer ? 'Offer selected' : 'Select an offer to view details'}
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default OffersList;
