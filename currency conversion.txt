Fix the multi-currency conversion calculation error in Telegram notifications for the Airtm Monitor Pro extension.

**Problem**: The Telegram notifications are displaying incorrect payment amounts due to missing currency conversion calculations between wallet currency and local currency. The extension is sending the wallet currency amount instead of the converted local currency amount that users actually receive/send.

**Root Cause Analysis**: 
- Airtm operations involve two currencies: wallet currency (USD/USDC/EUR) and local currency (EGP/other)
- The notification system is displaying the `grossAmount` or `netAmount` in wallet currency instead of converting to local currency
- Exchange rate conversion logic is missing from the Telegram notification formatting

**Specific Requirements**:

1. **Currency Detection & Extraction**:
   - Identify wallet currency from `operation.walletCurrency.symbol` (e.g., "$", "€")
   - Identify local currency from `operation.currency.symbol` (e.g., "£", "₹", "₦")
   - Extract exchange rate from available fields: `operation.rate`, `operation.displayRate.rate`, or `operation.rateInfo.exchangeRate`

2. **Conversion Logic Implementation**:
   - For SELL operations: `localAmount = walletAmount * exchangeRate`
   - For BUY operations: `localAmount = walletAmount * exchangeRate` (verify direction)
   - Handle `displayRate.direction` field ("TO_LOCAL_CURRENCY" vs "FROM_LOCAL_CURRENCY") to ensure correct conversion direction
   - Use `grossAmount` for total amount or `netAmount` for amount after fees (specify which is preferred)

3. **Notification Message Updates**:
   - Replace wallet currency amounts with converted local currency amounts
   - Use correct local currency symbol from `operation.currency.symbol`
   - Format: "500 £" instead of "10 $" for EGP example
   - Include both amounts if helpful: "10 $ → 500 £"

4. **Multi-Currency Support**:
   - Support all wallet currencies: USD ($), USDC, EUR (€), etc.
   - Support all local currencies: EGP (£), NGN (₦), INR (₹), etc.
   - Handle edge cases where exchange rate might be missing or zero

**Implementation Location**:
- Target file: `src/background/telegram-service.ts` or similar Telegram notification handler
- Specific function: `sendTelegramNotification` or `formatOfferMessage`
- Integration point: Where offer amounts are formatted for Telegram message text

**Test Cases**:
- USD to EGP: 10 USD × 49 = 490 EGP → display "490 £"
- EUR to EGP: 5 EUR × 53 = 265 EGP → display "265 £"  
- USDC to NGN: 20 USDC × 1600 = 32,000 NGN → display "32,000 ₦"

**Data Source**: Use the OperationDetails schema from `afteraccepting.txt` and AvailableOperations schema to ensure compatibility with both pre-acceptance offers and post-acceptance operations.

**Error Handling**: If exchange rate is missing or invalid, fall back to displaying wallet currency amount with a note like "~10 $ (rate pending)"