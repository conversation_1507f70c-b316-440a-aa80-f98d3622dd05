Fix the multi-currency conversion calculation error in Telegram notifications for the Airtm Monitor Pro extension.

**Problem**: The Telegram notifications are displaying incorrect payment amounts due to missing currency conversion calculations between wallet currency and local currency. The extension is sending the wallet currency amount instead of the converted local currency amount that users actually receive/send.

**Root Cause Analysis**: 
- Airtm operations involve two currencies: wallet currency (USD/USDC/EUR) and local currency (EGP/other)
- The notification system is displaying the `grossAmount` or `netAmount` in wallet currency instead of converting to local currency
- Exchange rate conversion logic is missing from the Telegram notification formatting

**Specific Requirements**:

1. **Currency Detection & Extraction**:
   - Identify wallet currency from `operation.walletCurrency.symbol` (e.g., "$", "€")
   - Identify local currency from `operation.currency.symbol` (e.g., "£", "₹", "₦")
   - Extract exchange rate from available fields: `operation.rate`, `operation.displayRate.rate`, or `operation.rateInfo.exchangeRate`

2. **Conversion Logic Implementation**:
   - For SELL operations: `localAmount = walletAmount * exchangeRate`
   - For BUY operations: `localAmount = walletAmount * exchangeRate` (verify direction)
   - Handle `displayRate.direction` field ("TO_LOCAL_CURRENCY" vs "FROM_LOCAL_CURRENCY") to ensure correct conversion direction
   - Use `grossAmount` for total amount or `netAmount` for amount after fees (specify which is preferred)

3. **Notification Message Updates**:
   - Replace wallet currency amounts with converted local currency amounts
   - Use correct local currency symbol from `operation.currency.symbol`
   - Format: "500 £" instead of "10 $" for EGP example
   - Include both amounts if helpful: "10 $ → 500 £"

4. **Multi-Currency Support**:
   - Support all wallet currencies: USD ($), USDC, EUR (€), etc.
   - Support all local currencies: EGP (£), NGN (₦), INR (₹), etc.
   - Handle edge cases where exchange rate might be missing or zero

**Implementation Location**:
- Target file: `src/background/telegram-service.ts` or similar Telegram notification handler
- Specific function: `sendTelegramNotification` or `formatOfferMessage`
- Integration point: Where offer amounts are formatted for Telegram message text

**Test Cases**:
- USD to EGP: 10 USD × 49 = 490 EGP → display "490 £"
- EUR to EGP: 5 EUR × 53 = 265 EGP → display "265 £"  
- USDC to NGN: 20 USDC × 1600 = 32,000 NGN → display "32,000 ₦"

**Data Source**: Use the OperationDetails schema from `afteraccepting.txt` and AvailableOperations schema to ensure compatibility with both pre-acceptance offers and post-acceptance operations.

**Error Handling**: If exchange rate is missing or invalid, fall back to displaying wallet currency amount with a note like "~10 $ (rate pending)"


Based on the OperationDetails schema in "afteraccepting.txt", implement and verify the currency conversion calculation for Telegram notifications in the Airtm Monitor Pro extension.

**Specific Example from afteraccepting.txt**:
- **Wallet Currency Amount**: 10.85 USDC (from `fundsToReceiveTaker: "10.85"`)
- **Local Currency Amount**: 514.55 EGP (from `fundsToSendTaker: "514.55"`)
- **Exchange Rate**: 49.57 (from `rate: "49.57"`)
- **Operation Type**: SELL operation

**Verification Requirements**:
1. **Confirm Rate Calculation**: Verify that 10.85 USDC × 49.57 ≈ 537.84 EGP, but the actual local amount is 514.55 EGP due to fees
2. **Use Correct Amount Fields**: 
   - For wallet currency: Use `rateInfo.fundsToReceiveTaker` (10.85) instead of `grossAmount` (11)
   - For local currency: Use `rateInfo.fundsToSendTaker` (514.55) instead of `rateInfo.netAmount` (514.55)
3. **Handle Fee Calculations**: Account for the difference between gross amount and net amount after fees
4. **Test Conversion Logic**: Ensure the `convertCurrencyAmount()` function correctly handles this SELL operation example
5. **Validate Direction**: Confirm `displayRate.direction: "TO_LOCAL_CURRENCY"` is properly interpreted

**Expected Telegram Message Output**:
- **Before Fix**: "11 $ : Vodafone Cash Egypt (🇪🇬)" (incorrect wallet currency amount)
- **After Fix**: "514.55 £ : Vodafone Cash Egypt (🇪🇬)" (correct local currency amount)
- **Alternative Format**: "514.55 £ (10.85 $) : Vodafone Cash Egypt (🇪🇬)" (showing both amounts)

**Implementation Focus**:
- Update the `convertCurrencyAmount()` function to use the correct amount fields from `rateInfo`
- Ensure proper handling of SELL vs BUY operation types
- Test with the specific data structure from afteraccepting.txt
- Verify that `displayRate.rate: "47.42396313364055299539"` vs `rate: "49.57"` are handled correctly