import React from 'react';
import { PopupFooterProps } from '../types';

const PopupFooter: React.FC<PopupFooterProps> = ({ 
  onHelpClick, 
  onAboutClick 
}) => {
  const handleHelpClick = (event: React.MouseEvent<HTMLAnchorElement>) => {
    event.preventDefault();
    onHelpClick();
  };

  const handleAboutClick = (event: React.MouseEvent<HTMLAnchorElement>) => {
    event.preventDefault();
    onAboutClick();
  };

  return (
    <div className="popup-footer">
      <a href="#" className="footer-link" onClick={handleHelpClick}>
        Help
      </a>
      <span style={{ fontSize: '0.75rem', color: 'var(--gray-400)' }}>
        v2.0.0
      </span>
      <a href="#" className="footer-link" onClick={handleAboutClick}>
        About
      </a>
    </div>
  );
};

export default PopupFooter;
