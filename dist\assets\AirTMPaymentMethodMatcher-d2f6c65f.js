var f=Object.defineProperty;var d=(r,e,a)=>e in r?f(r,e,{enumerable:!0,configurable:!0,writable:!0,value:a}):r[e]=a;var o=(r,e,a)=>(d(r,typeof e!="symbol"?e+"":e,a),a);class g{constructor(e={}){o(this,"config");o(this,"defaultAliases",{paypal:["paypal","pp","pay pal"],zelle:["zelle","zell","zele"],venmo:["venmo","venm","vemo"],cashapp:["cashapp","cash app","cash-app","$cashtag"],wise:["wise","transferwise","transfer wise"],revolut:["revolut","revolt","revol"],skrill:["skrill","skril","skrilla"],neteller:["neteller","neteler","netelr"],perfectmoney:["perfectmoney","perfect money","pm","perfect-money"],webmoney:["webmoney","web money","wm","web-money"],bitcoin:["bitcoin","btc","bit coin"],ethereum:["ethereum","eth","ether"],usdt:["usdt","tether","usd-t"],binance:["binance","bnb","binance pay"],bank:["bank","banking","bank transfer","wire"],credit:["credit","credit card","cc","visa","mastercard"],debit:["debit","debit card","dc"]});this.config={threshold:.7,enableAliases:!0,customAliases:{},...e}}normalizePaymentMethod(e){return e?e.replace(/^CATEGORY_TREE:AIRTM_/i,"").replace(/^E_TRANSFER_/i,"").replace(/^AIRTM_/i,"").replace(/[_-]/g," ").toLowerCase().trim().replace(/\s+/g," "):""}levenshteinDistance(e,a){const n=Array(a.length+1).fill(null).map(()=>Array(e.length+1).fill(null));for(let t=0;t<=e.length;t++)n[0][t]=t;for(let t=0;t<=a.length;t++)n[t][0]=t;for(let t=1;t<=a.length;t++)for(let i=1;i<=e.length;i++){const l=e[i-1]===a[t-1]?0:1;n[t][i]=Math.min(n[t][i-1]+1,n[t-1][i]+1,n[t-1][i-1]+l)}return n[a.length][e.length]}calculateSimilarity(e,a){if(e===a)return 1;if(!e||!a)return 0;const n=Math.max(e.length,a.length);return 1-this.levenshteinDistance(e,a)/n}wordBasedMatch(e,a){const n=e.split(" ").filter(l=>l.length>0),t=a.split(" ").filter(l=>l.length>0);if(t.length===0)return 0;let i=0;for(const l of t)n.some(s=>s.includes(l)||l.includes(s)||this.calculateSimilarity(s,l)>.8)&&i++;return i/t.length}getAliases(e){const a=this.normalizePaymentMethod(e),n=[a];if(this.config.enableAliases){for(const[,t]of Object.entries(this.config.customAliases))if(t.some(i=>this.normalizePaymentMethod(i)===a)){n.push(...t.map(i=>this.normalizePaymentMethod(i)));break}for(const[,t]of Object.entries(this.defaultAliases))if(t.some(i=>this.normalizePaymentMethod(i)===a)){n.push(...t.map(i=>this.normalizePaymentMethod(i)));break}}return[...new Set(n)]}matchPaymentMethod(e,a){const n=this.normalizePaymentMethod(e),t=this.getAliases(a);let i=0,l="";for(const s of t){if(n===s)return{isMatch:!0,score:1,matchedAlias:s};if(n.includes(s)||s.includes(n)){const m=Math.max(s.length/n.length,n.length/s.length)*.9;m>i&&(i=m,l=s)}const c=this.wordBasedMatch(n,s)*.85;c>i&&(i=c,l=s);const h=this.calculateSimilarity(n,s)*.8;h>i&&(i=h,l=s)}return{isMatch:i>=this.config.threshold,score:i,matchedAlias:i>=this.config.threshold?l:void 0}}matchAnyPaymentMethod(e,a){let n={isMatch:!1,score:0};for(const t of a){const i=this.matchPaymentMethod(e,t);if(i.score>n.score&&(n=i),i.score===1)break}return n}updateConfig(e){this.config={...this.config,...e}}getConfig(){return{...this.config}}}export{g as A};
