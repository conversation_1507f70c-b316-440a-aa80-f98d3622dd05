import{c as o,j as e,R as W,X as T,r as N,C as P,P as Y,a as se}from"./globals-d0472011.js";import{A as te}from"./AirTMPaymentMethodMatcher-d2f6c65f.js";/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ae=[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]],le=o("activity",ae);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const re=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m4.9 4.9 14.2 14.2",key:"1m5liu"}]],ne=o("ban",re);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ce=[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M17 17H4a1 1 0 0 1-.74-1.673C4.59 13.956 6 12.499 6 8a6 6 0 0 1 .258-1.742",key:"178tsu"}],["path",{d:"m2 2 20 20",key:"1ooewy"}],["path",{d:"M8.668 3.01A6 6 0 0 1 18 8c0 2.687.77 4.653 1.707 6.05",key:"1hqiys"}]],oe=o("bell-off",ce);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ie=[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]],X=o("bell",ie);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const de=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],xe=o("chevron-down",de);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const me=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],he=o("chevron-up",me);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ue=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]],pe=o("circle-alert",ue);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const be=[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]],fe=o("circle-check-big",be);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const je=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]],ye=o("circle-x",je);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ge=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]],Q=o("clock",ge);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ne=[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],ve=o("eye",Ne);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const we=[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]],L=o("funnel",we);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ke=[["rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",key:"zuxfzm"}],["rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",key:"1okwgv"}]],Ce=o("pause",ke);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ae=[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]],_e=o("play",Ae);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Me=[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]],J=o("search",Me);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ze=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],Ee=o("settings",ze);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Se=[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]],Z=o("shield",Se);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $e=[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]],Te=o("star",$e);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Le=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]],Re=o("trash-2",Le);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pe=[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]],Fe=o("trending-down",Pe);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const De=[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]],F=o("trending-up",De);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Oe=[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["path",{d:"M16 9a5 5 0 0 1 0 6",key:"1q6k2b"}],["path",{d:"M19.364 18.364a9 9 0 0 0 0-12.728",key:"ijwkga"}]],Ke=o("volume-2",Oe);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ie=[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["line",{x1:"22",x2:"16",y1:"9",y2:"15",key:"1ewh16"}],["line",{x1:"16",x2:"22",y1:"9",y2:"15",key:"5ykzw1"}]],Be=o("volume-x",Ie),Ge=({offer:s,isSelected:r,onClick:m,isBlacklisted:f=!1})=>{var M,S,$,z,E,x,g,D,O,K,I,B,G,V;const a=((M=s.currency)==null?void 0:M.symbol)||((S=s.walletCurrency)==null?void 0:S.symbol)||"USD",p=s.operationType||"BUY",b=typeof s.grossAmount=="string"?parseFloat(s.grossAmount)||0:s.grossAmount||0,h=typeof s.netAmount=="string"?parseFloat(s.netAmount)||0:s.netAmount||0,d=s.peer?`${s.peer.firstName} ${s.peer.lastName}`:"Unknown User",c=((z=($=s.peer)==null?void 0:$.numbers)==null?void 0:z.score)||0,u=((x=(E=s.peer)==null?void 0:E.numbers)==null?void 0:x.completedOperations)||0,j=((D=(g=s.peer)==null?void 0:g.securityHub)==null?void 0:D.facialVerified)&&((K=(O=s.peer)==null?void 0:O.securityHub)==null?void 0:K.documentVerified)||!1;let i=((G=(B=(I=s.makerPaymentMethod)==null?void 0:I.version)==null?void 0:B.category)==null?void 0:G.translationTag)||"Unknown";i.startsWith("CATEGORY_TREE:AIRTM_")&&(i=i.replace("CATEGORY_TREE:AIRTM_","")),i.startsWith("E_TRANSFER_")&&(i=i.replace("E_TRANSFER_","")),i=i.replace(/_/g," ").toLowerCase().replace(/\b\w/g,A=>A.toUpperCase());const v=s.status||"CREATED",k=s.createdAt?new Date(s.createdAt):new Date,n=Date.now()-k.getTime()<5*60*1e3,C=A=>{const R=new Date().getTime()-A.getTime(),H=Math.floor(R/(1e3*60)),U=Math.floor(R/(1e3*60*60)),q=Math.floor(R/(1e3*60*60*24));return q>0?`${q}d ago`:U>0?`${U}h ago`:H>0?`${H}m ago`:"Just now"},l=(A=>{switch(A){case"BUY":return{color:"text-emerald-400",bgColor:"bg-emerald-500/10",borderColor:"border-emerald-500/20",icon:F,label:"Buy"};case"SELL":return{color:"text-red-400",bgColor:"bg-red-500/10",borderColor:"border-red-500/20",icon:Fe,label:"Sell"};default:return{color:"text-blue-400",bgColor:"bg-blue-500/10",borderColor:"border-blue-500/20",icon:F,label:A}}})(p),t=l.icon,w=A=>{switch(A){case"CREATED":return"text-emerald-400";case"PAUSED":return"text-yellow-400";case"INACTIVE":return"text-red-400";default:return"text-gray-400"}};return e.jsxs("div",{onClick:m,className:`
        relative rounded-xl border transition-all duration-200 cursor-pointer
        hover:scale-[1.02] hover:shadow-lg hover:shadow-black/20
        ${r?"border-emerald-500 bg-gradient-to-br from-emerald-500/10 to-gray-800 shadow-lg shadow-emerald-500/20":f?"border-red-500/50 bg-gradient-to-br from-red-500/5 to-gray-800 opacity-60":"border-gray-700 bg-gradient-to-br from-gray-800 to-gray-900 hover:border-gray-600"}
        ${n?"ring-2 ring-emerald-500/30":""}
      `,children:[f&&e.jsx("div",{className:"absolute top-2 right-2 z-10",children:e.jsxs("div",{className:"flex items-center space-x-1 bg-red-500/20 border border-red-500/30 rounded-lg px-2 py-1",children:[e.jsx(ne,{className:"w-3 h-3 text-red-400"}),e.jsx("span",{className:"text-xs text-red-400 font-medium",children:"Blocked"})]})}),n&&!f&&e.jsx("div",{className:"absolute top-2 right-2 z-10",children:e.jsxs("div",{className:"flex items-center space-x-1 bg-emerald-500/20 border border-emerald-500/30 rounded-lg px-2 py-1",children:[e.jsx("div",{className:"w-2 h-2 bg-emerald-400 rounded-full animate-pulse"}),e.jsx("span",{className:"text-xs text-emerald-400 font-medium",children:"New"})]})}),e.jsxs("div",{className:"p-4",children:[e.jsxs("div",{className:"flex items-start justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:`${l.bgColor} ${l.borderColor} border rounded-lg p-2`,children:e.jsx(t,{className:`w-4 h-4 ${l.color}`})}),e.jsx("div",{children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-lg font-bold text-white",children:a}),e.jsx("span",{className:`text-xs px-2 py-1 rounded-full ${l.bgColor} ${l.color} font-medium`,children:l.label})]})})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:`text-xs font-medium ${w(v)}`,children:v}),e.jsxs("div",{className:"text-xs text-gray-500 flex items-center space-x-1 mt-1",children:[e.jsx(Q,{className:"w-3 h-3"}),e.jsx("span",{children:C(k)})]})]})]}),e.jsxs("div",{className:"mb-3",children:[e.jsx("div",{className:"text-sm text-gray-400 mb-1",children:"Amount Range"}),e.jsxs("div",{className:"text-white font-semibold",children:[h.toLocaleString()," - ",b.toLocaleString()," ",a]}),b>0&&e.jsxs("div",{className:"text-xs text-gray-500",children:["Available: ",b.toLocaleString()," ",a]})]}),e.jsx("div",{className:"flex items-center justify-between mb-3",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:"w-8 h-8 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-full flex items-center justify-center",children:e.jsx("span",{className:"text-white text-xs font-bold",children:d.charAt(0).toUpperCase()})}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx("span",{className:"text-sm font-medium text-white",children:d}),j&&e.jsx(Z,{className:"w-3 h-3 text-blue-400"})]}),e.jsxs("div",{className:"flex items-center space-x-2 text-xs text-gray-400",children:[e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx(Te,{className:"w-3 h-3 text-yellow-400"}),e.jsx("span",{children:c.toFixed(1)})]}),e.jsx("span",{children:"•"}),e.jsxs("span",{children:[u," trades"]})]})]})]})}),e.jsxs("div",{className:"mb-3",children:[e.jsx("div",{className:"text-sm text-gray-400 mb-1",children:"Payment Method"}),e.jsx("div",{className:"text-white font-medium",children:i})]}),e.jsxs("div",{className:"flex items-center justify-between pt-3 border-t border-gray-700",children:[e.jsxs("div",{className:"text-xs text-gray-500",children:["ID: ",(V=s.hash)==null?void 0:V.substring(0,8),"..."]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[v==="CREATED"&&e.jsx("div",{className:"w-2 h-2 bg-emerald-400 rounded-full animate-pulse"}),e.jsx("span",{className:"text-xs text-gray-400",children:"Click to select"})]})]})]})]})},Ve=(s,r,m)=>{if(!r.length)return s;const f=new te({threshold:m.fuzzyMatching.threshold,enableAliases:m.fuzzyMatching.enableAliases,customAliases:m.fuzzyMatching.customAliases});return s.filter(a=>{var b,h,d;const p=((d=(h=(b=a.makerPaymentMethod)==null?void 0:b.version)==null?void 0:h.category)==null?void 0:d.translationTag)||"";if(m.fuzzyMatching.enabled)return!f.matchAnyPaymentMethod(p,r).isMatch;{let c=p.toLowerCase();return c.startsWith("category_tree:airtm_")&&(c=c.replace("category_tree:airtm_","")),c.startsWith("e_transfer_")&&(c=c.replace("e_transfer_","")),!r.some(u=>c.includes(u.toLowerCase()))}})},He=({offers:s,selectedOffer:r,onOfferSelect:m,settings:f})=>{var k;const[a,p]=W.useState(""),[b,h]=W.useState(!1),d=Ve(s,f.blacklistKeywords||[],f),u=d.filter(n=>{var _,l,t,w,M;return a?[n.peer?`${n.peer.firstName} ${n.peer.lastName}`:"",((t=(l=(_=n.makerPaymentMethod)==null?void 0:_.version)==null?void 0:l.category)==null?void 0:t.translationTag)||"",n.hash||"",n.status||"",((w=n.currency)==null?void 0:w.symbol)||((M=n.walletCurrency)==null?void 0:M.symbol)||"",""].join(" ").toLowerCase().includes(a.toLowerCase()):!0}),j=((k=f.blacklistKeywords)==null?void 0:k.length)>0||a.length>0,y=s.length-d.length,i=()=>{p("")},v=()=>{h(!b)};return e.jsxs("div",{className:"flex-1 flex flex-col bg-white/5 backdrop-blur-sm overflow-hidden rounded-xl border border-white/10",children:[e.jsxs("div",{className:"bg-white/10 border-b border-white/10 px-6 py-4 space-y-4",children:[e.jsxs("div",{className:"relative",children:[e.jsx(J,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search offers...",value:a,onChange:n=>p(n.target.value),className:`
              w-full pl-10 pr-10 py-3 bg-white/10 border border-white/20 rounded-xl
              text-white placeholder-gray-400 text-sm backdrop-blur-sm
              focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500
              transition-all duration-200
            `}),a&&e.jsx("button",{onClick:i,className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors",children:e.jsx(T,{className:"w-4 h-4"})})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[y>0&&e.jsxs("div",{className:"flex items-center space-x-2 bg-red-500/20 border border-red-500/30 rounded-xl px-4 py-2 backdrop-blur-sm",children:[e.jsx(L,{className:"w-3 h-3 text-red-400"}),e.jsxs("span",{className:"text-xs text-red-400 font-medium",children:[y," blocked"]}),e.jsx("button",{onClick:v,className:"text-xs text-red-300 hover:text-red-200 underline",children:b?"Hide":"Show"})]}),a&&e.jsxs("div",{className:"flex items-center space-x-2 bg-blue-500/20 border border-blue-500/30 rounded-xl px-4 py-2 backdrop-blur-sm",children:[e.jsx(J,{className:"w-3 h-3 text-blue-400"}),e.jsxs("span",{className:"text-xs text-blue-400 font-medium",children:[u.length," found"]})]})]}),e.jsxs("div",{className:"text-xs text-gray-400",children:[u.length," of ",s.length," offers"]})]})]}),e.jsx("div",{className:"flex-1 overflow-y-auto custom-scrollbar-modern",children:u.length===0?e.jsxs("div",{className:"flex flex-col items-center justify-center h-full text-center p-8",children:[e.jsx(pe,{className:"w-12 h-12 text-gray-500 mb-4"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-300 mb-2",children:s.length===0?"No offers available":"No matching offers"}),e.jsx("p",{className:"text-sm text-gray-500 max-w-sm",children:s.length===0?"Start monitoring to see offers appear here":j?"Try adjusting your search or filter criteria":"Check back later for new offers"}),(a||y>0)&&e.jsx("button",{onClick:()=>{p(""),h(!1)},className:"mt-4 px-6 py-3 bg-emerald-500 hover:bg-emerald-600 text-white text-sm rounded-xl transition-colors border border-emerald-400/30",children:"Clear filters"})]}):e.jsx("div",{className:"p-6 space-y-4",children:(b?s.filter(n=>!d.includes(n)):u).map((n,C)=>e.jsx("div",{className:"animate-fade-in",style:{animationDelay:`${C*50}ms`},children:e.jsx(Ge,{offer:n,isSelected:(r==null?void 0:r.hash)===n.hash,onClick:()=>m(n),isBlacklisted:b&&!d.includes(n)})},n.hash))})}),u.length>0&&e.jsx("div",{className:"bg-white/10 border-t border-white/10 px-6 py-3 backdrop-blur-sm",children:e.jsxs("div",{className:"flex items-center justify-between text-xs text-gray-400",children:[e.jsxs("span",{children:[j?`Filtered: ${u.length}`:`Total: ${s.length}`," offers"]}),e.jsx("span",{children:r?"Offer selected":"Select an offer to view details"})]})})]})},Ue=({settings:s,onSettingsUpdate:r,selectedOffer:m,onAcceptOffer:f,onRejectOffer:a})=>{var z,E;const[p,b]=N.useState(!1),[h,d]=N.useState(""),[c,u]=N.useState(!1),[j,y]=N.useState([]),[i,v]=N.useState(!1),k=()=>{r({...s,monitoring:!s.monitoring})},n=()=>{r({...s,autoAccept:!s.autoAccept})},C=()=>{r({...s,notificationsEnabled:!s.notificationsEnabled})},_=()=>{r({...s,soundEnabled:!s.soundEnabled})},l=()=>{h.trim()&&!s.blacklistKeywords.includes(h.trim())&&(i?(j.includes(h.trim())||y([...j,h.trim()]),d("")):(r({...s,blacklistKeywords:[...s.blacklistKeywords,h.trim()]}),d(""),u(!1)))},t=()=>{if(j.length>0){const x=j.filter(g=>!s.blacklistKeywords.includes(g));r({...s,blacklistKeywords:[...s.blacklistKeywords,...x]}),y([]),v(!1),d(""),u(!1)}},w=()=>{y([]),v(!1),d(""),u(!1)},M=x=>{y(j.filter(g=>g!==x))},S=x=>{r({...s,blacklistKeywords:s.blacklistKeywords.filter(g=>g!==x)})},$=x=>{x.key==="Enter"?i?h.trim()?l():t():h.trim()&&(v(!0),l()):x.key==="Escape"&&(i?w():(d(""),u(!1)))};return e.jsxs("div",{className:"control-panel animate-slide-up",children:[e.jsxs("div",{className:"control-section",children:[m&&e.jsxs("div",{className:"mb-4 p-4 glass-effect border border-emerald-200/60 rounded-xl animate-scale-in",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-8 h-8 gradient-secondary rounded-xl flex items-center justify-center shadow-sm",children:e.jsx(P,{className:"w-4 h-4 text-white"})}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm font-bold text-slate-800",children:"Selected Offer"}),e.jsxs("div",{className:"text-xs text-slate-600 font-medium",children:[((z=m.currency)==null?void 0:z.symbol)||((E=m.walletCurrency)==null?void 0:E.symbol)||"N/A"," • ",m.operationType]})]})]}),e.jsx("div",{className:"badge badge-success",children:"Active"})]}),e.jsxs("div",{className:"control-grid",children:[e.jsxs("button",{onClick:()=>f(m.hash),className:"btn btn-success hover-lift",children:[e.jsx(P,{className:"w-4 h-4"}),e.jsx("span",{children:"Accept"})]}),e.jsxs("button",{onClick:()=>a(m.hash),className:"btn btn-danger hover-lift",children:[e.jsx(T,{className:"w-4 h-4"}),e.jsx("span",{children:"Reject"})]})]})]}),e.jsxs("div",{className:"control-grid mb-6",children:[e.jsxs("button",{onClick:k,className:`control-button hover-lift ${s.monitoring?"active":"inactive"}`,children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[s.monitoring?e.jsx(Ce,{className:"w-5 h-5"}):e.jsx(_e,{className:"w-5 h-5"}),e.jsx("span",{className:"font-semibold",children:s.monitoring?"Pause":"Start"})]}),s.monitoring&&e.jsx("div",{className:"status-online animate-pulse"})]}),e.jsxs("button",{onClick:n,className:`control-button hover-lift ${s.autoAccept?"active":"inactive"}`,children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(Z,{className:`w-5 h-5 ${s.autoAccept?"animate-pulse":""}`}),e.jsx("span",{className:"font-semibold",children:"Auto Accept"})]}),s.autoAccept&&e.jsx("div",{className:"status-warning animate-pulse"})]})]})]}),e.jsx("div",{className:"control-section",children:e.jsxs("button",{onClick:()=>b(!p),className:"w-full btn btn-secondary hover-lift",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-8 h-8 gradient-primary rounded-xl flex items-center justify-center shadow-sm",children:e.jsx(Ee,{className:"w-4 h-4 text-white"})}),e.jsx("span",{className:"font-bold",children:"Advanced Settings"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("div",{className:"badge badge-primary",children:[s.blacklistKeywords.length," filters"]}),p?e.jsx(he,{className:"w-5 h-5"}):e.jsx(xe,{className:"w-5 h-5"})]})]})}),p&&e.jsxs("div",{className:"control-section animate-slide-down",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-4",children:[e.jsx("div",{className:"w-8 h-8 bg-amber-500 rounded-xl flex items-center justify-center shadow-sm",children:e.jsx(X,{className:"w-4 h-4 text-white"})}),e.jsx("h3",{className:"text-lg font-bold text-slate-800",children:"Notifications"})]}),e.jsxs("div",{className:"control-grid",children:[e.jsxs("button",{onClick:C,className:`control-button hover-lift ${s.notificationsEnabled?"active":"inactive"}`,children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[s.notificationsEnabled?e.jsx(X,{className:"w-5 h-5"}):e.jsx(oe,{className:"w-5 h-5"}),e.jsx("span",{className:"font-semibold",children:"Alerts"})]}),s.notificationsEnabled&&e.jsx("div",{className:"status-warning animate-pulse"})]}),e.jsxs("button",{onClick:_,className:`control-button hover-lift ${s.soundEnabled?"active":"inactive"}`,children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[s.soundEnabled?e.jsx(Ke,{className:"w-5 h-5"}):e.jsx(Be,{className:"w-5 h-5"}),e.jsx("span",{className:"font-semibold",children:"Sound"})]}),s.soundEnabled&&e.jsx("div",{className:"status-warning animate-pulse"})]})]})]}),e.jsxs("div",{className:"space-y-4 mt-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-8 h-8 bg-red-500 rounded-xl flex items-center justify-center shadow-sm",children:e.jsx(L,{className:"w-4 h-4 text-white"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-bold text-slate-800",children:"Blacklist"}),e.jsxs("p",{className:"text-sm text-slate-600",children:[s.blacklistKeywords.length," keywords"]})]})]}),e.jsxs("button",{onClick:()=>u(!c),className:"btn btn-primary hover-lift",children:[e.jsx(Y,{className:"w-4 h-4"}),e.jsx("span",{children:"Add"})]})]}),c&&e.jsxs("div",{className:"space-y-4 animate-slide-down",children:[e.jsxs("div",{className:"flex space-x-3",children:[e.jsx("input",{type:"text",value:h,onChange:x=>d(x.target.value),onKeyDown:$,placeholder:i?"Add another keyword or press Enter to finish...":"Enter keyword...",className:`input-glass flex-1 ${i?"border-amber-400 focus:border-amber-500":"border-slate-200 focus:border-primary-500"}`,autoFocus:!0}),e.jsx("button",{onClick:l,disabled:!h.trim(),className:"btn btn-success hover-lift disabled:opacity-50 disabled:cursor-not-allowed",children:e.jsx(P,{className:"w-4 h-4"})}),i?e.jsx("button",{onClick:t,className:"bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2.5 rounded-xl transition-colors duration-200 shadow-sm",title:"Finish grouping",children:e.jsx(Y,{className:"w-4 h-4"})}):null,e.jsx("button",{onClick:()=>{i?w():(d(""),u(!1))},className:"bg-slate-200 hover:bg-slate-300 text-slate-600 px-4 py-2.5 rounded-xl transition-colors duration-200 border border-slate-200 shadow-sm",children:e.jsx(T,{className:"w-4 h-4"})})]}),i&&e.jsxs("div",{className:"bg-yellow-50 border border-yellow-200 rounded-xl p-3",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("span",{className:"text-sm font-medium text-yellow-800",children:"Grouping Mode Active"}),e.jsxs("span",{className:"text-xs text-yellow-600",children:[j.length," keyword(s) in group"]})]}),e.jsx("p",{className:"text-xs text-yellow-700 mb-3",children:"Type keywords and press Enter to add them. Press Enter on empty input to finish grouping."}),j.length>0&&e.jsxs("div",{className:"space-y-1",children:[e.jsx("span",{className:"text-xs text-yellow-800 font-medium",children:"Keywords in group:"}),e.jsx("div",{className:"flex flex-wrap gap-1",children:j.map((x,g)=>e.jsxs("div",{className:"flex items-center space-x-1 bg-yellow-200 rounded-lg px-2 py-1 text-xs text-yellow-800",children:[e.jsx("span",{children:x}),e.jsx("button",{onClick:()=>M(x),className:"text-yellow-600 hover:text-yellow-800 transition-colors duration-200",children:e.jsx(T,{className:"w-3 h-3"})})]},g))})]})]})]}),s.blacklistKeywords.length>0&&e.jsx("div",{className:"max-h-32 overflow-y-auto space-y-2 custom-scrollbar-modern",children:s.blacklistKeywords.map((x,g)=>e.jsxs("div",{className:"flex items-center justify-between bg-white rounded-lg px-3 py-2 border border-slate-200 shadow-sm",children:[e.jsx("span",{className:"text-sm text-slate-700",children:x}),e.jsx("button",{onClick:()=>S(x),className:"text-red-500 hover:text-red-600 transition-colors duration-200 p-1 rounded hover:bg-red-50",children:e.jsx(Re,{className:"w-3 h-3"})})]},g))}),s.blacklistKeywords.length===0&&e.jsx("div",{className:"text-center py-4 text-slate-500 text-sm",children:"No keywords added"})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("h3",{className:"text-sm font-semibold text-slate-700 flex items-center space-x-2",children:[e.jsx(L,{className:"w-4 h-4"}),e.jsx("span",{children:"Smart Matching"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-slate-600",children:"Enable fuzzy matching"}),e.jsx("button",{onClick:()=>r({...s,fuzzyMatching:{...s.fuzzyMatching,enabled:!s.fuzzyMatching.enabled}}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200 ${s.fuzzyMatching.enabled?"bg-emerald-500":"bg-slate-300"}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200 shadow-sm ${s.fuzzyMatching.enabled?"translate-x-6":"translate-x-1"}`})})]}),s.fuzzyMatching.enabled&&e.jsxs("div",{className:"space-y-3 bg-white rounded-xl p-4 border border-slate-200 shadow-sm",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-slate-600",children:"Match Threshold"}),e.jsx("span",{className:"text-xs text-emerald-600 font-mono bg-emerald-50 px-2 py-1 rounded",children:s.fuzzyMatching.threshold})]}),e.jsx("input",{type:"range",min:"0.1",max:"1.0",step:"0.1",value:s.fuzzyMatching.threshold,onChange:x=>r({...s,fuzzyMatching:{...s.fuzzyMatching,threshold:parseFloat(x.target.value)}}),className:"w-full h-2 bg-slate-200 rounded-lg appearance-none cursor-pointer slider"}),e.jsxs("div",{className:"flex justify-between text-xs text-slate-500",children:[e.jsx("span",{children:"Strict"}),e.jsx("span",{children:"Loose"})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-slate-600",children:"Use payment aliases"}),e.jsx("button",{onClick:()=>r({...s,fuzzyMatching:{...s.fuzzyMatching,enableAliases:!s.fuzzyMatching.enableAliases}}),className:`relative inline-flex h-5 w-9 items-center rounded-full transition-colors duration-200 ${s.fuzzyMatching.enableAliases?"bg-emerald-500":"bg-slate-300"}`,children:e.jsx("span",{className:`inline-block h-3 w-3 transform rounded-full bg-white transition-transform duration-200 shadow-sm ${s.fuzzyMatching.enableAliases?"translate-x-5":"translate-x-1"}`})})]}),e.jsxs("div",{className:"text-xs text-blue-700 bg-blue-50 border border-blue-200 rounded-lg p-2",children:[e.jsx("span",{className:"text-blue-800 font-medium",children:"💡 Tip:"})," Fuzzy matching helps catch AirTM payment methods with slight variations in naming."]})]})]}),e.jsx("div",{className:"pt-4 border-t border-slate-200/60",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-8 h-8 gradient-primary rounded-xl flex items-center justify-center shadow-sm",children:e.jsx(le,{className:"w-4 h-4 text-white"})}),e.jsxs("div",{children:[e.jsxs("span",{className:"text-sm font-bold text-slate-800",children:["Status: ",s.monitoring?"Active":"Paused"]}),e.jsx("div",{className:"text-xs text-slate-600",children:s.monitoring?"Real-time monitoring enabled":"Monitoring is stopped"})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:`status-dot ${s.monitoring?"status-online":"status-offline"} animate-pulse`}),e.jsx("span",{className:"text-sm font-semibold text-slate-700",children:s.monitoring?"Monitoring":"Stopped"})]})]})})]})]})},qe=({stats:s,filteredCount:r,isFiltered:m})=>{const f=[{label:"Total",value:s.total,icon:F,color:"text-blue-400",bgColor:"bg-blue-500/10",borderColor:"border-blue-500/20"},{label:"New",value:s.new,icon:ve,color:"text-emerald-400",bgColor:"bg-emerald-500/10",borderColor:"border-emerald-500/20"},{label:"Accepted",value:s.accepted,icon:fe,color:"text-green-400",bgColor:"bg-green-500/10",borderColor:"border-green-500/20"},{label:"Rejected",value:s.rejected,icon:ye,color:"text-red-400",bgColor:"bg-red-500/10",borderColor:"border-red-500/20"}];return e.jsxs("div",{className:"space-y-4",children:[m&&e.jsxs("div",{className:"flex items-center justify-between bg-emerald-50/80 border border-emerald-200/60 rounded-xl px-4 py-3 backdrop-blur-sm",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-8 h-8 bg-emerald-500 rounded-lg flex items-center justify-center shadow-sm",children:e.jsx(L,{className:"w-4 h-4 text-white"})}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm font-semibold text-emerald-700",children:"Filter Active"}),e.jsx("p",{className:"text-xs text-emerald-600",children:"Custom criteria applied"})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"text-lg font-bold text-emerald-700",children:r}),e.jsxs("div",{className:"text-xs text-emerald-600",children:["of ",s.total," offers"]})]})]}),e.jsx("div",{className:"grid grid-cols-2 gap-4",children:f.map((a,p)=>{const b=a.icon;return e.jsxs("div",{className:"bg-white/60 backdrop-blur-sm border border-slate-200/60 rounded-xl p-4 hover:bg-white/80 hover:border-slate-300/60 transition-all duration-300 hover:shadow-lg hover:-translate-y-0.5 group",style:{animationDelay:`${p*100}ms`},children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsx("div",{className:`w-10 h-10 ${a.bgColor} rounded-xl flex items-center justify-center shadow-sm group-hover:scale-110 transition-transform duration-300`,children:e.jsx(b,{className:`w-5 h-5 ${a.color}`})}),e.jsx("div",{className:`w-3 h-3 ${a.color.replace("text-","bg-")} rounded-full opacity-60 group-hover:opacity-100 transition-opacity duration-300`})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:`text-2xl font-bold ${a.color} leading-none`,children:a.value.toLocaleString()}),e.jsx("div",{className:"text-sm text-slate-600 font-medium",children:a.label})]}),e.jsx("div",{className:"mt-3 h-2 bg-slate-200/60 rounded-full overflow-hidden",children:e.jsx("div",{className:`h-full ${a.color.replace("text-","bg-")} transition-all duration-1000 ease-out rounded-full`,style:{width:s.total>0?`${a.value/s.total*100}%`:"0%",animationDelay:`${p*200+500}ms`}})})]},a.label)})}),e.jsx("div",{className:"bg-slate-50/60 backdrop-blur-sm border border-slate-200/60 rounded-xl p-4",children:e.jsxs("div",{className:"flex items-center justify-between text-sm",children:[e.jsxs("div",{className:"flex items-center space-x-2 text-slate-600",children:[e.jsx(Q,{className:"w-4 h-4"}),e.jsxs("span",{className:"font-medium",children:["Last updated: ",new Date().toLocaleTimeString()]})]}),e.jsx("div",{className:"flex items-center space-x-4 text-slate-700",children:s.total>0&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-lg font-bold text-green-600",children:[Math.round(s.accepted/(s.accepted+s.rejected)*100)||0,"%"]}),e.jsx("div",{className:"text-xs text-slate-500",children:"Success Rate"})]}),e.jsx("div",{className:"w-px h-8 bg-slate-300"}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-lg font-bold text-blue-600",children:s.total-s.accepted-s.rejected}),e.jsx("div",{className:"text-xs text-slate-500",children:"Pending"})]})]})})]})})]})};function We(){const[s,r]=N.useState([]),[m,f]=N.useState(null),[a,p]=N.useState(null),[b,h]=N.useState(!0),[d,c]=N.useState(null),[u,j]=N.useState(null);N.useEffect(()=>{y(),i()},[]);const y=async()=>{var l;h(!0);try{console.log("Popup: Loading initial data...");const t=await chrome.runtime.sendMessage({type:"GET_POPUP_DATA"});console.log("Popup: Received response:",t),t&&t.success&&t.data?(console.log("Popup: Setting offers:",((l=t.data.offers)==null?void 0:l.length)||0),console.log("Popup: Setting settings:",t.data.settings),console.log("Popup: Setting stats:",t.data.stats),r(t.data.offers||[]),f(t.data.settings||null),p(t.data.stats||{totalOffers:0,newOffers:0,averageRate:0})):(console.error("Popup: Invalid response from background:",t),c((t==null?void 0:t.error)||"Failed to load data from background script"))}catch(t){console.error("Popup: Error loading initial data:",t),c("Failed to communicate with extension")}finally{h(!1)}},i=()=>{const l=t=>{t.type==="OFFERS_UPDATED"&&(r(t.offers),p(t.stats))};return chrome.runtime.onMessage.addListener(l),()=>chrome.runtime.onMessage.removeListener(l)},v=async l=>{try{const t=await chrome.runtime.sendMessage({type:"ACCEPT_OFFER",offerId:l.id});t.success||c(t.error||"Failed to accept offer")}catch{c("Failed to accept offer")}},k=async l=>{try{const t=await chrome.runtime.sendMessage({type:"REJECT_OFFER",offerId:l.id});t.success||c(t.error||"Failed to reject offer")}catch{c("Failed to reject offer")}},n=async l=>{try{const t=await chrome.runtime.sendMessage({type:"UPDATE_SETTINGS",settings:l});t.success?f(l):c(t.error||"Failed to update settings")}catch{c("Failed to update settings")}},C=()=>{chrome.runtime.openOptionsPage()},_=()=>{y()};return b?e.jsxs("div",{className:"w-[520px] h-[680px] bg-gradient-to-br from-white via-gray-50 to-blue-50 flex items-center justify-center relative overflow-hidden",children:[e.jsx("div",{className:"absolute inset-0 opacity-30",children:e.jsx("div",{className:"absolute inset-0",style:{backgroundImage:`
              radial-gradient(circle at 25% 25%, rgba(99, 102, 241, 0.08) 0%, transparent 50%),
              radial-gradient(circle at 75% 75%, rgba(168, 85, 247, 0.08) 0%, transparent 50%)
            `}})}),e.jsx("div",{className:"absolute inset-0 bg-white/50 backdrop-blur-sm"}),e.jsxs("div",{className:"relative z-10 text-center",children:[e.jsx("div",{className:"w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-3xl flex items-center justify-center shadow-xl mb-6 mx-auto border border-white/30",children:e.jsx("span",{className:"text-white font-bold text-2xl",children:"A"})}),e.jsx("div",{className:"relative mb-6",children:e.jsxs("div",{className:"w-20 h-20 mx-auto",children:[e.jsx("div",{className:"absolute inset-0 rounded-full border-4 border-gray-200/60"}),e.jsx("div",{className:"absolute inset-0 rounded-full border-4 border-transparent border-t-indigo-500 animate-spin"}),e.jsx("div",{className:"absolute inset-2 rounded-full border-3 border-transparent border-t-purple-400 animate-spin",style:{animationDirection:"reverse",animationDuration:"2s"}})]})}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("h2",{className:"text-xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent",children:"Airtm Monitor Pro"}),e.jsx("p",{className:"text-gray-600 font-medium animate-pulse",children:"Loading offers..."}),e.jsxs("div",{className:"flex items-center justify-center space-x-2 mt-4",children:[e.jsx("div",{className:"w-2 h-2 bg-indigo-500 rounded-full animate-bounce"}),e.jsx("div",{className:"w-2 h-2 bg-purple-500 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),e.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]})]})]})]}):e.jsxs("div",{className:"w-[600px] h-[850px] bg-gradient-to-br from-slate-50 via-blue-50 to-emerald-50 relative overflow-hidden",children:[e.jsxs("div",{className:"absolute inset-0",children:[e.jsx("div",{className:"absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.08),transparent_50%),radial-gradient(circle_at_80%_80%,rgba(16,185,129,0.08),transparent_50%),radial-gradient(circle_at_40%_40%,rgba(99,102,241,0.05),transparent_50%)]"}),e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-white/90 via-white/60 to-white/80"})]}),e.jsxs("div",{className:"relative z-10 h-full flex flex-col",children:[e.jsx("div",{className:"bg-white/90 backdrop-blur-xl border-b border-slate-200/50 shadow-sm",children:e.jsx("div",{className:"px-6 py-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"w-12 h-12 bg-gradient-to-br from-indigo-600 via-purple-600 to-pink-600 rounded-2xl flex items-center justify-center shadow-lg border border-white/20",children:e.jsx("span",{className:"text-white font-bold text-xl",children:"A"})}),e.jsx("div",{className:"absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white shadow-sm animate-pulse"})]}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent",children:"Airtm Monitor Pro"}),e.jsx("p",{className:"text-sm text-slate-500 font-medium",children:"Real-time Trading Dashboard"})]})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("button",{onClick:_,className:"group relative px-4 py-2.5 bg-white/80 border border-slate-200/80 rounded-xl hover:bg-white hover:border-indigo-300/60 transition-all duration-300 shadow-sm hover:shadow-md",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("svg",{className:"w-4 h-4 text-indigo-600 group-hover:rotate-180 transition-transform duration-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),e.jsx("span",{className:"text-sm font-medium text-slate-700",children:"Refresh"})]})}),e.jsx("button",{onClick:C,className:"group relative px-4 py-2.5 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-xl hover:from-indigo-700 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("svg",{className:"w-4 h-4 group-hover:rotate-90 transition-transform duration-300",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]}),e.jsx("span",{className:"text-sm font-medium",children:"Settings"})]})})]})]})})}),d&&e.jsx("div",{className:"mx-6 mt-4 animate-in slide-in-from-top duration-300",children:e.jsx("div",{className:"bg-red-50/90 backdrop-blur-sm border border-red-200/60 rounded-xl p-4 shadow-sm",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx("div",{className:"w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center shadow-sm flex-shrink-0 mt-0.5",children:e.jsx("svg",{className:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h4",{className:"text-sm font-semibold text-red-800 mb-1",children:"Error"}),e.jsx("p",{className:"text-sm text-red-700",children:d})]})]}),e.jsx("button",{onClick:()=>c(null),className:"text-red-400 hover:text-red-600 transition-colors duration-200 p-1 rounded-lg hover:bg-red-100/50",children:e.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})})}),e.jsx("div",{className:"px-6 py-4",children:e.jsx("div",{className:"bg-white/70 backdrop-blur-xl border border-slate-200/50 rounded-2xl shadow-sm",children:e.jsx("div",{className:"p-4",children:e.jsx(qe,{stats:a?{total:a.totalOffers,new:a.newOffers,accepted:a.acceptedOffers,rejected:a.rejectedOffers}:{total:0,new:0,accepted:0,rejected:0}})})})}),e.jsx("div",{className:"flex-1 px-6 pb-4 min-h-0",children:e.jsx("div",{className:"h-full card-glass overflow-hidden animate-fade-in",children:e.jsxs("div",{className:"h-full flex flex-col",children:[e.jsx("div",{className:"px-6 py-4 border-b border-slate-200/60 gradient-surface",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-8 h-8 gradient-secondary rounded-xl flex items-center justify-center shadow-sm",children:e.jsx("div",{className:"w-4 h-4 bg-white rounded-sm"})}),e.jsx("h3",{className:"text-lg font-bold text-slate-800",children:"Available Offers"})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsxs("div",{className:"flex items-center space-x-2 px-3 py-1.5 glass-effect rounded-full border border-white/40",children:[e.jsx("div",{className:"status-online animate-pulse"}),e.jsx("span",{className:"text-sm font-semibold text-slate-700",children:"Live Updates"})]}),e.jsxs("div",{className:"px-3 py-1.5 badge badge-primary",children:[s.length," offers"]})]})]})}),e.jsx("div",{className:"flex-1 overflow-hidden",children:e.jsx("div",{className:"h-full overflow-y-auto scrollbar-modern",children:e.jsx("div",{className:"p-5",children:e.jsx(He,{offers:s,selectedOffer:u,onOfferSelect:j,settings:m||{monitoring:!1,telegramBotToken:"",telegramChatId:"",webhookUrl:"",soundEnabled:!1,notificationsEnabled:!1,autoAccept:!1,minAmount:0,maxAmount:999999,preferredCurrencies:[],keywords:[],blacklistKeywords:[],paymentMethods:[],countries:[],hotkeys:{accept_offer:{key:"Enter",description:"Accept selected offer"},reject_offer:{key:"Delete",description:"Reject selected offer"},open_offer_details:{key:"Ctrl+Shift+D",description:"Open offer details"},cycle_offers:{key:"Ctrl+Shift+C",description:"Cycle offers"}},fuzzyMatching:{enabled:!1,threshold:.8,enableAliases:!0,customAliases:{}}}})})})})]})})}),e.jsx("div",{className:"px-6 py-3",children:e.jsx(Ue,{settings:m||{monitoring:!1,telegramBotToken:"",telegramChatId:"",webhookUrl:"",soundEnabled:!1,notificationsEnabled:!1,autoAccept:!1,minAmount:0,maxAmount:999999,preferredCurrencies:[],keywords:[],blacklistKeywords:[],paymentMethods:[],countries:[],hotkeys:{accept_offer:{key:"Enter",description:"Accept selected offer"},reject_offer:{key:"Delete",description:"Reject selected offer"},open_offer_details:{key:"Ctrl+Shift+D",description:"Open offer details"},cycle_offers:{key:"Ctrl+Shift+C",description:"Cycle offers"}},fuzzyMatching:{enabled:!1,threshold:.8,enableAliases:!0,customAliases:{}}},onSettingsUpdate:n,onAcceptOffer:l=>{const t=s.find(w=>w.id===l);t&&v(t)},onRejectOffer:l=>{const t=s.find(w=>w.id===l);t&&k(t)},selectedOffer:u})})]})]})}const ee=document.getElementById("root");if(!ee)throw new Error("Root element not found");const Ye=se(ee);Ye.render(e.jsx(We,{}));
