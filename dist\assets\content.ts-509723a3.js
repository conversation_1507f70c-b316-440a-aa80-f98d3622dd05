import{i as m,s as h}from"./extension-context-ed07ef7d.js";function k(e){if(console.log("Validating AvailableOperations query format and payload structure"),!m())return console.warn("⚠️ Extension context invalidated, skipping AvailableOperations parsing"),[];if(!e?.availableOperations||!Array.isArray(e.availableOperations))throw console.warn("Invalid AvailableOperations GraphQL query payload structure:",e),new Error("Invalid AvailableOperations query format: missing or invalid availableOperations array");console.log("AvailableOperations query format validation successful");const t=[];for(let r=0;r<e.availableOperations.length;r++)try{if(!m()){console.warn("⚠️ Extension context invalidated during offer parsing, stopping");break}const o=e.availableOperations[r];console.log(`🔍 Raw offer ${r} structure:`,JSON.stringify(o,null,2));const n=oe(o);t.push(n)}catch(o){const n=o instanceof Error?o.message:String(o);if(n.includes("Extension context invalidated")||n.includes("context invalidated")){console.warn("⚠️ Extension context invalidated during offer parsing");break}console.error(`Failed to parse offer at index ${r}:`,o),console.error("Raw offer data:",JSON.stringify(e.availableOperations[r],null,2));continue}return console.log(`Successfully parsed ${t.length} offers from ${e.availableOperations.length} raw offers using AvailableOperations query format`),t}function oe(e){if(!e||typeof e!="object")throw new Error("Invalid offer: not an object");const{id:t,hash:r,operationType:o,status:n,isMine:a,createdAt:s,updatedAt:i,grossAmount:c,netAmount:d,metadata:p,walletCurrency:f,peer:O,rate:R,rateInfo:D,displayRate:N,currency:$,makerPaymentMethod:P,__typename:te}=e;if(typeof t!="string"||!t.trim())throw new Error("Invalid offer: missing or invalid id");if(typeof r!="string"||!r.trim())throw new Error(`Invalid offer ${t}: missing or invalid hash`);if(o!=="BUY"&&o!=="SELL")throw new Error(`Invalid offer ${t}: operationType must be BUY or SELL, got ${o}`);if(typeof n!="string"||!n.trim())throw new Error(`Invalid offer ${t}: missing or invalid status`);const M=["CREATED","ACCEPTED","REJECTED","COMPLETED","CANCELLED"];if(!M.includes(n))throw new Error(`Invalid offer ${t}: status must be one of ${M.join(", ")}, got ${n}`);if(typeof a!="boolean")throw new Error(`Invalid offer ${t}: isMine must be boolean, got ${typeof a}`);if(typeof s!="string"||!s.trim())throw new Error(`Invalid offer ${t}: missing or invalid createdAt`);if(i!==null&&(typeof i!="string"||!i.trim()))throw new Error(`Invalid offer ${t}: missing or invalid updatedAt`);if(typeof c!="string"||!c.trim())throw new Error(`Invalid offer ${t}: grossAmount must be a non-empty string`);if(typeof d!="string"||!d.trim())throw new Error(`Invalid offer ${t}: netAmount must be a non-empty string`);if(!f||typeof f!="object")throw new Error(`Invalid offer ${t}: missing or invalid walletCurrency`);if(!O||typeof O!="object")throw new Error(`Invalid offer ${t}: missing or invalid peer`);return{id:t,hash:r,operationType:o,status:n,isMine:a,createdAt:s,updatedAt:i,grossAmount:c,netAmount:d,metadata:p||{},walletCurrency:q(f,`${t}.walletCurrency`),peer:re(O,`${t}.peer`),rate:R?String(R):void 0,rateInfo:D?ne(D,t):void 0,displayRate:N?ae(N,t):void 0,currency:$?q($,`${t}.currency`):void 0,makerPaymentMethod:P?ie(P,`${t}.makerPaymentMethod`):void 0,__typename:te||"Operations__Unknown"}}function F(e,t,r){if(typeof e!="string"||!e.trim())throw new Error(`Invalid offer ${r}: ${t} must be a non-empty string`);return e.trim()}function q(e,t){if(!e||typeof e!="object")throw new Error(`Invalid ${t}: not an object`);const{id:r,symbol:o,precision:n,__typename:a}=e;if(typeof r!="string"||!r.trim())throw new Error(`Invalid ${t}: missing or invalid id`);if(typeof o!="string"||!o.trim())throw new Error(`Invalid ${t}: missing or invalid symbol`);if(typeof n!="number"||n<0||!Number.isInteger(n))throw new Error(`Invalid ${t}: precision must be a non-negative integer, got ${n}`);return a&&a!=="Catalogs__Currency"&&console.warn(`${t}: unexpected __typename '${a}', expected 'Catalogs__Currency'`),{id:r.trim(),symbol:o.trim(),precision:n,__typename:a||"Catalogs__Currency"}}function re(e,t){if(!e||typeof e!="object")throw new Error(`Invalid ${t}: not an object`);const{id:r,firstName:o,lastName:n,createdAt:a,country:s,countryInfo:i,securityHub:c,numbers:d,preferences:p,__typename:f}=e;if(typeof r!="string"||!r.trim())throw new Error(`Invalid ${t}: missing or invalid id`);if(typeof o!="string")throw new Error(`Invalid ${t}: firstName must be a string, got ${typeof o}`);if(typeof n!="string")throw new Error(`Invalid ${t}: lastName must be a string, got ${typeof n}`);if(typeof a!="string"||!a.trim())throw new Error(`Invalid ${t}: createdAt must be a non-empty string`);if(typeof s!="string")throw new Error(`Invalid ${t}: country must be a string, got ${typeof s}`);if(!i||typeof i!="object")throw new Error(`Invalid ${t}: countryInfo is required and must be an object`);if(!c||typeof c!="object")throw new Error(`Invalid ${t}: securityHub is required and must be an object`);if(typeof c.verificationStatusName!="string"&&console.warn(`${t}: securityHub.verificationStatusName should be a string, got ${typeof c.verificationStatusName}`),!d||typeof d!="object")throw new Error(`Invalid ${t}: numbers is required and must be an object`);if(!p||typeof p!="object")throw new Error(`Invalid ${t}: preferences is required and must be an object`);return f&&!["Auth__OperationUser","Auth__User"].includes(f)&&console.warn(`${t}: unexpected __typename '${f}', expected 'Auth__OperationUser' or 'Auth__User'`),e}function ne(e,t){if(!e||typeof e!="object")throw new Error(`Invalid offer ${t}: rateInfo is not an object`);const{id:r,netAmount:o,grossAmount:n,fundsToReceiveMaker:a,fundsToReceiveTaker:s,fundsToSendMaker:i,fundsToSendTaker:c,__typename:d}=e;return{id:r||t,netAmount:F(o,"rateInfo.netAmount",t),grossAmount:F(n,"rateInfo.grossAmount",t),fundsToReceiveMaker:a!==null?typeof a=="string"?a:String(a):null,fundsToReceiveTaker:s!==null?typeof s=="string"?s:String(s):null,fundsToSendMaker:i!==null?typeof i=="string"?i:String(i):null,fundsToSendTaker:c!==null?typeof c=="string"?c:String(c):null,__typename:d||"Operations__RateInfo"}}function ae(e,t){if(!e||typeof e!="object")throw new Error(`Invalid offer ${t}: displayRate is not an object`);const{direction:r,rate:o,__typename:n}=e,a=["TO_LOCAL_CURRENCY","FROM_LOCAL_CURRENCY","TO_WALLET_CURRENCY"];return a.includes(r)||console.warn(`Invalid direction '${r}', defaulting to 'TO_LOCAL_CURRENCY'`),o!=null&&typeof o!="string"&&typeof o!="number"&&console.warn(`Invalid rate type '${typeof o}', converting to string`),{direction:a.includes(r)?r:"TO_LOCAL_CURRENCY",rate:o?String(o):"0",__typename:n||"Operations__DisplayRate"}}function ie(e,t){if(!e||typeof e!="object")throw new Error(`Invalid ${t}: not an object`);const{id:r,categoryId:o,isThirdPartyInstance:n,version:a}=e;if(typeof r!="string"||!r.trim())throw new Error(`Invalid ${t}: missing or invalid id`);if(typeof o!="string"||!o.trim())throw new Error(`Invalid ${t}: missing or invalid categoryId`);if(typeof n!="boolean")throw new Error(`Invalid ${t}: isThirdPartyInstance must be boolean, got ${typeof n}`);if(!a||typeof a!="object")throw new Error(`Invalid ${t}: version is required and must be an object`);return e}function se(){try{const e=["airtm_token","auth_token","access_token","bearer_token","authToken","accessToken","bearerToken","jwt","JWT","token","authorization","auth","session_token"];for(const o of e){const n=localStorage.getItem(o);if(n&&n.length>10)return console.log(`Bearer token found in localStorage: ${o}`),n}for(const o of e){const n=sessionStorage.getItem(o);if(n&&n.length>10)return console.log(`Bearer token found in sessionStorage: ${o}`),n}const t=document.cookie.split(";");for(const o of t){const[n,a]=o.trim().split("=");if((n.toLowerCase().includes("token")||n.toLowerCase().includes("auth"))&&a&&a.length>10)return console.log(`Bearer token found in cookie: ${n}`),a}const r=document.querySelectorAll("script");for(const o of r){const n=o.textContent||"",a=[/(?:token|bearer|authorization)["']?\s*:\s*["']([^"'\s]{20,})["']/i,/Authorization["']?\s*:\s*["']Bearer\s+([^"'\s]+)["']/i,/["']token["']\s*:\s*["']([^"'\s]{20,})["']/i,/window\.__INITIAL_STATE__.*["']token["']\s*:\s*["']([^"'\s]+)["']/i];for(const s of a){const i=n.match(s);if(i&&i[1])return console.log("Bearer token found in script content"),i[1]}}return typeof window<"u"&&window.__auth_token?(console.log("Bearer token found in window.__auth_token"),window.__auth_token):(console.log("No bearer token found in any location"),null)}catch(e){return console.error("Error extracting bearer token:",e),null}}const x=["AvailableOperations","GetBalance","GetTransactions","CreateOperation","AcceptOperation","OperationDetails","UnmatchOperationMutation"],z="https://app.airtm.com/graphql",w=new Map,Q=2e3;function ce(){if(console.log("🔧 Initializing XHR interceptor..."),typeof window>"u"||!window.XMLHttpRequest){console.warn("❌ XHR interceptor: XMLHttpRequest not available");return}const e=window.XMLHttpRequest,t=e.prototype.open,r=e.prototype.send;e.prototype.open=function(o,n,a,s,i){const c=typeof n=="string"?n:n.toString();return console.log("🌐 XHR open called for URL:",c),this._interceptedMethod=o,this._interceptedUrl=c,t.call(this,o,n,a??!0,s,i)},e.prototype.send=function(o){const n=this._interceptedMethod,a=this._interceptedUrl;if(console.log("📤 XHR send called:",n,a),W(a,{method:n,body:o})){console.log("🎯 Intercepting Airtm GraphQL XHR request:",a);const s=this.onreadystatechange;this.onreadystatechange=function(i){if(this.readyState===4&&this.status===200){console.log("📥 XHR GraphQL response received, status:",this.status);try{const c=this.responseText;if(c){console.log("📄 XHR Response preview:",c.substring(0,200)+"...");try{l(o||"")}catch(d){console.error("❌ Error processing XHR GraphQL response:",d)}}}catch(c){console.error("❌ Error parsing XHR response:",c)}}if(s)return s.apply(this,[i])}}else console.log("⏭️ Skipping non-GraphQL XHR request:",a);return r.apply(this,[o])},console.log("✅ XHR interceptor initialized")}function H(){if(console.log("🔧 Initializing all interceptors (fetch, XHR, WebSocket, DOM observer)..."),typeof window>"u"){console.warn("❌ Fetch interceptor: window not available");return}if(console.log("✅ Window is available"),console.log("📋 Monitored operations:",x),console.log("🌐 Target GraphQL endpoint:",z),window.fetch){const e=window.fetch;window.fetch=be(e),console.log("✅ Fetch interceptor initialized")}else console.warn("⚠️ window.fetch not available");ce(),le(),de(),fe(),ue(),he(),console.log("🎉 All interceptors and monitoring techniques initialized successfully"),setTimeout(()=>{console.log("📊 Extension Status Check:"),console.log("- Fetch interceptor:",typeof window.fetch=="function"?"✅":"❌"),console.log("- XHR interceptor:",typeof XMLHttpRequest<"u"?"✅":"❌"),console.log("- WebSocket interceptor:",typeof WebSocket<"u"?"✅":"❌"),console.log("- DOM observer:",typeof MutationObserver<"u"?"✅":"❌"),console.log("- Performance API:",typeof PerformanceObserver<"u"?"✅":"❌"),console.log("- Service Worker:","serviceWorker"in navigator?"✅":"❌"),console.log("- Global function:",typeof window.airtmExtensionReceiveData=="function"?"✅":"❌"),console.log("🔍 If you still don't see offers, check the Network tab in DevTools for GraphQL requests")},1e3),console.log("🔍 Ready to intercept GraphQL requests!")}function le(){if(console.log("🔧 Initializing WebSocket interceptor..."),typeof WebSocket<"u"){const e=WebSocket;window.WebSocket=function(t,r){console.log("🔌 WebSocket connection intercepted:",t);const o=new e(t,r),n=o.addEventListener;return o.addEventListener=function(a,s,i){if(a==="message"){const c=d=>{const p=d;console.log("📨 WebSocket message received:",p.data);try{const f=JSON.parse(p.data);f&&(f.data||f.errors)&&(console.log("🎯 Potential GraphQL data via WebSocket:",f),l(p.data))}catch{}if(typeof s=="function")return s.call(o,d);if(s&&typeof s.handleEvent=="function")return s.handleEvent(d)};return n.call(this,a,c,i)}return n.call(this,a,s,i)},o},console.log("✅ WebSocket interceptor initialized")}else console.warn("⚠️ WebSocket not available")}function de(){console.log("🔧 Initializing DOM observer..."),typeof MutationObserver<"u"?(new MutationObserver(t=>{t.forEach(r=>{if(r.type==="childList"&&r.addedNodes.forEach(o=>{if(o.nodeType===Node.ELEMENT_NODE){const n=o;if(n.textContent&&n.textContent.includes("availableOperations")){console.log("🎯 DOM element with availableOperations detected:",n);try{const i=n.textContent.match(/\{.*availableOperations.*\}/);i&&l(i[0])}catch{console.log("❌ Failed to parse DOM content as JSON")}}if(n.tagName==="SCRIPT"&&n.textContent)try{const i=JSON.parse(n.textContent);i&&i.data&&i.data.availableOperations&&(console.log("🎯 Script tag with availableOperations detected"),l(n.textContent||""))}catch{}n.getAttributeNames().filter(i=>i.startsWith("data-")).forEach(i=>{const c=n.getAttribute(i);c&&(c.includes("availableOperations")||c.includes("graphql"))&&(console.log("📄 Found GraphQL data in data attribute:",i),l(c))}),Object.keys(n).filter(i=>i.startsWith("__react")).forEach(i=>{try{const c=n[i];if(c){const d=JSON.stringify(c);d.includes("availableOperations")&&(console.log("🎯 Found GraphQL data in React component:",i),l(d))}}catch{}})}}),r.type==="attributes"){const n=r.target.getAttribute(r.attributeName||"");n&&n.includes("availableOperations")&&(console.log("📄 Found GraphQL data in modified attribute:",r.attributeName),l(n))}})}).observe(document.body,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["data-state","data-props","data-apollo","data-graphql"]}),console.log("✅ DOM observer initialized")):console.warn("⚠️ MutationObserver not available"),setInterval(()=>{document.querySelectorAll("script").forEach((r,o)=>{r.textContent&&r.textContent.includes("availableOperations")&&!r.scanned&&(console.log(`🔍 Periodic scan found GraphQL data in script ${o}`),l(r.textContent),r.scanned=!0)}),document.querySelectorAll("[data-state], [data-props], [data-apollo], [data-graphql]").forEach((r,o)=>{["data-state","data-props","data-apollo","data-graphql"].forEach(a=>{const s=r.getAttribute(a);s&&s.includes("availableOperations")&&!r[a+"_scanned"]&&(console.log(`🔍 Periodic scan found GraphQL data in ${a} of element ${o}`),l(s),r[a+"_scanned"]=!0)})})},3e3)}function fe(){if(console.log("🔧 Initializing network monitor..."),typeof PerformanceObserver<"u")try{new PerformanceObserver(t=>{t.getEntries().forEach(r=>{(r.name.includes("graphql")||r.name.includes("airtm.com"))&&(console.log("🌐 Network request detected via Performance API:",r.name),r.name.includes("graphql")&&(console.log("🎯 GraphQL request detected, attempting to capture response..."),setTimeout(()=>{try{[window.__APOLLO_CLIENT__,window.__NEXT_DATA__,window.__INITIAL_STATE__,window.store,window.app].forEach((n,a)=>{if(n){console.log(`🔍 Checking cache location ${a+1}:`,n);const s=JSON.stringify(n);s.includes("availableOperations")&&(console.log("🎯 Found availableOperations in cache!"),l(s))}})}catch(o){console.warn("⚠️ Error checking caches:",o)}},100)))})}).observe({entryTypes:["resource"]}),console.log("✅ Network monitor initialized")}catch(e){console.warn("⚠️ PerformanceObserver failed to initialize:",e)}else console.warn("⚠️ PerformanceObserver not available");setInterval(()=>{performance.getEntriesByType("resource").forEach(t=>{t.name.includes("graphql")&&!t.logged&&(console.log("📊 Resource timing detected GraphQL call:",t.name),setTimeout(()=>{document.querySelectorAll("script").forEach(o=>{o.textContent&&o.textContent.includes("availableOperations")&&(console.log("🎯 Found GraphQL data in script tag!"),l(o.textContent))}),Object.keys(window).forEach(o=>{try{const n=window[o];if(n&&typeof n=="object"){const a=JSON.stringify(n);a.includes("availableOperations")&&(console.log("🎯 Found GraphQL data in window."+o),l(a))}}catch{}})},200),t.logged=!0)})},2e3)}function ue(){console.log("🔧 Initializing additional event listeners..."),window.addEventListener("airtm-data-update",e=>{console.log("🎯 Custom airtm-data-update event detected:",e.detail),e.detail&&typeof e.detail=="string"&&l(e.detail)}),window.addEventListener("storage",e=>{if(e.key&&e.key.includes("airtm")&&e.newValue){console.log("💾 Storage event with airtm data detected:",e.key,e.newValue);try{const t=JSON.parse(e.newValue);t&&t.availableOperations&&l(e.newValue)}catch{}}}),window.addEventListener("message",e=>{e.data&&typeof e.data=="object"&&(e.data.type==="airtm-graphql-response"||e.data.data&&e.data.data.availableOperations)&&(console.log("📬 PostMessage with GraphQL data detected:",e.data),l(JSON.stringify(e.data)))}),setInterval(()=>{try{for(let e=0;e<localStorage.length;e++){const t=localStorage.key(e);if(t&&t.includes("airtm")){const r=localStorage.getItem(t);r&&r.includes("availableOperations")&&(console.log("🔍 Found airtm data in localStorage:",t),l(r))}}}catch{}try{for(let e=0;e<sessionStorage.length;e++){const t=sessionStorage.key(e);if(t&&t.includes("airtm")){const r=sessionStorage.getItem(t);r&&r.includes("availableOperations")&&(console.log("🔍 Found airtm data in sessionStorage:",t),l(r))}}}catch{}},5e3),console.log("✅ Additional event listeners initialized")}function ge(){console.log("🔧 Initializing Service Worker listener..."),"serviceWorker"in navigator?(navigator.serviceWorker.addEventListener("message",e=>{e.data&&e.data.type==="network-request"&&(console.log("🔧 Service Worker network request:",e.data),e.data.url&&e.data.url.includes("graphql")&&console.log("🎯 GraphQL request detected via Service Worker"))}),navigator.serviceWorker.register("/service-worker.js").catch(()=>{console.log("ℹ️ Service Worker registration failed (expected if not available)")}),console.log("✅ Service Worker listener initialized")):console.warn("⚠️ Service Worker not available")}function pe(){if(console.log("🔧 Initializing DevTools monitor..."),window.chrome&&window.chrome.devtools)try{window.chrome.devtools.network.onRequestFinished.addListener(e=>{e.request.url.includes("graphql")&&(console.log("🔧 DevTools detected GraphQL request:",e),e.getContent(t=>{t&&l(t)}))}),console.log("✅ DevTools monitor initialized")}catch{console.log("ℹ️ DevTools API not accessible (expected in content script)")}}function me(){console.log("🔧 Initializing aggressive polling..."),setInterval(()=>{try{Object.keys(window).forEach(r=>{if(r.toLowerCase().includes("airtm")||r.toLowerCase().includes("graphql")){const o=window[r];if(o&&typeof o=="object"){const n=JSON.stringify(o);n.includes("availableOperations")&&(console.log("🔍 Found GraphQL data in window object:",r),l(n))}}});const t=document.querySelector("[data-reactroot]");if(t&&t._reactInternalFiber&&console.log("🔍 React app detected, checking for data..."),window.__APOLLO_CLIENT__){console.log("🔍 Apollo Client detected");const r=window.__APOLLO_CLIENT__;if(r.cache&&r.cache.data){const o=JSON.stringify(r.cache.data);o.includes("availableOperations")&&(console.log("🎯 Found availableOperations in Apollo cache"),l(o))}}}catch{}},3e3),console.log("✅ Aggressive polling initialized")}function he(){console.log("🚀 Initializing advanced monitoring techniques..."),ge(),pe(),me(),ye(),window.airtmExtensionReceiveData=function(e){console.log("📞 Data received via global function:",e),l(typeof e=="string"?e:JSON.stringify(e))},console.log("✅ Advanced monitoring initialized")}function ye(){console.log("🔧 Injecting page script for main world access...");const e=document.createElement("script");e.src=chrome.runtime.getURL("inject-script.js"),e.type="text/javascript",window.addEventListener("airtm-graphql-detected",t=>{console.log("📡 Received data from main world:",t.detail),t.detail&&t.detail.responseText&&l(t.detail.responseText)}),(document.head||document.documentElement).appendChild(e),e.remove(),console.log("✅ Page script injected successfully")}function be(e){return async function(t,r){const o=typeof t=="string"?t:t instanceof URL?t.href:t.url;if(console.log("🌐 Fetch interceptor called for URL:",o),W(o,r))try{console.log("🎯 Intercepting Airtm GraphQL request:",o);const n=await e(t,r),a=n.clone();return console.log("📥 GraphQL response received, status:",n.status),Le(a).catch(s=>{console.error("❌ Error processing GraphQL response:",s)}),n}catch(n){return console.error("❌ Error in fetch interceptor:",n),e(t,r)}else console.log("⏭️ Skipping non-GraphQL request:",o);return e(t,r)}}function W(e,t){if(console.log("🔍 Checking URL for GraphQL interception:",e),!e.includes(z))return console.log("❌ URL does not match Airtm GraphQL endpoint"),!1;if(console.log("✅ URL matches Airtm GraphQL endpoint"),t?.method&&t.method.toUpperCase()!=="POST")return console.log("❌ Request method is not POST:",t.method),!1;if(console.log("✅ Request method is POST or undefined (default)"),t?.body){const r=typeof t.body=="string"?t.body:JSON.stringify(t.body);console.log("📝 Request body preview:",r.substring(0,200)+"...");const o=x.some(n=>r.includes(n)?(console.log("✅ Found exact match for operation:",n),!0):r.toLowerCase().includes(n.toLowerCase())?(console.log("✅ Found case-insensitive match for operation:",n),!0):!1);return o||(console.log("❌ No monitored operations found in request body"),console.log("🔍 Monitored operations:",x)),o}return console.log("⚠️ No request body found, defaulting to true for Airtm GraphQL endpoint"),!0}function we(e){try{const t=e.data.acceptOperation;if(console.log("✅ Processing successful AcceptOperation:",t),t&&t.id){console.log("🎉 Operation successfully accepted:",t.id);const r={operationId:t.id,timestamp:new Date().toISOString(),status:"accepted",data:t};g("OPERATION_ACCEPTED",r),h({type:"SEND_TELEGRAM_OPERATION_UPDATE",operationId:t.id,status:"accepted"}).catch(o=>{console.warn("⚠️ Failed to send Telegram notification for acceptance:",o)})}else console.warn("⚠️ AcceptOperation response missing operation ID")}catch(t){console.error("❌ Error handling AcceptOperation response:",t)}}function ve(e){try{const t=e.data.unmatchOperation;if(console.log("❌ Processing successful UnmatchOperation:",t),t===!0){console.log("🚫 Operation successfully declined");const r={timestamp:new Date().toISOString(),status:"declined",success:!0};g("OPERATION_DECLINED",r),g("SEND_TELEGRAM_OPERATION_UPDATE",{status:"declined",timestamp:new Date().toISOString()})}else console.warn("⚠️ UnmatchOperation response unexpected result:",t)}catch(t){console.error("❌ Error handling UnmatchOperation response:",t)}}function Ee(e){try{const t=e.errors||[];console.log("❌ Processing UnmatchOperation errors:",t);const r=t.filter(o=>o.path&&o.path.includes("unmatchOperation"));for(const o of r){console.log("🔍 UnmatchOperation error details:",o);const n={timestamp:new Date().toISOString(),status:"decline_failed",error:o.message||"Unknown decline error",errorCode:o.extensions?.code||"UNKNOWN"};g("OPERATION_DECLINE_ERROR",n),g("SEND_TELEGRAM_OPERATION_UPDATE",{status:"decline_error",error:o.message,timestamp:new Date().toISOString()})}}catch(t){console.error("❌ Error handling UnmatchOperation error:",t)}}function Oe(e){try{const t=e.errors||[];console.log("❌ Processing AcceptOperation errors:",t);const r=t.filter(o=>o.path&&o.path.includes("acceptOperation"));for(const o of r)if(console.log("🔍 AcceptOperation error details:",o),o.message&&(o.message.toLowerCase().includes("no longer available")||o.message.toLowerCase().includes("not available")||o.message.toLowerCase().includes("already taken")||o.message.toLowerCase().includes("expired"))){console.log("🚫 Operation is no longer available");const a=Se(o);g("OPERATION_NOT_AVAILABLE",{error:o.message,operationId:a,timestamp:new Date().toISOString()}),h({type:"SEND_TELEGRAM_OPERATION_UPDATE",operationId:a||"unknown",status:"Not Available"}).catch(s=>{console.warn("⚠️ Failed to send Telegram notification for unavailable operation:",s)})}else console.log("⚠️ Other AcceptOperation error:",o.message),g("OPERATION_ACCEPT_ERROR",{error:o.message,timestamp:new Date().toISOString(),errorType:"accept_failed"})}catch(t){console.error("❌ Error handling AcceptOperation error:",t)}}function Se(e){try{if(e.path&&Array.isArray(e.path)){for(const t of e.path)if(typeof t=="string"&&/^\d+$/.test(t))return t}if(e.message){const t=e.message.match(/operation[\s\w]*?(\d+)/i);if(t&&t[1])return t[1]}return null}catch(t){return console.error("Error extracting operation ID from error:",t),null}}function l(e){try{if(!m()){console.warn("⚠️ Extension context invalidated, attempting recovery...");try{const n=ke(e);n.length>0&&(localStorage.setItem("airtm_offers_fallback",JSON.stringify({offers:n,timestamp:Date.now(),source:"context_invalidated_fallback"})),console.log(`💾 Stored ${n.length} offers in localStorage fallback`))}catch(n){console.error("❌ Fallback storage failed:",n)}L();return}console.log("🔄 Processing GraphQL response text..."),console.log("📄 Response text length:",e.length),console.log("📄 Response preview:",e.substring(0,500)+"...");const t=Ne(e),r=Date.now();if(w.has(t)){const n=w.get(t);if(r-n<Q){console.log("⏭️ Skipping duplicate GraphQL response processing");return}}w.set(t,r),$e();let o;try{o=JSON.parse(e),console.log("✅ JSON parsing successful"),console.log("📊 Parsed data keys:",Object.keys(o))}catch(n){console.error("❌ Failed to parse GraphQL response as JSON:",n);return}o.errors&&Array.isArray(o.errors)&&console.warn("⚠️ GraphQL response contains errors:",o.errors),o.data&&o.data.availableOperations?(console.log("🎯 availableOperations detected, length:",o.data.availableOperations.length),Te(o.data)):console.log("ℹ️ No availableOperations found in response"),o.data&&o.data.getBalance&&(console.log("💰 getBalance operation detected"),g("BALANCE_UPDATE",o.data.getBalance)),o.data&&o.data.createOperation&&(console.log("🆕 createOperation detected"),g("OPERATION_CREATED",o.data.createOperation)),o.data&&o.data.acceptOperation?(console.log("✅ acceptOperation detected"),we(o)):o.errors&&o.errors.some(n=>n.path&&n.path.includes("acceptOperation"))&&(console.log("❌ acceptOperation error detected"),Oe(o)),o.data&&o.data.unmatchOperation!==void 0?(console.log("❌ unmatchOperation detected"),ve(o)):o.errors&&o.errors.some(n=>n.path&&n.path.includes("unmatchOperation"))&&(console.log("❌ unmatchOperation error detected"),Ee(o)),o.data&&o.data.operation&&(console.log("📋 OperationDetails detected"),Ce(o.data.operation))}catch(t){const r=t instanceof Error?t.message:String(t);if(r.includes("Extension context invalidated")||r.includes("context invalidated")){console.warn("⚠️ Extension context invalidated during GraphQL response processing");return}console.error("❌ Error processing GraphQL response text:",t)}}function ke(e){try{const t=JSON.parse(e);return t?.data?.availableOperations?k(t):t?.data?.operations?t.data.operations:t?.data?.offers?t.data.offers:[]}catch(t){return console.error("❌ Error extracting offers from GraphQL response:",t),[]}}let S=!1,y=0;const E=5,xe=2e3;function L(){S||y>=E||(S=!0,y++,console.log(`🔄 Scheduling context recovery attempt ${y}/${E}`),setTimeout(()=>{_e(),S=!1},xe))}function _e(){console.log("🔄 Attempting extension context recovery..."),v(`Attempting recovery... (${y}/${E})`);try{m()?(console.log("✅ Extension context recovered successfully!"),y=0,v("✅ Context recovered!","success"),Ae(),console.log("🔄 Re-initializing fetch interceptor..."),setTimeout(()=>{_()},3e3)):(console.log(`❌ Context recovery attempt ${y} failed`),y<E?L():(console.log("❌ Max recovery attempts reached, giving up"),v("❌ Recovery failed","error"),Ie(),setTimeout(()=>{_()},5e3)))}catch(e){console.error("❌ Error during context recovery:",e),v("❌ Recovery error","error")}}function v(e,t="info"){try{_();const r=document.createElement("div");if(r.id="airtm-recovery-status",r.style.cssText=`
      position: fixed;
      top: 10px;
      left: 50%;
      transform: translateX(-50%);
      background: ${t==="success"?"#10b981":t==="error"?"#ef4444":"#3b82f6"};
      color: white;
      padding: 8px 16px;
      border-radius: 20px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 12px;
      font-weight: 600;
      z-index: 10001;
      box-shadow: 0 4px 12px rgba(0,0,0,0.2);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255,255,255,0.2);
      animation: slideDown 0.3s ease-out;
    `,r.textContent=e,!document.getElementById("airtm-recovery-animations")){const o=document.createElement("style");o.id="airtm-recovery-animations",o.textContent=`
        @keyframes slideDown {
          from { transform: translateX(-50%) translateY(-100%); opacity: 0; }
          to { transform: translateX(-50%) translateY(0); opacity: 1; }
        }
      `,document.head.appendChild(o)}document.body.appendChild(r)}catch(r){console.error("❌ Error showing recovery status indicator:",r)}}function _(){try{const e=document.getElementById("airtm-recovery-status");e&&e.remove()}catch(e){console.error("❌ Error hiding recovery status indicator:",e)}}function Ae(){try{const e=localStorage.getItem("airtm_offers_fallback");if(e){const t=JSON.parse(e);console.log(`📤 Sending ${t.offers.length} fallback offers to background script`),h({type:"OFFERS_UPDATE",data:{offers:t.offers,timestamp:t.timestamp,source:"context_recovery_fallback"}}).then(()=>{localStorage.removeItem("airtm_offers_fallback"),console.log("✅ Fallback data sent and cleared")}).catch(r=>{console.error("❌ Failed to send fallback data:",r)})}}catch(e){console.error("❌ Error sending fallback data:",e)}}function Ie(){try{document.querySelectorAll(".airtm-context-recovery-notification").forEach(o=>o.remove());const t=document.createElement("div");t.className="airtm-context-recovery-notification",t.style.cssText=`
      position: fixed;
      top: 20px;
      right: 20px;
      background: linear-gradient(135deg, #ff6b6b, #ee5a52);
      color: white;
      padding: 20px;
      border-radius: 12px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      font-weight: 500;
      z-index: 10000;
      box-shadow: 0 8px 32px rgba(0,0,0,0.3);
      max-width: 350px;
      border: 1px solid rgba(255,255,255,0.2);
      backdrop-filter: blur(10px);
    `,t.innerHTML=`
      <div style="display: flex; align-items: center; margin-bottom: 12px;">
        <span style="font-size: 20px; margin-right: 10px;">⚠️</span>
        <strong>Airtm Monitor Pro</strong>
      </div>
      <div style="margin-bottom: 15px; line-height: 1.4;">
        Extension context lost. Some features may not work properly.
      </div>
      <div style="display: flex; gap: 10px;">
        <button id="reload-extension-btn" style="
          background: rgba(255,255,255,0.2);
          border: 1px solid rgba(255,255,255,0.3);
          color: white;
          padding: 8px 16px;
          border-radius: 6px;
          font-size: 12px;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.2s;
        ">Reload Extension</button>
        <button id="refresh-page-btn" style="
          background: rgba(255,255,255,0.2);
          border: 1px solid rgba(255,255,255,0.3);
          color: white;
          padding: 8px 16px;
          border-radius: 6px;
          font-size: 12px;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.2s;
        ">Refresh Page</button>
        <button id="dismiss-btn" style="
          background: transparent;
          border: 1px solid rgba(255,255,255,0.3);
          color: white;
          padding: 8px 16px;
          border-radius: 6px;
          font-size: 12px;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.2s;
        ">Dismiss</button>
      </div>
    `,t.addEventListener("click",o=>{const n=o.target;n.id==="reload-extension-btn"?(window.open("chrome://extensions/","_blank"),t.remove()):n.id==="refresh-page-btn"?window.location.reload():n.id==="dismiss-btn"&&t.remove()}),t.querySelectorAll("button").forEach(o=>{o.addEventListener("mouseenter",()=>{o.style.background="rgba(255,255,255,0.3)"}),o.addEventListener("mouseleave",()=>{o.id==="dismiss-btn"?o.style.background="transparent":o.style.background="rgba(255,255,255,0.2)"})}),setTimeout(()=>{t.parentNode&&t.remove()},3e4),document.body.appendChild(t),console.log("🔔 Context recovery notification shown with actionable buttons")}catch(e){console.error("❌ Error showing context recovery notification:",e)}}async function Le(e){try{const t=await e.text();l(t)}catch(t){console.error("❌ Error processing GraphQL response:",t)}}async function Ce(e){try{console.log("🔧 Handling OperationDetails with status tracking"),console.log("📊 Operation data:",JSON.stringify(e,null,2).substring(0,1e3)+"...");const t={id:e.id,hash:e.hash,status:e.status,operationType:e.operationType,grossAmount:e.grossAmount,netAmount:e.netAmount,airtmFee:e.airtmFee,takerCommission:e.takerCommission,createdAt:e.createdAt,updatedAt:e.updatedAt,previousStatus:e.previousStatus,timeToLive:e.timeToLive,makerPaymentMethod:e.makerPaymentMethod,takerPaymentMethod:e.takerPaymentMethod};console.log(`📈 OperationDetails processed: ${t.id} - Status: ${t.status}`),g("OPERATION_DETAILS_UPDATE",{operation:t,timestamp:new Date().toISOString(),source:"graphql-interception"}),await chrome.storage.local.set({[`operation_${t.id}`]:t,lastOperationUpdate:new Date().toISOString()})}catch(t){console.error("❌ Error handling OperationDetails:",t)}}async function Te(e,t){try{if(!m()){console.warn("⚠️ Extension context invalidated, attempting recovery for AvailableOperations...");try{const n=k(e);n.length>0&&(localStorage.setItem("airtm_offers_fallback",JSON.stringify({offers:n,timestamp:Date.now(),source:"available_operations_context_invalidated"})),console.log(`💾 Stored ${n.length} AvailableOperations offers in localStorage fallback`))}catch(n){console.error("❌ AvailableOperations fallback storage failed:",n)}L();return}console.log("🔧 Handling AvailableOperations query format with comprehensive validation"),console.log("📊 Raw data structure:",JSON.stringify(e,null,2).substring(0,1e3)+"...");const r=k(e);if(console.log(`📈 AvailableOperations GraphQL query processed: ${r.length} offers found`),r.length===0){console.log("📭 No offers found - notifying extension with empty array"),g("OFFERS_UPDATE",{offers:[],timestamp:new Date().toISOString(),source:"graphql-interception"});return}console.log(`✅ Successfully parsed ${r.length} offers from GraphQL response`),console.log("🔍 First offer preview:",JSON.stringify(r[0],null,2).substring(0,500)+"..."),await Re(r);const o=se();o?(console.log("🔑 Bearer token extracted and storing..."),await De(o)):console.log("⚠️ No bearer token found"),console.log("📢 Notifying extension components of offers update"),g("OFFERS_UPDATE",{offers:r,timestamp:new Date().toISOString(),source:"graphql-interception"}),console.log("📡 Dispatching custom event for content script listeners"),window.dispatchEvent(new CustomEvent("airtm-offers-detected",{detail:{offers:r,timestamp:new Date().toISOString()}})),console.log("🎉 AvailableOperations handling completed successfully")}catch(r){const o=r instanceof Error?r.message:String(r);if(o.includes("Extension context invalidated")||o.includes("context invalidated")){console.warn("⚠️ Extension context invalidated during AvailableOperations handling");return}console.error("❌ Error handling AvailableOperations:",r),console.error("📊 Error details:",r instanceof Error?r.stack:r),m()&&g("PARSING_ERROR",{error:r instanceof Error?r.message:"Unknown parsing error",timestamp:new Date().toISOString()})}}async function Re(e){try{if(typeof chrome<"u"&&chrome.storage&&chrome.runtime&&chrome.runtime.id)try{await chrome.storage.local.set({airtm_offers:e,airtm_offers_timestamp:new Date().toISOString(),airtm_offers_count:e.length}),console.log(`✅ Stored ${e.length} offers in Chrome storage`);return}catch(t){const r=t instanceof Error?t.message:String(t);if(r.includes("Extension context invalidated")||r.includes("context invalidated")||r.includes("receiving end does not exist"))console.warn("⚠️ Extension context invalidated, falling back to localStorage");else throw t}try{localStorage.setItem("airtm_offers",JSON.stringify(e)),localStorage.setItem("airtm_offers_timestamp",new Date().toISOString()),localStorage.setItem("airtm_offers_count",e.length.toString()),console.log(`📦 Stored ${e.length} offers in localStorage (fallback)`)}catch(t){throw console.error("❌ Failed to store in localStorage:",t),new Error("Unable to store offers: Both Chrome storage and localStorage failed")}}catch(t){const r=t instanceof Error?t.message:"Unknown error";console.error("❌ Error storing offers:",r)}}async function De(e){try{if(typeof chrome<"u"&&chrome.storage&&chrome.runtime&&chrome.runtime.id)try{await chrome.storage.local.set({airtm_bearer_token:e,airtm_token_timestamp:new Date().toISOString()}),console.log("✅ Bearer token stored successfully in Chrome storage");return}catch(t){const r=t instanceof Error?t.message:String(t);if(r.includes("Extension context invalidated")||r.includes("context invalidated")||r.includes("receiving end does not exist"))console.warn("⚠️ Extension context invalidated, falling back to localStorage for bearer token");else throw t}try{localStorage.setItem("airtm_bearer_token",e),localStorage.setItem("airtm_token_timestamp",new Date().toISOString()),console.log("📦 Bearer token stored in localStorage (fallback)")}catch(t){console.error("❌ Failed to store bearer token in localStorage:",t)}}catch(t){const r=t instanceof Error?t.message:"Unknown error";console.error("❌ Error storing bearer token:",r)}}function g(e,t){try{typeof chrome<"u"&&chrome.runtime&&chrome.runtime.id?h({type:e,data:t,metadata:{timestamp:new Date().toISOString(),source:"fetch-interceptor"}}).catch(r=>{const o=r instanceof Error?r.message:String(r);o.includes("Extension context invalidated")||o.includes("context invalidated")||o.includes("receiving end does not exist")?(console.warn(`⚠️ Extension context invalidated while sending message: ${e}`),console.log("💡 Extension may have been reloaded or disabled. Data will be stored locally.")):console.log(`❌ Failed to send message to extension (${e}):`,o)}):console.log(`📭 Chrome extension APIs not available for message: ${e}`)}catch(r){const o=r instanceof Error?r.message:"Unknown error";console.log(`❌ Could not send message to extension (${e}):`,o)}}function Ne(e){try{const r=JSON.parse(e);if(r.data?.availableOperations&&Array.isArray(r.data.availableOperations)){const o=r.data.availableOperations,n=o.length,a=o.slice(0,3).map((s,i)=>s?.id||s?.hash||`op${i}`).join("-");return`availableOps-${n}-${a}`}}catch(r){console.debug("Cache key generation fallback due to:",r)}let t=0;for(let r=0;r<e.length;r++){const o=e.charCodeAt(r);t=(t<<5)-t+o,t=t&t}return t.toString()}function $e(){const e=Date.now(),t=[];w.forEach((r,o)=>{e-r>Q*2&&t.push(o)}),t.forEach(r=>w.delete(r))}async function C(){try{if(typeof chrome<"u"&&chrome.storage&&chrome.runtime&&chrome.runtime.id)try{return(await chrome.storage.local.get("airtm_offers")).airtm_offers||[]}catch(e){const t=e instanceof Error?e.message:String(e);if(t.includes("Extension context invalidated")||t.includes("context invalidated")||t.includes("receiving end does not exist"))console.warn("⚠️ Extension context invalidated, falling back to localStorage for offers retrieval");else throw e}try{const e=localStorage.getItem("airtm_offers");return e?JSON.parse(e):[]}catch(e){return console.error("❌ Failed to retrieve offers from localStorage:",e),[]}}catch(e){return console.error("❌ Error retrieving stored offers:",e),[]}}function Pe(e){console.log("🔧 Updating duplicate detection config:",e)}const A=/https:\/\/app\.airtm\.com\/peer-transfers\/available/;let b=null;async function U(){try{if(console.log("🚀 Airtm Monitor Pro: Content script initializing..."),console.log("📍 Current URL:",window.location.href),console.log("🎯 Target pattern:",A.toString()),!m()){console.log("⚠️ Extension context invalidated, content script will not initialize");return}if(!A.test(window.location.href)){console.log("❌ Not on target page, content script will not activate"),console.log("💡 Expected URL pattern: https://app.airtm.com/peer-transfers/available");return}console.log("✅ On target page, initializing fetch interceptor..."),console.log("🔧 Window.fetch available:",typeof window.fetch<"u"),H(),Me(),B(),Fe(),console.log("🎉 Airtm Monitor Pro: Content script initialized successfully"),console.log("👂 Now listening for GraphQL requests...")}catch(e){console.error("❌ Error initializing content script:",e),(e instanceof Error?e.message:String(e)).includes("Extension context invalidated")&&console.log("🔄 Extension context was invalidated during initialization")}}function Me(){if(!m()){console.log("⚠️ Extension context invalidated, skipping message listener setup");return}chrome.runtime.onMessage.addListener((e,t,r)=>{if(!m())return console.log("⚠️ Extension context invalidated during message handling"),!1;switch(console.log("Content script received message:",e),e.type){case"ACCEPT_OFFER":return J(e.data?.offerId).then(o=>r({success:!0,result:o})).catch(o=>r({success:!1,error:o.message})),!0;case"REJECT_OFFER":return X(e.data?.offerId).then(o=>r({success:!0,result:o})).catch(o=>r({success:!1,error:o.message})),!0;case"GET_OFFERS":return C().then(o=>r({success:!0,offers:o})).catch(o=>r({success:!1,error:o.message})),!0;case"STATUS_UPDATE":Ue(e.data),r({success:!0});break;case"HIGHLIGHT_OFFER":Ge(e.data?.offer),r({success:!0});break;case"FOCUS_OFFER":return je(e.data?.offerId).then(()=>r({success:!0})).catch(o=>r({success:!1,error:o.message})),!0;case"UPDATE_SETTINGS":Qe(e.data),r({success:!0});break;case"EXECUTE_COMMAND":return e.command?qe(e.command).then(()=>r({success:!0})).catch(o=>r({success:!1,error:o.message})):r({success:!1,error:"Command is required"}),!0;default:console.log("Unknown message type:",e.type),r({success:!1,error:"Unknown message type"})}})}function B(){b&&(document.removeEventListener("keydown",b),b=null),b=e=>{if(e.ctrlKey&&e.shiftKey)switch(e.key.toLowerCase()){case"a":e.preventDefault(),Y();break;case"r":e.preventDefault(),V();break;case"d":e.preventDefault(),K();break;case"c":e.preventDefault(),Z();break}},document.addEventListener("keydown",b)}function Fe(){window.addEventListener("airtm-offers-detected",r=>{if(console.log("Offers detected event:",r.detail),m())try{h({type:"OFFERS_UPDATE",data:r.detail,metadata:{timestamp:new Date().toISOString(),source:"content-script"}}).catch(o=>{console.log("⚠️ Failed to send message to background script:",o)})}catch(o){console.log("⚠️ Extension context invalidated, cannot send message:",o)}else console.log("⚠️ Extension context invalidated, skipping message send")});let e=window.location.href;new MutationObserver(()=>{window.location.href!==e&&(e=window.location.href,console.log("Page navigation detected:",e),A.test(e)&&setTimeout(()=>H(),1e3))}).observe(document.body,{childList:!0,subtree:!0})}async function J(e){if(!e)throw new Error("Offer ID is required");console.log("Attempting to accept offer:",e);try{const t=T(e);if(!t)throw new Error(`Offer element not found for ID: ${e}`);const r=t.querySelector('[data-testid="accept-button"], .accept-btn, button[aria-label*="accept" i]');if(!r)throw new Error("Accept button not found");return r.click(),console.log("Offer acceptance initiated:",e),!0}catch(t){throw console.error("Error accepting offer:",t),t}}async function X(e){if(!e)throw new Error("Offer ID is required");console.log("Attempting to reject offer:",e);try{const t=T(e);if(!t)throw new Error(`Offer element not found for ID: ${e}`);const r=t.querySelector('[data-testid="reject-button"], .reject-btn, button[aria-label*="reject" i]');if(!r)throw new Error("Reject button not found");return r.click(),console.log("Offer rejection initiated:",e),!0}catch(t){throw console.error("Error rejecting offer:",t),t}}function T(e){const t=[`[data-offer-id="${e}"]`,`[data-id="${e}"]`,`#offer-${e}`,`.offer-${e}`];for(const o of t){const n=document.querySelector(o);if(n)return n}const r=document.querySelectorAll('[class*="offer"], [data-testid*="offer"]');for(const o of r)if(o.textContent?.includes(e))return o;return null}async function qe(e){try{switch(console.log("Executing command:",e),e){case"accept_offer":await Y();break;case"reject_offer":await V();break;case"open_offer_details":K();break;case"cycle_offers":Z();break;default:console.warn("Unknown command:",e)}}catch(t){throw console.error("Error executing command:",e,t),t}}async function Y(){try{const e=await C();if(e.length===0){console.log("No offers available for quick accept");return}const t=e[0];await J(t.id)}catch(e){console.error("Error in quick accept:",e)}}async function V(){try{const e=await C();if(e.length===0){console.log("No offers available for quick reject");return}const t=e[0];await X(t.id)}catch(e){console.error("Error in quick reject:",e)}}function K(){console.log("Show details shortcut triggered"),h({type:"SHOW_DETAILS",metadata:{timestamp:new Date().toISOString(),source:"keyboard-shortcut"}}).catch(e=>{console.log("⚠️ Failed to send show details message:",e)})}function Z(){console.log("Cycle offers shortcut triggered"),h({type:"CYCLE_OFFERS",metadata:{timestamp:new Date().toISOString(),source:"keyboard-shortcut"}}).catch(e=>{console.log("⚠️ Failed to send cycle offers message:",e)})}function Ue(e){console.log("Status update received:",e),e.monitoring?console.log("Monitoring is active"):console.log("Monitoring is inactive")}function Ge(e){e&&(console.log("Highlighting offer:",e.id),ee(),ze(e),We())}async function je(e){if(!e)throw new Error("Offer ID is required");console.log("Focusing on offer:",e);try{ee();const t=T(e);if(t){t.classList.add("airtm-monitor-highlight","airtm-monitor-focus"),t.scrollIntoView({behavior:"smooth",block:"center",inline:"center"});const r=document.createElement("style");r.id="airtm-focus-styles",r.textContent=`
        .airtm-monitor-focus {
          animation: airtm-focus-pulse 3s infinite !important;
          border: 4px solid #ef4444 !important;
          box-shadow: 0 0 30px rgba(239, 68, 68, 0.8) !important;
        }
        
        .airtm-monitor-focus::before {
          content: '🎯 DETECTED OFFER!' !important;
          background: linear-gradient(135deg, #ef4444, #dc2626) !important;
        }
        
        @keyframes airtm-focus-pulse {
          0% { 
            box-shadow: 0 0 30px rgba(239, 68, 68, 0.8);
            transform: scale(1);
          }
          50% { 
            box-shadow: 0 0 50px rgba(239, 68, 68, 1);
            transform: scale(1.02);
          }
          100% { 
            box-shadow: 0 0 30px rgba(239, 68, 68, 0.8);
            transform: scale(1);
          }
        }
      `;const o=document.getElementById("airtm-focus-styles");o&&o.remove(),document.head.appendChild(r),setTimeout(()=>{t.classList.remove("airtm-monitor-focus");const n=document.getElementById("airtm-focus-styles");n&&n.remove()},15e3),console.log("Successfully focused on offer:",e)}else{console.log("Specific offer not found, highlighting first available offer");const r=document.querySelector('[data-testid*="offer"], [class*="offer"], .MuiCard-root');r&&(r.classList.add("airtm-monitor-highlight"),r.scrollIntoView({behavior:"smooth",block:"center"}),setTimeout(()=>{r.classList.remove("airtm-monitor-highlight")},1e4))}}catch(t){throw console.error("Error focusing on offer:",t),t}}function ee(){if(document.getElementById("airtm-monitor-styles"))return;const e=document.createElement("style");e.id="airtm-monitor-styles",e.textContent=`
    .airtm-monitor-highlight {
      animation: airtm-pulse 2s infinite;
      border: 3px solid #10b981 !important;
      border-radius: 8px !important;
      box-shadow: 0 0 20px rgba(16, 185, 129, 0.5) !important;
      background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(34, 197, 94, 0.1)) !important;
      position: relative !important;
    }
    
    .airtm-monitor-highlight::before {
      content: '🔥 NEW OFFER!';
      position: absolute;
      top: -10px;
      right: -10px;
      background: linear-gradient(135deg, #10b981, #22c55e);
      color: white;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: bold;
      z-index: 1000;
      animation: airtm-bounce 1s infinite;
    }
    
    @keyframes airtm-pulse {
      0% { box-shadow: 0 0 20px rgba(16, 185, 129, 0.5); }
      50% { box-shadow: 0 0 30px rgba(16, 185, 129, 0.8); }
      100% { box-shadow: 0 0 20px rgba(16, 185, 129, 0.5); }
    }
    
    @keyframes airtm-bounce {
      0%, 100% { transform: translateY(0); }
      50% { transform: translateY(-5px); }
    }
    
    .airtm-floating-notification {
      position: fixed;
      top: 20px;
      right: 20px;
      background: linear-gradient(135deg, #10b981, #22c55e);
      color: white;
      padding: 16px 20px;
      border-radius: 12px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
      z-index: 10000;
      animation: airtm-slide-in 0.5s ease-out;
      max-width: 300px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
    
    .airtm-floating-notification h3 {
      margin: 0 0 8px 0;
      font-size: 16px;
      font-weight: bold;
    }
    
    .airtm-floating-notification p {
      margin: 0;
      font-size: 14px;
      opacity: 0.9;
    }
    
    @keyframes airtm-slide-in {
      from {
        transform: translateX(100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }
  `,document.head.appendChild(e)}function ze(e){console.log("Attempting to highlight offer:",e),setTimeout(()=>{let t=!1;const r=['[data-testid*="offer"]','[class*="offer"]','[class*="card"]',".MuiCard-root",".MuiPaper-root",'[data-cy*="offer"]',".offer-card",".peer-transfer-card"];for(const o of r){if(t)break;const n=document.querySelectorAll(o);console.log(`Found ${n.length} elements with selector: ${o}`),n.forEach(a=>{if(t)return;const s=a.textContent||"",i=e.grossAmount||e.amount,c=e.currency?.symbol||e.walletCurrency?.symbol;let d=!1;if(i){const p=i.toString(),f=parseFloat(p).toLocaleString();(s.includes(p)||s.includes(f))&&(d=!0)}if(!d&&c&&s.includes(c)&&(d=!0),d){a.classList.add("airtm-monitor-highlight"),a.scrollIntoView({behavior:"smooth",block:"center"}),setTimeout(()=>{a.classList.remove("airtm-monitor-highlight")},1e4),console.log("✅ Highlighted offer element:",a),t=!0;return}})}if(!t){console.log("No specific match found, highlighting most recent offer");const o=document.querySelectorAll('[data-testid*="offer"], [class*="offer"], .MuiCard-root');if(o.length>0){const n=o[0];n.classList.add("airtm-monitor-highlight"),n.scrollIntoView({behavior:"smooth",block:"center"}),setTimeout(()=>{n.classList.remove("airtm-monitor-highlight")},1e4),console.log("✅ Highlighted first available offer element:",n),t=!0}}t||console.warn("⚠️ Could not find any offer elements to highlight")},500)}function Qe(e){console.log("📝 Received settings update:",e),e.duplicateDetection&&Pe(e.duplicateDetection),e.hotkeys&&(console.log("🔄 Hotkeys updated, re-setting up keyboard shortcuts"),B())}let u=null,G=0;const He=0;function We(){const e=Date.now();if(u){try{u.pause(),u.currentTime=0,console.log("🔇 Previous sound interrupted for new offer")}catch(t){console.log("Error stopping previous sound:",t)}u=null}if(e-G<He){console.log("🔇 Sound skipped - too rapid succession (debounced)");return}G=e;try{u=new Audio(chrome.runtime.getURL("sounds/beep.mp3")),u.volume=.5,u.onended=()=>{u=null,console.log("🔊 Sound playback completed")},u.onerror=()=>{u=null,console.log("🔇 Sound playback error")},u.play().then(()=>{console.log("🔊 New notification sound started")}).catch(t=>{console.log("Could not play sound:",t),u=null})}catch(t){console.log("Sound not available:",t),u=null}}document.readyState==="loading"?document.addEventListener("DOMContentLoaded",U):U();let I=null,j=!0;function Be(){I=setInterval(()=>{const e=m();if(e!==j){if(e){console.log("✅ Extension context restored during health check");try{const t=localStorage.getItem("airtm_offers_fallback");if(t){console.log("📤 Sending fallback data after context restoration...");const r=JSON.parse(t);h({type:"OFFERS_UPDATE",data:{offers:r.offers,timestamp:r.timestamp,source:"health_check_recovery"}}).then(()=>{localStorage.removeItem("airtm_offers_fallback"),console.log("✅ Fallback data sent and cleared during health check")}).catch(o=>{console.error("❌ Failed to send fallback data during health check:",o)})}}catch(t){console.error("❌ Error processing fallback data during health check:",t)}}else console.warn("⚠️ Extension context lost during health check");j=e}},5e3)}Be();window.addEventListener("beforeunload",()=>{I&&clearInterval(I)});
