{"name": "airtm-monitor-pro", "version": "1.0.0", "description": "Chrome extension for monitoring Airtm cryptocurrency exchange offers", "type": "module", "scripts": {"dev": "vite", "dev:popup": "vite --config vite.dev.config.ts", "build": "tsc && vite build", "build:prod": "tsc && vite build --mode production", "preview": "vite preview", "preview:popup": "vite preview --config vite.dev.config.ts", "type-check": "tsc --noEmit", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint src --ext ts,tsx --fix"}, "dependencies": {"lucide-react": "^0.514.0", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@crxjs/vite-plugin": "^2.0.0-beta.21", "@types/chrome": "^0.0.246", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^4.5.0"}, "keywords": ["chrome-extension", "airtm", "cryptocurrency", "trading", "monitoring", "react", "typescript"], "author": "Airtm Monitor Pro Team", "license": "MIT"}