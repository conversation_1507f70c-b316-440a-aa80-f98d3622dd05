/**
 * Airtm Monitor Pro - Popup Application
 * Vanilla JavaScript implementation for CSP compliance
 */

class AirtmPopupApp {
  constructor() {
    this.settings = {
      monitoring: false,
      refreshInterval: 10,
      monitorBuyPage: true,
      monitorSellPage: true,
      autoAccept: false
    };
    
    this.stats = {
      offersCount: 0,
      monitoringStartTime: null,
      isConnected: true
    };
    
    this.monitoringTimer = null;
    this.init();
  }
  
  async init() {
    try {
      await this.loadSettings();
      await this.loadStats();
      this.setupEventListeners();
      this.updateUI();
      this.startMonitoringTimer();
      
      console.log('✅ Popup initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize popup:', error);
      this.showError('Initialization failed', error.message);
    }
  }
  
  async loadSettings() {
    try {
      if (chrome?.storage?.sync) {
        const result = await chrome.storage.sync.get('settings');
        if (result.settings) {
          this.settings = { ...this.settings, ...result.settings };
        }
      }
    } catch (error) {
      console.warn('Could not load settings:', error);
    }
  }
  
  async loadStats() {
    try {
      if (chrome?.storage?.local) {
        const result = await chrome.storage.local.get(['stats', 'monitoringStartTime']);
        if (result.stats) {
          this.stats = { ...this.stats, ...result.stats };
        }
        if (result.monitoringStartTime) {
          this.stats.monitoringStartTime = result.monitoringStartTime;
        }
      }
    } catch (error) {
      console.warn('Could not load stats:', error);
    }
  }
  
  async saveSettings() {
    try {
      if (chrome?.storage?.sync) {
        await chrome.storage.sync.set({ settings: this.settings });
      }
    } catch (error) {
      console.error('Failed to save settings:', error);
    }
  }
  
  setupEventListeners() {
    // Monitoring toggle
    const monitoringToggle = document.getElementById('monitoring-toggle');
    monitoringToggle.addEventListener('change', (e) => {
      this.settings.monitoring = e.target.checked;
      this.handleMonitoringToggle();
      this.saveSettings();
    });
    
    // Open Airtm button
    const openAirtmBtn = document.getElementById('open-airtm');
    openAirtmBtn.addEventListener('click', () => {
      this.openAirtm();
    });
    
    // View offers button
    const viewOffersBtn = document.getElementById('view-offers');
    viewOffersBtn.addEventListener('click', () => {
      this.viewOffers();
    });
    
    // Settings button
    const settingsBtn = document.getElementById('open-settings');
    settingsBtn.addEventListener('click', () => {
      this.openSettings();
    });
    
    // Help link
    const helpLink = document.getElementById('help-link');
    helpLink.addEventListener('click', (e) => {
      e.preventDefault();
      this.openHelp();
    });
    
    // About link
    const aboutLink = document.getElementById('about-link');
    aboutLink.addEventListener('click', (e) => {
      e.preventDefault();
      this.openAbout();
    });
  }
  
  updateUI() {
    // Update monitoring toggle
    const monitoringToggle = document.getElementById('monitoring-toggle');
    monitoringToggle.checked = this.settings.monitoring;
    
    // Update connection status
    this.updateConnectionStatus();
    
    // Update stats
    this.updateStats();
  }
  
  updateConnectionStatus() {
    const connectionText = document.getElementById('connection-text');
    const statusDot = document.querySelector('.status-dot');
    
    if (this.stats.isConnected) {
      connectionText.textContent = 'Connected';
      statusDot.style.background = 'var(--success-500)';
    } else {
      connectionText.textContent = 'Disconnected';
      statusDot.style.background = 'var(--error-500)';
    }
  }
  
  updateStats() {
    // Update offers count
    const offersCount = document.getElementById('offers-count');
    offersCount.textContent = this.stats.offersCount;
    
    // Update monitoring time
    this.updateMonitoringTime();
  }
  
  updateMonitoringTime() {
    const monitoringTimeEl = document.getElementById('monitoring-time');
    
    if (this.settings.monitoring && this.stats.monitoringStartTime) {
      const elapsed = Date.now() - this.stats.monitoringStartTime;
      const minutes = Math.floor(elapsed / 60000);
      const seconds = Math.floor((elapsed % 60000) / 1000);
      monitoringTimeEl.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    } else {
      monitoringTimeEl.textContent = '00:00';
    }
  }
  
  startMonitoringTimer() {
    // Update monitoring time every second
    setInterval(() => {
      this.updateMonitoringTime();
    }, 1000);
  }
  
  handleMonitoringToggle() {
    if (this.settings.monitoring) {
      // Start monitoring
      this.stats.monitoringStartTime = Date.now();
      this.sendMessageToBackground('startMonitoring');
    } else {
      // Stop monitoring
      this.stats.monitoringStartTime = null;
      this.sendMessageToBackground('stopMonitoring');
    }
    
    this.saveStats();
    this.updateStats();
  }
  
  async saveStats() {
    try {
      if (chrome?.storage?.local) {
        await chrome.storage.local.set({ 
          stats: this.stats,
          monitoringStartTime: this.stats.monitoringStartTime
        });
      }
    } catch (error) {
      console.error('Failed to save stats:', error);
    }
  }
  
  sendMessageToBackground(action, data = {}) {
    try {
      if (chrome?.runtime?.sendMessage) {
        chrome.runtime.sendMessage({ action, ...data });
      }
    } catch (error) {
      console.warn('Could not send message to background:', error);
    }
  }
  
  openAirtm() {
    this.openTab('https://app.airtm.com/peer-transfers/available');
  }
  
  viewOffers() {
    this.openTab('https://app.airtm.com/peer-transfers/available');
  }
  
  openSettings() {
    if (chrome?.runtime?.openOptionsPage) {
      chrome.runtime.openOptionsPage();
    } else {
      // Fallback
      this.openTab(chrome.runtime.getURL('src/options/index.html'));
    }
  }
  
  openHelp() {
    this.openSettings(); // Open settings and navigate to help section
  }
  
  openAbout() {
    this.openSettings(); // Open settings and navigate to about section
  }
  
  openTab(url) {
    try {
      if (chrome?.tabs?.create) {
        chrome.tabs.create({ url });
        window.close(); // Close popup after opening tab
      }
    } catch (error) {
      console.error('Could not open tab:', error);
    }
  }
  
  showError(title, message) {
    console.error(`${title}: ${message}`);
    // Could implement a toast notification here
  }
}

// Initialize popup when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  new AirtmPopupApp();
});

// Fallback initialization
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    new AirtmPopupApp();
  });
} else {
  new AirtmPopupApp();
}

// Export for debugging
window.AirtmPopupApp = AirtmPopupApp;
