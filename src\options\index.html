<!DOCTYPE html>
<html lang="en" class="options-page">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=0.8, maximum-scale=1.2">
  <title>Airtm Monitor Pro - Settings</title>

  <!-- Preload critical resources -->
  <link rel="preload" href="./main.tsx" as="script" crossorigin>

  <!-- Critical CSS for immediate rendering -->
  <style>
    /* Critical CSS for options page - ensure full window sizing */
    html.options-page, body.options {
      width: 100vw !important;
      height: 100vh !important;
      min-width: 100vw !important;
      min-height: 100vh !important;
      max-width: 100vw !important;
      max-height: none !important;
      margin: 0 !important;
      padding: 0 !important;
      overflow-x: hidden !important;
      overflow-y: auto !important;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON> San<PERSON>', 'Droid Sans', 'Helvetica Neue', sans-serif;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      background: linear-gradient(135deg, #1e1b4b, #0f172a, #312e81);
    }

    #root {
      width: 100% !important;
      height: 100% !important;
      min-height: 100vh !important;
      display: flex !important;
      flex-direction: column !important;
    }

    /* Loading spinner for immediate feedback */
    .initial-loading {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      color: #a78bfa;
    }

    .initial-loading .spinner {
      width: 2rem;
      height: 2rem;
      border: 3px solid rgba(167, 139, 250, 0.3);
      border-top: 3px solid #a78bfa;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      to { transform: rotate(360deg); }
    }
  </style>
</head>
<body class="options">
  <div id="root">
    <!-- Initial loading state -->
    <div class="initial-loading">
      <div>
        <div class="spinner"></div>
        <p style="margin-top: 1rem; font-size: 1.125rem;">Loading Airtm Monitor Settings...</p>
      </div>
    </div>
  </div>

  <!-- Error handling script -->
  <script>
    window.addEventListener('error', function(e) {
      console.error('Options page error:', e.error);
      const root = document.getElementById('root');
      if (root) {
        root.innerHTML = `
          <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; min-height: 100vh; color: white; text-align: center; padding: 2rem;">
            <h1 style="color: #ef4444; font-size: 2rem; margin-bottom: 1rem;">Configuration Error</h1>
            <p style="color: #94a3b8; font-size: 1.125rem; margin-bottom: 2rem;">Failed to load the options page. Please try refreshing or check the console for details.</p>
            <button onclick="window.location.reload()" style="background: linear-gradient(to right, #8b5cf6, #7c3aed); color: white; padding: 0.75rem 1.5rem; border-radius: 0.5rem; border: none; cursor: pointer; font-weight: 600;">
              Reload Page
            </button>
          </div>
        `;
      }
    });
  </script>

  <script type="module" src="./main.tsx"></script>

  <!-- Debug helper for development -->
  <script src="./debug.js"></script>
</body>
</html>
