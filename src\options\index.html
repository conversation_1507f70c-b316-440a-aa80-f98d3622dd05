<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Airtm Monitor Pro - Configuration</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <style>
    /* Inline critical CSS to avoid loading issues */
    :root {
      --primary-500: #0ea5e9;
      --secondary-500: #8b5cf6;
      --success-500: #10b981;
      --warning-500: #f59e0b;
      --error-500: #ef4444;
      --gray-50: #f9fafb;
      --gray-100: #f3f4f6;
      --gray-200: #e5e7eb;
      --gray-300: #d1d5db;
      --gray-400: #9ca3af;
      --gray-500: #6b7280;
      --gray-600: #4b5563;
      --gray-700: #374151;
      --gray-800: #1f2937;
      --gray-900: #111827;
      --space-1: 0.25rem;
      --space-2: 0.5rem;
      --space-3: 0.75rem;
      --space-4: 1rem;
      --space-6: 1.5rem;
      --space-8: 2rem;
      --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
      --radius-md: 0.5rem;
      --radius-lg: 0.75rem;
      --radius-xl: 1rem;
      --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
      --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      --transition-fast: 150ms ease-in-out;
    }

    * { box-sizing: border-box; margin: 0; padding: 0; }

    html, body {
      height: 100%;
      font-family: var(--font-family);
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      overflow-x: hidden;
    }

    .loading-screen {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
      transition: opacity 0.5s ease;
    }

    .loading-screen.hidden {
      opacity: 0;
      visibility: hidden;
    }

    .loading-content {
      text-align: center;
      color: white;
    }

    .loading-logo {
      position: relative;
      width: 80px;
      height: 80px;
      margin: 0 auto 1.5rem;
    }

    .loading-spinner {
      width: 100%;
      height: 100%;
      color: rgba(255, 255, 255, 0.3);
      animation: spin 2s linear infinite;
    }

    @keyframes spin {
      to { transform: rotate(360deg); }
    }

    .loading-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 1.5rem;
    }

    .loading-content h2 {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
    }

    .loading-content p {
      opacity: 0.8;
    }

    .app {
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      transition: opacity 0.5s ease;
    }

    .app.hidden {
      opacity: 0;
      visibility: hidden;
    }

    .error-message {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: white;
      padding: 2rem;
      border-radius: var(--radius-xl);
      box-shadow: var(--shadow-md);
      text-align: center;
      max-width: 500px;
      width: 90%;
      z-index: 10000;
    }

    .error-message h3 {
      color: var(--error-500);
      margin-bottom: 1rem;
    }

    .error-message p {
      color: var(--gray-700);
      margin-bottom: 1.5rem;
    }

    .error-button {
      background: var(--primary-500);
      color: white;
      border: none;
      padding: 0.75rem 1.5rem;
      border-radius: var(--radius-md);
      cursor: pointer;
      font-weight: 500;
    }

    .error-button:hover {
      background: var(--primary-600);
    }

    /* Main App Styles */
    .app-header {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-bottom: 1px solid var(--gray-200);
      position: sticky;
      top: 0;
      z-index: 100;
    }

    .header-content {
      max-width: 1400px;
      margin: 0 auto;
      padding: var(--space-4) var(--space-6);
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .header-brand {
      display: flex;
      align-items: center;
      gap: var(--space-4);
    }

    .brand-icon {
      width: 48px;
      height: 48px;
      background: linear-gradient(135deg, var(--primary-500), var(--secondary-500));
      border-radius: var(--radius-lg);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.25rem;
      color: white;
      box-shadow: var(--shadow-md);
    }

    .brand-text h1 {
      font-size: 1.5rem;
      font-weight: 700;
      color: var(--gray-900);
      margin-bottom: var(--space-1);
    }

    .brand-text p {
      font-size: 0.875rem;
      color: var(--gray-600);
    }

    .status-indicator {
      display: flex;
      align-items: center;
      gap: var(--space-2);
      padding: var(--space-2) var(--space-4);
      background: var(--gray-100);
      border-radius: var(--radius-md);
      font-size: 0.875rem;
      font-weight: 500;
    }

    .status-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: var(--success-500);
    }

    .app-main {
      flex: 1;
      background: var(--gray-50);
    }

    .main-container {
      max-width: 1400px;
      margin: 0 auto;
      display: grid;
      grid-template-columns: 280px 1fr;
      min-height: calc(100vh - 80px);
    }

    .app-nav {
      background: white;
      border-right: 1px solid var(--gray-200);
      padding: var(--space-6);
      overflow-y: auto;
    }

    .nav-section {
      margin-bottom: var(--space-8);
    }

    .nav-section h3 {
      font-size: 0.875rem;
      font-weight: 600;
      color: var(--gray-500);
      text-transform: uppercase;
      letter-spacing: 0.05em;
      margin-bottom: var(--space-4);
    }

    .nav-list {
      list-style: none;
    }

    .nav-link {
      display: flex;
      align-items: center;
      gap: var(--space-3);
      padding: var(--space-3) var(--space-4);
      border-radius: var(--radius-md);
      color: var(--gray-700);
      text-decoration: none;
      font-weight: 500;
      transition: all var(--transition-fast);
      margin-bottom: var(--space-1);
    }

    .nav-link:hover {
      background: var(--gray-100);
      color: var(--gray-900);
    }

    .nav-link.active {
      background: linear-gradient(135deg, var(--primary-500), var(--secondary-500));
      color: white;
      box-shadow: var(--shadow-md);
    }

    .nav-icon {
      font-size: 1.125rem;
    }

    .app-content {
      padding: var(--space-6);
      overflow-y: auto;
    }

    .content-section {
      display: none;
    }

    .content-section.active {
      display: block;
    }

    .section-header {
      margin-bottom: var(--space-8);
    }

    .section-header h2 {
      font-size: 1.875rem;
      font-weight: 700;
      color: var(--gray-900);
      margin-bottom: var(--space-2);
    }

    .section-header p {
      font-size: 1.125rem;
      color: var(--gray-600);
    }

    .settings-grid {
      display: grid;
      gap: var(--space-6);
      margin-bottom: var(--space-8);
    }

    .setting-card {
      background: white;
      border-radius: var(--radius-xl);
      padding: var(--space-6);
      box-shadow: var(--shadow-sm);
      border: 1px solid var(--gray-200);
      transition: all var(--transition-fast);
    }

    .setting-card:hover {
      box-shadow: var(--shadow-md);
      transform: translateY(-2px);
    }

    .setting-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: var(--space-3);
    }

    .setting-header h3 {
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--gray-900);
    }

    .setting-card p {
      color: var(--gray-600);
      margin-bottom: var(--space-4);
    }

    .toggle-input {
      display: none;
    }

    .toggle-label {
      position: relative;
      display: inline-block;
      width: 52px;
      height: 28px;
      cursor: pointer;
    }

    .toggle-slider {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: var(--gray-300);
      border-radius: 28px;
      transition: background-color var(--transition-fast);
    }

    .toggle-slider::before {
      content: '';
      position: absolute;
      height: 20px;
      width: 20px;
      left: 4px;
      bottom: 4px;
      background: white;
      border-radius: 50%;
      transition: transform var(--transition-fast);
      box-shadow: var(--shadow-sm);
    }

    .toggle-input:checked + .toggle-label .toggle-slider {
      background: linear-gradient(135deg, var(--primary-500), var(--secondary-500));
    }

    .toggle-input:checked + .toggle-label .toggle-slider::before {
      transform: translateX(24px);
    }

    .setting-status {
      margin-top: var(--space-3);
      padding: var(--space-2) var(--space-3);
      background: var(--gray-100);
      border-radius: var(--radius-md);
      font-size: 0.875rem;
      font-weight: 500;
      color: var(--gray-700);
    }

    .save-section {
      position: sticky;
      bottom: 0;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-top: 1px solid var(--gray-200);
      padding: var(--space-6);
      margin: var(--space-6) calc(-1 * var(--space-6)) calc(-1 * var(--space-6));
    }

    .save-button {
      display: inline-flex;
      align-items: center;
      gap: var(--space-2);
      padding: var(--space-4) var(--space-6);
      background: linear-gradient(135deg, var(--success-500), #047857);
      color: white;
      border: none;
      border-radius: var(--radius-lg);
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      transition: all var(--transition-fast);
      box-shadow: var(--shadow-md);
    }

    .save-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    }

    .button-icon {
      font-size: 1.125rem;
    }

    .save-status {
      margin-top: var(--space-3);
      padding: var(--space-3) var(--space-4);
      border-radius: var(--radius-md);
      font-size: 0.875rem;
      font-weight: 500;
      display: none;
    }

    .save-status.success {
      background: #dcfce7;
      color: #166534;
      border: 1px solid #bbf7d0;
    }

    .save-status.error {
      background: #fef2f2;
      color: #991b1b;
      border: 1px solid #fecaca;
    }

    @media (max-width: 768px) {
      .main-container {
        grid-template-columns: 1fr;
      }

      .app-nav {
        display: none;
      }

      .header-content {
        flex-direction: column;
        gap: var(--space-4);
        text-align: center;
      }
    }
  </style>
</head>
<body>
  <!-- Loading Screen -->
  <div id="loading-screen" class="loading-screen">
    <div class="loading-content">
      <div class="loading-logo">
        <svg viewBox="0 0 100 100" class="loading-spinner">
          <circle cx="50" cy="50" r="45" stroke="currentColor" stroke-width="8" fill="none" stroke-dasharray="283" stroke-dashoffset="283">
            <animate attributeName="stroke-dashoffset" dur="2s" values="283;0;283" repeatCount="indefinite"/>
          </circle>
        </svg>
        <div class="loading-icon">⚡</div>
      </div>
      <h2>Airtm Monitor Pro</h2>
      <p>Loading configuration panel...</p>
    </div>
  </div>

  <!-- Main Application -->
  <div id="app" class="app hidden">
    <!-- Header -->
    <header class="app-header">
      <div class="header-content">
        <div class="header-brand">
          <div class="brand-icon">⚡</div>
          <div class="brand-text">
            <h1>Airtm Monitor Pro</h1>
            <p>Advanced Trading Configuration</p>
          </div>
        </div>
        <div class="header-status">
          <div id="connection-status" class="status-indicator">
            <div class="status-dot"></div>
            <span>Checking connection...</span>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="app-main">
      <div class="main-container">
        <!-- Navigation Sidebar -->
        <nav class="app-nav">
          <div class="nav-section">
            <h3>Configuration</h3>
            <ul class="nav-list">
              <li><a href="#monitoring" class="nav-link active" data-section="monitoring">
                <span class="nav-icon">👁️</span>
                <span>Monitoring</span>
              </a></li>
              <li><a href="#automation" class="nav-link" data-section="automation">
                <span class="nav-icon">🤖</span>
                <span>Automation</span>
              </a></li>
              <li><a href="#notifications" class="nav-link" data-section="notifications">
                <span class="nav-icon">🔔</span>
                <span>Notifications</span>
              </a></li>
              <li><a href="#filters" class="nav-link" data-section="filters">
                <span class="nav-icon">🔍</span>
                <span>Filters</span>
              </a></li>
              <li><a href="#advanced" class="nav-link" data-section="advanced">
                <span class="nav-icon">⚙️</span>
                <span>Advanced</span>
              </a></li>
            </ul>
          </div>
          <div class="nav-section">
            <h3>Support</h3>
            <ul class="nav-list">
              <li><a href="#help" class="nav-link" data-section="help">
                <span class="nav-icon">❓</span>
                <span>Help</span>
              </a></li>
              <li><a href="#about" class="nav-link" data-section="about">
                <span class="nav-icon">ℹ️</span>
                <span>About</span>
              </a></li>
            </ul>
          </div>
        </nav>

        <!-- Content Area -->
        <div class="app-content">
          <!-- Monitoring Section -->
          <section id="monitoring-section" class="content-section active">
            <div class="section-header">
              <h2>Monitoring Configuration</h2>
              <p>Configure how the extension monitors Airtm for new offers</p>
            </div>

            <div class="settings-grid">
              <div class="setting-card">
                <div class="setting-header">
                  <h3>Monitoring Status</h3>
                  <div class="setting-toggle">
                    <input type="checkbox" id="monitoring-enabled" class="toggle-input">
                    <label for="monitoring-enabled" class="toggle-label">
                      <span class="toggle-slider"></span>
                    </label>
                  </div>
                </div>
                <p>Enable or disable automatic monitoring of Airtm offers</p>
                <div class="setting-status">
                  <span id="monitoring-status-text">Disabled</span>
                </div>
              </div>

              <div class="setting-card">
                <div class="setting-header">
                  <h3>Refresh Interval</h3>
                </div>
                <p>How often to check for new offers (in seconds)</p>
                <div class="setting-input-group">
                  <input type="range" id="refresh-interval" min="5" max="60" value="10" class="range-input">
                  <span id="refresh-interval-value" class="input-value">10s</span>
                </div>
              </div>

              <div class="setting-card">
                <div class="setting-header">
                  <h3>Page Monitoring</h3>
                </div>
                <p>Monitor specific Airtm pages for offers</p>
                <div class="checkbox-group">
                  <label class="checkbox-label">
                    <input type="checkbox" id="monitor-buy-page" checked>
                    <span class="checkbox-custom"></span>
                    <span>Buy Offers Page</span>
                  </label>
                  <label class="checkbox-label">
                    <input type="checkbox" id="monitor-sell-page" checked>
                    <span class="checkbox-custom"></span>
                    <span>Sell Offers Page</span>
                  </label>
                </div>
              </div>
            </div>
          </section>

          <!-- Automation Section -->
          <section id="automation-section" class="content-section">
            <div class="section-header">
              <h2>Automation Settings</h2>
              <p>Configure automatic actions and responses</p>
            </div>

            <div class="settings-grid">
              <div class="setting-card">
                <div class="setting-header">
                  <h3>Auto Accept</h3>
                  <div class="setting-toggle">
                    <input type="checkbox" id="auto-accept-enabled" class="toggle-input">
                    <label for="auto-accept-enabled" class="toggle-label">
                      <span class="toggle-slider"></span>
                    </label>
                  </div>
                </div>
                <p>Automatically accept offers that meet your criteria</p>
                <div class="setting-status">
                  <span id="auto-accept-status-text">Disabled</span>
                </div>
              </div>

              <div class="setting-card">
                <div class="setting-header">
                  <h3>Amount Filters</h3>
                </div>
                <p>Set minimum and maximum amounts for auto-acceptance</p>
                <div class="amount-filters">
                  <div class="filter-group">
                    <label>Minimum Amount ($)</label>
                    <input type="number" id="min-amount" min="0" step="0.01" class="amount-input">
                  </div>
                  <div class="filter-group">
                    <label>Maximum Amount ($)</label>
                    <input type="number" id="max-amount" min="0" step="0.01" class="amount-input">
                  </div>
                </div>
              </div>
            </div>
          </section>

          <!-- Notifications Section -->
          <section id="notifications-section" class="content-section">
            <div class="section-header">
              <h2>Notification Settings</h2>
              <p>Configure how you receive alerts about new offers</p>
            </div>

            <div class="settings-grid">
              <div class="setting-card">
                <div class="setting-header">
                  <h3>Telegram Integration</h3>
                </div>
                <p>Send notifications to your Telegram account</p>
                <div class="telegram-config">
                  <div class="input-group">
                    <label>Bot Token</label>
                    <input type="password" id="telegram-bot-token" placeholder="Enter your bot token" class="text-input">
                  </div>
                  <div class="input-group">
                    <label>Chat ID</label>
                    <input type="text" id="telegram-chat-id" placeholder="Enter your chat ID" class="text-input">
                  </div>
                  <button id="test-telegram" class="test-button">Test Connection</button>
                </div>
              </div>
            </div>
          </section>

          <!-- Help Section -->
          <section id="help-section" class="content-section">
            <div class="section-header">
              <h2>Help & Documentation</h2>
              <p>Learn how to use Airtm Monitor Pro effectively</p>
            </div>

            <div class="help-content">
              <div class="help-card">
                <h3>🚀 Getting Started</h3>
                <p>Follow these steps to set up your monitoring:</p>
                <ol>
                  <li>Enable monitoring in the Monitoring section</li>
                  <li>Configure your preferred refresh interval</li>
                  <li>Set up Telegram notifications (optional)</li>
                  <li>Configure automation rules (optional)</li>
                </ol>
              </div>

              <div class="help-card">
                <h3>🔧 Troubleshooting</h3>
                <p>Common issues and solutions:</p>
                <ul>
                  <li><strong>Extension not working:</strong> Check if you're logged into Airtm</li>
                  <li><strong>No notifications:</strong> Verify your Telegram bot configuration</li>
                  <li><strong>Auto-accept not working:</strong> Check your filter settings</li>
                </ul>
              </div>
            </div>
          </section>

          <!-- About Section -->
          <section id="about-section" class="content-section">
            <div class="section-header">
              <h2>About Airtm Monitor Pro</h2>
              <p>Information about this extension</p>
            </div>

            <div class="about-content">
              <div class="about-card">
                <h3>Version Information</h3>
                <p><strong>Version:</strong> 2.0.0</p>
                <p><strong>Build:</strong> Modern Vanilla Architecture</p>
                <p><strong>Last Updated:</strong> <span id="last-updated"></span></p>
              </div>

              <div class="about-card">
                <h3>Features</h3>
                <ul>
                  <li>✅ Real-time offer monitoring</li>
                  <li>✅ Telegram notifications</li>
                  <li>✅ Auto-accept functionality</li>
                  <li>✅ Advanced filtering</li>
                  <li>✅ CSP-compliant architecture</li>
                  <li>✅ Modern responsive design</li>
                </ul>
              </div>
            </div>
          </section>

          <!-- Save Button -->
          <div class="save-section">
            <button id="save-settings" class="save-button">
              <span class="button-icon">💾</span>
              <span>Save Configuration</span>
            </button>
            <div id="save-status" class="save-status hidden"></div>
          </div>
        </div>
      </div>
    </main>
  </div>

  <!-- External Script -->
  <script src="app.js"></script>
</body>
</html>
