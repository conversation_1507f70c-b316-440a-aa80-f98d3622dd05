/**
 * Popup Entry Point
 */

import { createRoot } from 'react-dom/client'
import { Popup } from './Popup'
import '../styles/globals.css'

// Enforce popup dimensions immediately
function enforcePopupDimensions() {
  const html = document.documentElement
  const body = document.body

  // Set dimensions with !important via JavaScript
  const style = `
    width: 600px !important;
    height: 850px !important;
    min-width: 600px !important;
    min-height: 850px !important;
    max-width: 600px !important;
    max-height: 850px !important;
    margin: 0 !important;
    padding: 0 !important;
    overflow: hidden !important;
  `

  html.style.cssText = style
  body.style.cssText = style

  // Also set the body class
  body.className = 'popup'

  console.log('Popup dimensions enforced:', {
    htmlSize: `${html.offsetWidth}x${html.offsetHeight}`,
    bodySize: `${body.offsetWidth}x${body.offsetHeight}`,
    windowSize: `${window.innerWidth}x${window.innerHeight}`
  })
}

// Enforce dimensions immediately and on load
enforcePopupDimensions()
document.addEventListener('DOMContentLoaded', enforcePopupDimensions)
window.addEventListener('load', enforcePopupDimensions)

const container = document.getElementById('root')
if (!container) {
  throw new Error('Root element not found')
}

// Enforce root container dimensions
container.style.cssText = `
  width: 600px !important;
  height: 850px !important;
  min-width: 600px !important;
  min-height: 850px !important;
  max-width: 600px !important;
  max-height: 850px !important;
  display: flex !important;
  flex-direction: column !important;
`

const root = createRoot(container)
root.render(<Popup />)
