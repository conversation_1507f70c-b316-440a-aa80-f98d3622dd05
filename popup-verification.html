<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Popup Verification - Airtm Monitor Pro</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #eff6ff 50%, #ecfdf5 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .title {
            background: linear-gradient(135deg, #3b82f6, #10b981);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 2.5rem;
            font-weight: bold;
            text-align: center;
            margin-bottom: 16px;
        }
        
        .subtitle {
            text-align: center;
            color: #64748b;
            font-size: 1.2rem;
            margin-bottom: 40px;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }
        
        .test-section {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
        }
        
        .test-title {
            font-size: 1.5rem;
            font-weight: bold;
            color: #1e293b;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .test-icon {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #3b82f6, #10b981);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }
        
        .popup-frame {
            width: 600px;
            height: 850px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            margin: 0 auto 24px;
            background: white;
        }
        
        .popup-iframe {
            width: 100%;
            height: 100%;
            border: none;
            display: block;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
            margin-top: 20px;
        }
        
        .status-item {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 16px;
            text-align: center;
        }
        
        .status-label {
            font-size: 14px;
            color: #64748b;
            margin-bottom: 8px;
        }
        
        .status-value {
            font-size: 18px;
            font-weight: bold;
            color: #1e293b;
            font-family: monospace;
        }
        
        .instructions {
            background: linear-gradient(135deg, #eff6ff, #ecfdf5);
            border: 1px solid #bfdbfe;
            border-radius: 12px;
            padding: 24px;
            margin-top: 40px;
        }
        
        .instructions h3 {
            color: #1e40af;
            margin-top: 0;
            margin-bottom: 16px;
        }
        
        .instructions ol {
            color: #1e40af;
            line-height: 1.6;
        }
        
        .instructions li {
            margin-bottom: 8px;
        }
        
        .success-indicator {
            background: #dcfce7;
            border: 1px solid #86efac;
            color: #166534;
            padding: 12px 16px;
            border-radius: 8px;
            margin-top: 16px;
            font-weight: 600;
        }
        
        .error-indicator {
            background: #fef2f2;
            border: 1px solid #fecaca;
            color: #991b1b;
            padding: 12px 16px;
            border-radius: 8px;
            margin-top: 16px;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">Popup Verification Test</h1>
        <p class="subtitle">Testing the fixed 600x850px popup dimensions</p>
        
        <div class="test-grid">
            <div class="test-section">
                <h2 class="test-title">
                    <div class="test-icon">🖥️</div>
                    Direct Popup Test
                </h2>
                <p>This iframe simulates the browser extension popup environment:</p>
                
                <div class="popup-frame">
                    <iframe 
                        src="./dist/src/popup/index.html" 
                        class="popup-iframe"
                        title="Popup Test"
                        id="popup-iframe">
                    </iframe>
                </div>
                
                <div class="status-grid">
                    <div class="status-item">
                        <div class="status-label">Expected Size</div>
                        <div class="status-value">600×850</div>
                    </div>
                    <div class="status-item">
                        <div class="status-label">Actual Size</div>
                        <div class="status-value" id="actual-size">Loading...</div>
                    </div>
                    <div class="status-item">
                        <div class="status-label">Status</div>
                        <div class="status-value" id="status">Testing...</div>
                    </div>
                    <div class="status-item">
                        <div class="status-label">Load Time</div>
                        <div class="status-value" id="load-time">0ms</div>
                    </div>
                </div>
                
                <div id="result-indicator"></div>
            </div>
            
            <div class="test-section">
                <h2 class="test-title">
                    <div class="test-icon">🔧</div>
                    Extension Installation
                </h2>
                <p>Follow these steps to test the actual browser extension:</p>
                
                <div class="instructions">
                    <h3>📋 Installation Steps:</h3>
                    <ol>
                        <li>Open Chrome and navigate to <code>chrome://extensions/</code></li>
                        <li>Enable "Developer mode" (toggle in top right corner)</li>
                        <li>Click "Load unpacked" button</li>
                        <li>Select the <code>dist</code> folder from this project</li>
                        <li>The extension should appear in your extensions list</li>
                        <li>Click the extension icon in the toolbar to test</li>
                    </ol>
                    
                    <h3>✅ Expected Results:</h3>
                    <ul>
                        <li>Popup opens at exactly 600×850 pixels</li>
                        <li>Modern glass-morphism design is visible</li>
                        <li>All controls and buttons are clearly visible</li>
                        <li>Header shows "Airtm Monitor Pro" with status indicator</li>
                        <li>Control panel with monitoring toggles is functional</li>
                    </ul>
                    
                    <h3>🐛 Troubleshooting:</h3>
                    <ul>
                        <li>If popup is still small, reload the extension</li>
                        <li>Clear browser cache and try again</li>
                        <li>Check browser console for error messages</li>
                        <li>Test in incognito mode</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        const startTime = Date.now();
        
        function updateStatus() {
            const iframe = document.getElementById('popup-iframe');
            const actualSizeEl = document.getElementById('actual-size');
            const statusEl = document.getElementById('status');
            const loadTimeEl = document.getElementById('load-time');
            const resultEl = document.getElementById('result-indicator');
            
            if (iframe) {
                const rect = iframe.getBoundingClientRect();
                const width = Math.round(rect.width);
                const height = Math.round(rect.height);
                
                actualSizeEl.textContent = `${width}×${height}`;
                loadTimeEl.textContent = `${Date.now() - startTime}ms`;
                
                if (width === 600 && height === 850) {
                    statusEl.textContent = '✅ PASS';
                    statusEl.style.color = '#059669';
                    resultEl.innerHTML = '<div class="success-indicator">🎉 Popup dimensions are correct! The fix is working properly.</div>';
                } else {
                    statusEl.textContent = '❌ FAIL';
                    statusEl.style.color = '#dc2626';
                    resultEl.innerHTML = '<div class="error-indicator">⚠️ Popup dimensions are incorrect. Expected 600×850, got ' + width + '×' + height + '</div>';
                }
            }
        }
        
        // Update status every second
        setInterval(updateStatus, 1000);
        
        // Initial update
        setTimeout(updateStatus, 500);
        
        // Update on iframe load
        document.getElementById('popup-iframe').addEventListener('load', () => {
            setTimeout(updateStatus, 100);
        });
    </script>
</body>
</html>
