function e(){var t;try{return!!((t=chrome==null?void 0:chrome.runtime)!=null&&t.id)}catch{return!1}}function i(){if(!e())throw new Error("Extension context is invalid. Please reload the extension or refresh the page.")}function o(t){const n=t instanceof Error?t.message:String(t);return(n==null?void 0:n.includes("Could not establish connection"))||(n==null?void 0:n.includes("Receiving end does not exist"))||(n==null?void 0:n.includes("Extension context invalidated"))}async function s(t){i();try{return await chrome.runtime.sendMessage(t)}catch(n){throw o(n)?new Error("Extension context lost during message sending. Please reload the extension."):n}}export{o as a,e as i,s};
