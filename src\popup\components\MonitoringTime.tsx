import React, { useState, useEffect } from 'react';
import { MonitoringTimeProps } from '../types';

const MonitoringTime: React.FC<MonitoringTimeProps> = ({ 
  monitoringStartTime, 
  isMonitoring 
}) => {
  const [displayTime, setDisplayTime] = useState('00:00');

  useEffect(() => {
    const updateTime = () => {
      if (isMonitoring && monitoringStartTime) {
        const elapsed = Date.now() - monitoringStartTime;
        const minutes = Math.floor(elapsed / 60000);
        const seconds = Math.floor((elapsed % 60000) / 1000);
        setDisplayTime(
          `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
        );
      } else {
        setDisplayTime('00:00');
      }
    };

    // Update immediately
    updateTime();

    // Update every second
    const interval = setInterval(updateTime, 1000);

    return () => clearInterval(interval);
  }, [isMonitoring, monitoringStartTime]);

  return <>{displayTime}</>;
};

export default MonitoringTime;
