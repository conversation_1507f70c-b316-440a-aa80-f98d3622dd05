/**
 * CSP-Compliant Error Handler for Options Page
 * Handles initialization errors and provides user feedback
 */

(function() {
  'use strict';
  
  // Track initialization state
  let initializationTimeout;
  let hasReactLoaded = false;
  
  // Check if <PERSON><PERSON> has loaded successfully
  function checkReactInitialization() {
    const root = document.getElementById('root');
    if (!root) return;
    
    // Check if <PERSON>act has rendered content (no longer showing initial loading)
    const hasInitialLoading = root.innerHTML.includes('initial-loading');
    const hasReactContent = root.innerHTML.includes('min-h-screen bg-gradient-to-br') || 
                           root.innerHTML.includes('options-loading') ||
                           root.innerHTML.includes('options-error');
    
    if (hasReactContent && !hasInitialLoading) {
      hasReactLoaded = true;
      clearTimeout(initializationTimeout);
      console.log('✅ React options page loaded successfully');
      return;
    }
    
    // If still showing initial loading after timeout, show error
    if (hasInitialLoading) {
      showInitializationError();
    }
  }
  
  // Show initialization error UI
  function showInitializationError() {
    const root = document.getElementById('root');
    if (!root || hasReactLoaded) return;
    
    console.error('❌ Options page failed to initialize within timeout');
    
    root.innerHTML = `
      <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; min-height: 100vh; color: white; text-align: center; padding: 2rem; background: linear-gradient(135deg, #1e1b4b, #0f172a, #312e81); font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;">
        <div style="max-width: 600px;">
          <div style="width: 64px; height: 64px; margin: 0 auto 2rem; background: linear-gradient(135deg, #ef4444, #dc2626); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
            <svg style="width: 32px; height: 32px; color: white;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
          </div>
          
          <h1 style="color: #ef4444; font-size: 2.5rem; margin-bottom: 1rem; font-weight: bold;">Configuration Error</h1>
          
          <p style="color: #94a3b8; font-size: 1.25rem; margin-bottom: 2rem; line-height: 1.6;">
            The Airtm Monitor options page failed to load properly. This may be due to Content Security Policy restrictions or missing resources.
          </p>
          
          <div style="background: rgba(239, 68, 68, 0.1); border: 1px solid rgba(239, 68, 68, 0.3); border-radius: 0.75rem; padding: 1.5rem; margin-bottom: 2rem; text-align: left;">
            <h3 style="color: #fca5a5; font-size: 1rem; font-weight: 600; margin-bottom: 0.5rem;">Possible Causes:</h3>
            <ul style="color: #fca5a5; font-size: 0.875rem; line-height: 1.5; margin: 0; padding-left: 1.5rem;">
              <li>Content Security Policy (CSP) violations</li>
              <li>Missing or corrupted extension files</li>
              <li>Browser compatibility issues</li>
              <li>Extension permissions not granted</li>
            </ul>
          </div>
          
          <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap; margin-bottom: 2rem;">
            <button onclick="window.location.reload()" style="background: linear-gradient(to right, #8b5cf6, #7c3aed); color: white; padding: 1rem 2rem; border-radius: 0.75rem; border: none; cursor: pointer; font-weight: 600; font-size: 1rem; transition: transform 0.2s; box-shadow: 0 4px 12px rgba(139, 92, 246, 0.25);" onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
              🔄 Reload Page
            </button>
            <button onclick="openExtensionManager()" style="background: linear-gradient(to right, #059669, #047857); color: white; padding: 1rem 2rem; border-radius: 0.75rem; border: none; cursor: pointer; font-weight: 600; font-size: 1rem; transition: transform 0.2s; box-shadow: 0 4px 12px rgba(5, 150, 105, 0.25);" onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
              ⚙️ Extension Settings
            </button>
          </div>
          
          <details style="text-align: left; background: rgba(15, 23, 42, 0.5); border-radius: 0.75rem; padding: 1rem;">
            <summary style="color: #a78bfa; cursor: pointer; font-weight: 600; margin-bottom: 1rem;">🔧 Advanced Troubleshooting</summary>
            <div style="color: #cbd5e1; font-size: 0.875rem; line-height: 1.6;">
              <p><strong>1. Check Browser Console:</strong> Press F12 and look for error messages in the Console tab.</p>
              <p><strong>2. Verify Extension Permissions:</strong> Go to chrome://extensions/ and ensure all permissions are granted.</p>
              <p><strong>3. Clear Extension Data:</strong> Try disabling and re-enabling the extension.</p>
              <p><strong>4. Update Browser:</strong> Ensure you're using the latest version of Chrome.</p>
              <p><strong>5. Contact Support:</strong> If issues persist, report the problem with console error details.</p>
            </div>
          </details>
        </div>
      </div>
    `;
  }
  
  // Open Chrome extension manager
  function openExtensionManager() {
    if (chrome && chrome.tabs) {
      chrome.tabs.create({ url: 'chrome://extensions/' });
    } else {
      window.open('chrome://extensions/', '_blank');
    }
  }
  
  // Make function globally available
  window.openExtensionManager = openExtensionManager;
  
  // Set initialization timeout
  initializationTimeout = setTimeout(checkReactInitialization, 8000); // 8 second timeout
  
  // Also check periodically in case of slow loading
  const checkInterval = setInterval(() => {
    if (hasReactLoaded) {
      clearInterval(checkInterval);
      return;
    }
    checkReactInitialization();
  }, 2000);
  
  // Clear interval after 15 seconds to avoid infinite checking
  setTimeout(() => {
    clearInterval(checkInterval);
  }, 15000);
  
  console.log('🛡️ CSP-compliant error handler initialized');
})();
