import{r as d,j as e,c as T}from"./globals-810552d8.js";import{D as B}from"./index-357a2da0.js";/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const z=r=>r.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),P=r=>r.replace(/^([A-Z])|[\s-_]+(\w)/g,(s,a,o)=>o?o.toUpperCase():a.toLowerCase()),C=r=>{const s=P(r);return s.charAt(0).toUpperCase()+s.slice(1)},E=(...r)=>r.filter((s,a,o)=>!!s&&s.trim()!==""&&o.indexOf(s)===a).join(" ").trim(),$=r=>{for(const s in r)if(s.startsWith("aria-")||s==="role"||s==="title")return!0};/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var G={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const I=d.forwardRef(({color:r="currentColor",size:s=24,strokeWidth:a=2,absoluteStrokeWidth:o,className:p="",children:n,iconNode:m,...c},x)=>d.createElement("svg",{ref:x,...G,width:s,height:s,stroke:r,strokeWidth:o?Number(a)*24/Number(s):a,className:E("lucide",p),...!n&&!$(c)&&{"aria-hidden":"true"},...c},[...m.map(([f,u])=>d.createElement(f,u)),...Array.isArray(n)?n:[n]]));/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const N=(r,s)=>{const a=d.forwardRef(({className:o,...p},n)=>d.createElement(I,{ref:n,iconNode:s,className:E(`lucide-${z(C(r))}`,`lucide-${r}`,o),...p}));return a.displayName=C(r),a};/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const K=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],W=N("check",K);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const D=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]],R=N("plus",D);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],y=N("x",_),v=({label:r,icon:s,value:a,onChange:o,placeholder:p,className:n=""})=>{const[m,c]=d.useState(""),[x,f]=d.useState([]),[u,l]=d.useState(!1),t=i=>{if(i.key==="Enter")if(i.preventDefault(),u)if(m.trim()){const h=[...x,m.trim()];f(h),c("")}else b();else m.trim()&&(f([m.trim()]),c(""),l(!0));else i.key==="Escape"&&(u?g():c(""))},b=()=>{x.length>0&&o([...a,...x]),f([]),l(!1),c("")},g=()=>{f([]),l(!1),c("")},S=i=>{const h=x.filter((L,w)=>w!==i);f(h),h.length===0&&l(!1)},M=i=>{const h=a.filter((L,w)=>w!==i);o(h)},A=()=>u?x.length===0?"Type next keyword and press Enter...":"Add another keyword or press Enter on empty to finish group":p;return e.jsxs("div",{className:`space-y-3 ${n}`,children:[e.jsxs("label",{className:"text-lg font-bold text-white flex items-center space-x-2",children:[e.jsx("span",{children:s}),e.jsx("span",{children:r})]}),e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",value:m,onChange:i=>c(i.target.value),onKeyDown:t,className:`w-full px-6 py-4 bg-slate-800/60 border rounded-2xl text-white text-lg placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-violet-500/50 focus:border-violet-500/50 backdrop-blur-sm transition-all duration-300 hover:border-violet-500/30 ${u?"border-yellow-500/50 ring-2 ring-yellow-500/20 bg-yellow-500/5":"border-slate-600/50"}`,placeholder:A()}),u&&e.jsxs("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center space-x-2",children:[e.jsx("span",{className:"text-yellow-400 text-sm font-medium",children:"Grouping Mode"}),e.jsx("div",{className:"w-2 h-2 bg-yellow-400 rounded-full animate-pulse"})]})]}),u&&e.jsxs("div",{className:"p-4 bg-gradient-to-r from-yellow-500/10 to-orange-500/10 border border-yellow-500/30 rounded-2xl backdrop-blur-sm",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsxs("h4",{className:"text-yellow-300 font-bold flex items-center space-x-2",children:[e.jsx(R,{className:"w-4 h-4"}),e.jsxs("span",{children:["Building Group (",x.length," items)"]})]}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsxs("button",{onClick:b,className:"px-3 py-1 bg-green-500/20 text-green-300 rounded-lg text-sm font-medium hover:bg-green-500/30 transition-colors flex items-center space-x-1",children:[e.jsx(W,{className:"w-3 h-3"}),e.jsx("span",{children:"Finish"})]}),e.jsxs("button",{onClick:g,className:"px-3 py-1 bg-red-500/20 text-red-300 rounded-lg text-sm font-medium hover:bg-red-500/30 transition-colors flex items-center space-x-1",children:[e.jsx(y,{className:"w-3 h-3"}),e.jsx("span",{children:"Cancel"})]})]})]}),x.length>0&&e.jsxs("div",{className:"space-y-2",children:[e.jsx("p",{className:"text-yellow-200/80 text-sm",children:"Keywords in current group:"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:x.map((i,h)=>e.jsxs("span",{className:"inline-flex items-center space-x-1 px-3 py-1 bg-yellow-500/20 text-yellow-200 rounded-lg text-sm border border-yellow-500/30",children:[e.jsx("span",{children:i}),e.jsx("button",{onClick:()=>S(h),className:"text-yellow-300 hover:text-red-300 transition-colors",children:e.jsx(y,{className:"w-3 h-3"})})]},h))})]}),e.jsxs("div",{className:"mt-3 text-yellow-200/70 text-xs space-y-1",children:[e.jsxs("p",{children:["• Press ",e.jsx("kbd",{className:"px-1 py-0.5 bg-yellow-500/20 rounded text-yellow-300",children:"Enter"})," to add current keyword to group"]}),e.jsxs("p",{children:["• Press ",e.jsx("kbd",{className:"px-1 py-0.5 bg-yellow-500/20 rounded text-yellow-300",children:"Enter"})," on empty input to finish group"]}),e.jsxs("p",{children:["• Press ",e.jsx("kbd",{className:"px-1 py-0.5 bg-yellow-500/20 rounded text-yellow-300",children:"Escape"})," to cancel grouping"]})]})]}),a.length>0&&e.jsxs("div",{className:"space-y-3",children:[e.jsxs("p",{className:"text-slate-300 text-sm font-medium",children:["Current ",r.toLowerCase(),":"]}),e.jsx("div",{className:"flex flex-wrap gap-2",children:a.map((i,h)=>e.jsxs("span",{className:"inline-flex items-center space-x-2 px-4 py-2 bg-violet-500/20 text-violet-200 rounded-xl text-sm border border-violet-500/30 hover:border-violet-400/50 transition-all duration-200",children:[e.jsx("span",{children:i}),e.jsx("button",{onClick:()=>M(h),className:"text-violet-300 hover:text-red-300 transition-colors",children:e.jsx(y,{className:"w-4 h-4"})})]},h))})]})]})},U=()=>{const[r,s]=d.useState(B),[a,o]=d.useState(!1),[p,n]=d.useState(""),[m,c]=d.useState(!1);d.useEffect(()=>{f(),x()},[]);const x=async()=>{try{await chrome.runtime.sendMessage({type:"PING"}),c(!0)}catch{c(!1),console.log("Background script not responding")}},f=async()=>{try{const t=await chrome.storage.sync.get("settings");t.settings&&s(t.settings)}catch(t){console.error("Error loading settings:",t)}},u=async()=>{try{o(!0),n(""),await chrome.storage.sync.set({settings:r});try{await chrome.runtime.sendMessage({type:"SETTINGS_UPDATE",settings:r})}catch(t){console.log("Background script not available:",t)}n("Settings saved successfully!"),setTimeout(()=>n(""),3e3)}catch(t){console.error("Error saving settings:",t),n("Error saving settings")}finally{o(!1)}},l=(t,b)=>{s(g=>({...g,[t]:b}))};return e.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-violet-950 via-slate-900 to-indigo-950",children:[e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-violet-600/20 via-purple-600/20 to-indigo-600/20 blur-3xl"}),e.jsx("div",{className:"relative backdrop-blur-xl bg-black/30 border-b border-violet-500/20",children:e.jsx("div",{className:"max-w-7xl mx-auto px-8 py-8",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"space-y-2",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-12 h-12 bg-gradient-to-br from-violet-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg",children:e.jsxs("svg",{className:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-4xl font-bold bg-gradient-to-r from-white via-violet-200 to-purple-200 bg-clip-text text-transparent",children:"Airtm Monitor"}),e.jsx("p",{className:"text-violet-300 text-lg font-medium",children:"Professional Configuration Center"})]})]})}),e.jsxs("div",{className:"flex items-center space-x-6",children:[e.jsxs("div",{className:`px-6 py-3 rounded-2xl text-sm font-bold flex items-center space-x-3 transition-all duration-500 ${m?"bg-gradient-to-r from-emerald-500/20 to-green-500/20 text-emerald-300 border border-emerald-500/30 shadow-lg shadow-emerald-500/10":"bg-gradient-to-r from-red-500/20 to-pink-500/20 text-red-300 border border-red-500/30 shadow-lg shadow-red-500/10"}`,children:[e.jsx("div",{className:`w-3 h-3 rounded-full animate-pulse ${m?"bg-emerald-400 shadow-lg shadow-emerald-400/50":"bg-red-400 shadow-lg shadow-red-400/50"}`}),e.jsx("span",{children:m?"System Online":"System Offline"})]}),e.jsx("button",{onClick:u,disabled:a,className:`px-8 py-4 rounded-2xl font-bold text-lg transition-all duration-500 transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-violet-500/30 ${a?"bg-gradient-to-r from-slate-600 to-slate-700 text-slate-400 cursor-not-allowed":"bg-gradient-to-r from-violet-600 to-purple-600 text-white hover:from-violet-500 hover:to-purple-500 shadow-2xl shadow-violet-500/25 hover:shadow-violet-500/40"}`,children:a?e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-2 border-slate-400 border-t-transparent"}),e.jsx("span",{children:"Saving Configuration..."})]}):e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),e.jsx("span",{children:"Save Configuration"})]})})]})]})})})]}),e.jsxs("div",{className:"max-w-7xl mx-auto px-8 py-12",children:[p&&e.jsx("div",{className:`mb-8 p-6 rounded-3xl backdrop-blur-xl border transition-all duration-500 ${p.includes("Error")?"bg-red-500/10 border-red-500/30 text-red-300 shadow-lg shadow-red-500/10":"bg-emerald-500/10 border-emerald-500/30 text-emerald-300 shadow-lg shadow-emerald-500/10"}`,children:e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:`w-4 h-4 rounded-full animate-pulse ${p.includes("Error")?"bg-red-400 shadow-lg shadow-red-400/50":"bg-emerald-400 shadow-lg shadow-emerald-400/50"}`}),e.jsx("span",{className:"font-bold text-lg",children:p})]})}),e.jsxs("div",{className:"grid gap-8",children:[e.jsxs("section",{className:"relative group",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-cyan-500/10 via-blue-500/10 to-violet-500/10 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-500"}),e.jsxs("div",{className:"relative backdrop-blur-xl bg-black/40 border border-cyan-500/20 rounded-3xl p-8 hover:border-cyan-400/30 transition-all duration-500",children:[e.jsxs("div",{className:"flex items-center space-x-4 mb-8",children:[e.jsx("div",{className:"w-14 h-14 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-xl shadow-cyan-500/25",children:e.jsx("svg",{className:"w-7 h-7 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"})})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-3xl font-bold bg-gradient-to-r from-cyan-300 to-blue-300 bg-clip-text text-transparent",children:"Core System"}),e.jsx("p",{className:"text-cyan-200/80 text-lg",children:"Essential monitoring controls"})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between p-6 bg-gradient-to-r from-slate-800/50 to-slate-700/50 rounded-2xl border border-slate-600/30 hover:border-cyan-500/30 transition-all duration-300",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-lg font-bold text-white",children:"System Monitoring"}),e.jsx("p",{className:"text-slate-300",children:"Enable real-time offer detection and processing"})]}),e.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[e.jsx("input",{type:"checkbox",checked:r.monitoring,onChange:t=>l("monitoring",t.target.checked),className:"sr-only peer"}),e.jsx("div",{className:"w-14 h-8 bg-slate-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-cyan-500/30 rounded-full peer peer-checked:after:translate-x-6 peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-7 after:w-7 after:transition-all peer-checked:bg-gradient-to-r peer-checked:from-cyan-500 peer-checked:to-blue-500 peer-checked:shadow-lg peer-checked:shadow-cyan-500/25"})]})]}),e.jsxs("div",{className:"flex items-center justify-between p-6 bg-gradient-to-r from-slate-800/50 to-slate-700/50 rounded-2xl border border-slate-600/30 hover:border-cyan-500/30 transition-all duration-300",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-lg font-bold text-white",children:"Auto-Accept Mode"}),e.jsx("p",{className:"text-slate-300",children:"Automatically accept offers matching your criteria"})]}),e.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[e.jsx("input",{type:"checkbox",checked:r.autoAccept,onChange:t=>l("autoAccept",t.target.checked),className:"sr-only peer"}),e.jsx("div",{className:"w-14 h-8 bg-slate-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-cyan-500/30 rounded-full peer peer-checked:after:translate-x-6 peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-7 after:w-7 after:transition-all peer-checked:bg-gradient-to-r peer-checked:from-cyan-500 peer-checked:to-blue-500 peer-checked:shadow-lg peer-checked:shadow-cyan-500/25"})]})]})]})]})]}),e.jsxs("section",{className:"relative group",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-emerald-500/10 via-green-500/10 to-teal-500/10 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-500"}),e.jsxs("div",{className:"relative backdrop-blur-xl bg-black/40 border border-emerald-500/20 rounded-3xl p-8 hover:border-emerald-400/30 transition-all duration-500",children:[e.jsxs("div",{className:"flex items-center space-x-4 mb-8",children:[e.jsx("div",{className:"w-14 h-14 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center shadow-xl shadow-emerald-500/25",children:e.jsxs("svg",{className:"w-7 h-7 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 17h5l-5 5v-5z"}),e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 19.5A2.5 2.5 0 0 1 6.5 17H20"})]})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-3xl font-bold bg-gradient-to-r from-emerald-300 to-teal-300 bg-clip-text text-transparent",children:"Alert System"}),e.jsx("p",{className:"text-emerald-200/80 text-lg",children:"Notification preferences and alerts"})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between p-6 bg-gradient-to-r from-slate-800/50 to-slate-700/50 rounded-2xl border border-slate-600/30 hover:border-emerald-500/30 transition-all duration-300",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-lg font-bold text-white",children:"Desktop Notifications"}),e.jsx("p",{className:"text-slate-300",children:"System notifications for new matching offers"})]}),e.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[e.jsx("input",{type:"checkbox",checked:r.notificationsEnabled,onChange:t=>l("notificationsEnabled",t.target.checked),className:"sr-only peer"}),e.jsx("div",{className:"w-14 h-8 bg-slate-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-emerald-500/30 rounded-full peer peer-checked:after:translate-x-6 peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-7 after:w-7 after:transition-all peer-checked:bg-gradient-to-r peer-checked:from-emerald-500 peer-checked:to-teal-500 peer-checked:shadow-lg peer-checked:shadow-emerald-500/25"})]})]}),e.jsxs("div",{className:"flex items-center justify-between p-6 bg-gradient-to-r from-slate-800/50 to-slate-700/50 rounded-2xl border border-slate-600/30 hover:border-emerald-500/30 transition-all duration-300",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-lg font-bold text-white",children:"Audio Alerts"}),e.jsx("p",{className:"text-slate-300",children:"Sound notifications for instant awareness"})]}),e.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[e.jsx("input",{type:"checkbox",checked:r.soundEnabled,onChange:t=>l("soundEnabled",t.target.checked),className:"sr-only peer"}),e.jsx("div",{className:"w-14 h-8 bg-slate-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-emerald-500/30 rounded-full peer peer-checked:after:translate-x-6 peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-7 after:w-7 after:transition-all peer-checked:bg-gradient-to-r peer-checked:from-emerald-500 peer-checked:to-teal-500 peer-checked:shadow-lg peer-checked:shadow-emerald-500/25"})]})]}),e.jsx("div",{className:"p-6 bg-gradient-to-r from-amber-500/10 to-orange-500/10 border border-amber-500/30 rounded-2xl backdrop-blur-sm",children:e.jsxs("div",{className:"flex items-start space-x-4",children:[e.jsx("div",{className:"w-6 h-6 text-amber-400 mt-1 flex-shrink-0",children:e.jsx("svg",{fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})})}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-bold text-amber-300",children:"Permission Required"}),e.jsx("p",{className:"text-amber-200/90 mt-2 leading-relaxed",children:"Enable browser notifications for this extension. Look for the notification icon in your address bar and grant permission for the best experience."})]})]})})]})]})]}),e.jsxs("section",{className:"relative group",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-violet-500/10 via-purple-500/10 to-fuchsia-500/10 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-500"}),e.jsxs("div",{className:"relative backdrop-blur-xl bg-black/40 border border-violet-500/20 rounded-3xl p-8 hover:border-violet-400/30 transition-all duration-500",children:[e.jsxs("div",{className:"flex items-center space-x-4 mb-8",children:[e.jsx("div",{className:"w-14 h-14 bg-gradient-to-br from-violet-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-xl shadow-violet-500/25",children:e.jsx("svg",{className:"w-7 h-7 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"})})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-3xl font-bold bg-gradient-to-r from-violet-300 to-purple-300 bg-clip-text text-transparent",children:"Smart Filters"}),e.jsx("p",{className:"text-violet-200/80 text-lg",children:"Advanced offer matching criteria"})]})]}),e.jsxs("div",{className:"space-y-8",children:[e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsxs("label",{className:"text-lg font-bold text-white flex items-center space-x-2",children:[e.jsx("span",{children:"💰"}),e.jsx("span",{children:"Minimum Amount"})]}),e.jsx("input",{type:"number",value:r.minAmount,onChange:t=>l("minAmount",parseFloat(t.target.value)||0),className:"w-full px-6 py-4 bg-slate-800/60 border border-slate-600/50 rounded-2xl text-white text-lg placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-violet-500/50 focus:border-violet-500/50 backdrop-blur-sm transition-all duration-300 hover:border-violet-500/30",placeholder:"0.00"})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("label",{className:"text-lg font-bold text-white flex items-center space-x-2",children:[e.jsx("span",{children:"💎"}),e.jsx("span",{children:"Maximum Amount"})]}),e.jsx("input",{type:"number",value:r.maxAmount,onChange:t=>l("maxAmount",parseFloat(t.target.value)||0),className:"w-full px-6 py-4 bg-slate-800/60 border border-slate-600/50 rounded-2xl text-white text-lg placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-violet-500/50 focus:border-violet-500/50 backdrop-blur-sm transition-all duration-300 hover:border-violet-500/30",placeholder:"0.00"})]})]}),e.jsx(v,{label:"Preferred Currencies",icon:"🌍",value:r.preferredCurrencies,onChange:t=>l("preferredCurrencies",t),placeholder:"Type currency and press Enter to start grouping (e.g., USD, EUR, GBP, BTC)"}),e.jsx(v,{label:"Keywords",icon:"🔍",value:r.keywords,onChange:t=>l("keywords",t),placeholder:"Type keyword and press Enter to start grouping (e.g., bitcoin, crypto, exchange)"}),e.jsx(v,{label:"Blacklist Keywords",icon:"🚫",value:r.blacklistKeywords,onChange:t=>l("blacklistKeywords",t),placeholder:"Type keyword and press Enter to start grouping (e.g., scam, fraud, avoid)"}),e.jsx(v,{label:"Payment Methods",icon:"💳",value:r.paymentMethods,onChange:t=>l("paymentMethods",t),placeholder:"Type payment method and press Enter to start grouping (e.g., PayPal, Bank Transfer, Wise)"}),e.jsx(v,{label:"Target Countries",icon:"🗺️",value:r.countries,onChange:t=>l("countries",t),placeholder:"Type country and press Enter to start grouping (e.g., US, UK, CA, DE, AU)"})]})]})]}),e.jsxs("section",{className:"relative group",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-500/10 via-indigo-500/10 to-purple-500/10 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-500"}),e.jsxs("div",{className:"relative backdrop-blur-xl bg-black/40 border border-blue-500/20 rounded-3xl p-8 hover:border-blue-400/30 transition-all duration-500",children:[e.jsxs("div",{className:"flex items-center space-x-4 mb-8",children:[e.jsx("div",{className:"w-14 h-14 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-xl shadow-blue-500/25",children:e.jsx("svg",{className:"w-7 h-7 text-white",fill:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{d:"M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z"})})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-3xl font-bold bg-gradient-to-r from-blue-300 to-indigo-300 bg-clip-text text-transparent",children:"Telegram Integration"}),e.jsx("p",{className:"text-blue-200/80 text-lg",children:"Instant notifications via Telegram"})]})]}),e.jsxs("div",{className:"space-y-8",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsxs("label",{className:"text-lg font-bold text-white flex items-center space-x-2",children:[e.jsx("span",{children:"🤖"}),e.jsx("span",{children:"Bot Token"})]}),e.jsx("input",{type:"password",value:r.telegramBotToken||"",onChange:t=>l("telegramBotToken",t.target.value),className:"w-full px-6 py-4 bg-slate-800/60 border border-slate-600/50 rounded-2xl text-white text-lg placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 backdrop-blur-sm transition-all duration-300 hover:border-blue-500/30",placeholder:"Enter your Telegram Bot Token"})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("label",{className:"text-lg font-bold text-white flex items-center space-x-2",children:[e.jsx("span",{children:"💬"}),e.jsx("span",{children:"Chat ID"})]}),e.jsx("input",{type:"text",value:r.telegramChatId||"",onChange:t=>l("telegramChatId",t.target.value),className:"w-full px-6 py-4 bg-slate-800/60 border border-slate-600/50 rounded-2xl text-white text-lg placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 backdrop-blur-sm transition-all duration-300 hover:border-blue-500/30",placeholder:"Enter your Telegram Chat ID"})]}),e.jsx("div",{className:"p-6 bg-gradient-to-r from-cyan-500/10 to-blue-500/10 border border-cyan-500/30 rounded-2xl backdrop-blur-sm",children:e.jsxs("div",{className:"flex items-start space-x-4",children:[e.jsx("div",{className:"w-6 h-6 text-cyan-400 mt-1 flex-shrink-0",children:e.jsx("svg",{fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})})}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-bold text-cyan-300",children:"Quick Setup Guide"}),e.jsxs("div",{className:"mt-3 space-y-2 text-cyan-200/90",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"w-6 h-6 bg-cyan-500/20 rounded-full flex items-center justify-center text-xs font-bold",children:"1"}),e.jsx("span",{children:"Message @BotFather on Telegram to create a new bot"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"w-6 h-6 bg-cyan-500/20 rounded-full flex items-center justify-center text-xs font-bold",children:"2"}),e.jsx("span",{children:"Copy the bot token and paste it in the field above"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"w-6 h-6 bg-cyan-500/20 rounded-full flex items-center justify-center text-xs font-bold",children:"3"}),e.jsx("span",{children:"Start a chat with your bot and send any message"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"w-6 h-6 bg-cyan-500/20 rounded-full flex items-center justify-center text-xs font-bold",children:"4"}),e.jsx("span",{children:"Get your chat ID from @userinfobot or browser console"})]})]})]})]})})]})]})]}),e.jsxs("section",{className:"relative group",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-purple-500/10 via-fuchsia-500/10 to-pink-500/10 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-500"}),e.jsxs("div",{className:"relative backdrop-blur-xl bg-black/40 border border-purple-500/20 rounded-3xl p-8 hover:border-purple-400/30 transition-all duration-500",children:[e.jsxs("div",{className:"flex items-center space-x-4 mb-8",children:[e.jsx("div",{className:"w-14 h-14 bg-gradient-to-br from-purple-500 to-fuchsia-600 rounded-2xl flex items-center justify-center shadow-xl shadow-purple-500/25",children:e.jsx("svg",{className:"w-7 h-7 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"})})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-3xl font-bold bg-gradient-to-r from-purple-300 to-pink-300 bg-clip-text text-transparent",children:"Keyboard Shortcuts"}),e.jsx("p",{className:"text-purple-200/80 text-lg",children:"Power user hotkeys for quick actions"})]})]}),e.jsx("div",{className:"space-y-6",children:Object.entries(r.hotkeys).map(([t,b])=>e.jsx("div",{className:"p-6 bg-gradient-to-r from-slate-800/50 to-slate-700/50 rounded-2xl border border-slate-600/30 hover:border-purple-500/30 transition-all duration-300",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-lg font-bold text-white",children:b.description}),e.jsx("p",{className:"text-slate-300",children:"Quick access hotkey"})]}),e.jsx("input",{type:"text",value:b.combination,onChange:g=>l("hotkeys",{...r.hotkeys,[t]:{...b,combination:g.target.value}}),className:"px-6 py-4 bg-slate-800/60 border border-slate-600/50 rounded-2xl text-white text-lg placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/50 backdrop-blur-sm transition-all duration-300 hover:border-purple-500/30",placeholder:"Ctrl+Shift+Key"})]})},t))}),e.jsx("div",{className:"p-6 bg-gradient-to-r from-fuchsia-500/10 to-purple-500/10 border border-fuchsia-500/30 rounded-2xl backdrop-blur-sm mt-8",children:e.jsxs("div",{className:"flex items-start space-x-4",children:[e.jsx("div",{className:"w-6 h-6 text-fuchsia-400 mt-1 flex-shrink-0",children:e.jsx("svg",{fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})})}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-bold text-fuchsia-300",children:"Chrome Extension Settings"}),e.jsx("p",{className:"text-fuchsia-200/90 mt-2 leading-relaxed",children:"Keyboard shortcuts can be customized in Chrome Extensions settings. Navigate to chrome://extensions/shortcuts to configure global hotkeys."})]})]})})]})]})]})]})]})};function j(){const r=document.documentElement,s=document.body,a=`
    width: 100vw !important;
    height: 100vh !important;
    min-width: 100vw !important;
    min-height: 100vh !important;
    max-width: 100vw !important;
    max-height: none !important;
    margin: 0 !important;
    padding: 0 !important;
    overflow-x: hidden !important;
    overflow-y: auto !important;
  `;r.style.cssText=a,s.style.cssText=a,r.className="options-page",s.className="options",console.log("Options page dimensions enforced:",{htmlSize:`${r.offsetWidth}x${r.offsetHeight}`,bodySize:`${s.offsetWidth}x${s.offsetHeight}`,windowSize:`${window.innerWidth}x${window.innerHeight}`})}j();document.addEventListener("DOMContentLoaded",j);window.addEventListener("load",j);window.addEventListener("resize",j);const k=document.getElementById("root");if(!k)throw new Error("Root element not found");k.style.cssText=`
  width: 100% !important;
  height: 100% !important;
  min-height: 100vh !important;
  display: flex !important;
  flex-direction: column !important;
`;const V=T(k);V.render(e.jsx(U,{}));
