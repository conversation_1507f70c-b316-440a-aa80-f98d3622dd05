import React from 'react';
import { PopupHeaderProps } from '../types';

const PopupHeader: React.FC<PopupHeaderProps> = ({ isConnected }) => {
  return (
    <div className="popup-header">
      <div className="popup-icon">⚡</div>
      <div className="popup-title">
        <h1>Airtm Monitor Pro</h1>
        <p>Real-time offer monitoring</p>
      </div>
      <div className="popup-status">
        <div 
          className="status-dot"
          style={{
            background: isConnected ? 'var(--success-500)' : 'var(--error-500)'
          }}
        ></div>
        <span>{isConnected ? 'Connected' : 'Disconnected'}</span>
      </div>
    </div>
  );
};

export default PopupHeader;
