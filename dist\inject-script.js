(function() {
  console.log('🌍 Page script injected - monitoring main world context');
  
  // Override fetch in main world
  const originalFetch = window.fetch;
  window.fetch = async function(...args) {
    const response = await originalFetch.apply(this, args);
    const url = typeof args[0] === 'string' ? args[0] : args[0].url;
    
    if (url && url.includes('graphql')) {
      console.log('🌍 Main world fetch detected GraphQL:', url);
      
      // Clone and read response
      const clonedResponse = response.clone();
      clonedResponse.text().then(text => {
        if (text.includes('availableOperations')) {
          console.log('🎯 Main world detected availableOperations');
          
          // Send to content script via custom event
          window.dispatchEvent(new CustomEvent('airtm-graphql-detected', {
            detail: { responseText: text, url: url }
          }));
        }
      }).catch(() => {});
      
      // Also check for data in global state after response
      setTimeout(() => {
        const globalChecks = [
          'window.__APOLLO_CLIENT__',
          'window.__NEXT_DATA__',
          'window.__INITIAL_STATE__',
          'window.store',
          'window.app',
          'window.React',
          'window.ReactDOM'
        ];
        
        globalChecks.forEach(checkPath => {
          try {
            const value = eval(checkPath);
            if (value) {
              const valueStr = JSON.stringify(value);
              if (valueStr.includes('availableOperations')) {
                console.log('🎯 Found GraphQL data in ' + checkPath);
                window.dispatchEvent(new CustomEvent('airtm-graphql-detected', {
                  detail: { responseText: valueStr, url: 'global-state-' + checkPath }
                }));
              }
            }
          } catch (e) {
            // Ignore evaluation errors
          }
        });
      }, 300);
    }
    
    return response;
  };
  
  // Override XMLHttpRequest in main world
  const OriginalXHR = XMLHttpRequest;
  XMLHttpRequest = function() {
    const xhr = new OriginalXHR();
    const originalSend = xhr.send;
    const originalOpen = xhr.open;
    
    let requestUrl = '';
    
    xhr.open = function(method, url, ...args) {
      requestUrl = url;
      return originalOpen.apply(this, [method, url, ...args]);
    };
    
    xhr.send = function(data) {
      if (requestUrl.includes('graphql')) {
        console.log('🌍 Main world XHR detected GraphQL:', requestUrl);
        
        const originalOnReadyStateChange = xhr.onreadystatechange;
        xhr.onreadystatechange = function() {
          if (xhr.readyState === 4 && xhr.status === 200) {
            if (xhr.responseText && xhr.responseText.includes('availableOperations')) {
              console.log('🎯 Main world XHR detected availableOperations');
              
              // Send to content script via custom event
              window.dispatchEvent(new CustomEvent('airtm-graphql-detected', {
                detail: { responseText: xhr.responseText, url: requestUrl }
              }));
            }
          }
          
          if (originalOnReadyStateChange) {
            return originalOnReadyStateChange.apply(this, arguments);
          }
        };
      }
      
      return originalSend.apply(this, arguments);
    };
    
    return xhr;
  };
  
  // Monitor for React/Vue state changes
  let lastStateCheck = '';
  setInterval(() => {
    try {
      // Check for React DevTools
      if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
        const reactFiber = window.__REACT_DEVTOOLS_GLOBAL_HOOK__.renderers;
        if (reactFiber) {
          Object.values(reactFiber).forEach(renderer => {
            if (renderer && renderer.findFiberByHostInstance) {
              // This is a simplified check - would need more complex traversal
              console.log('🔍 React renderer found');
            }
          });
        }
      }
      
      // Check for Vue DevTools
      if (window.__VUE__) {
        console.log('🔍 Vue instance found');
      }
      
      // Check for Apollo Client in main world
      if (window.__APOLLO_CLIENT__) {
        const apolloClient = window.__APOLLO_CLIENT__;
        if (apolloClient.cache && apolloClient.cache.data) {
          const currentState = JSON.stringify(apolloClient.cache.data);
          if (currentState !== lastStateCheck && currentState.includes('availableOperations')) {
            console.log('🎯 Apollo cache state change detected with availableOperations');
            lastStateCheck = currentState;
            
            window.dispatchEvent(new CustomEvent('airtm-graphql-detected', {
              detail: { responseText: currentState, url: 'apollo-cache' }
            }));
          }
        }
      }
      
      // Check for any global variables that might contain data
      Object.keys(window).forEach(key => {
        if (key.toLowerCase().includes('airtm') || key.toLowerCase().includes('operation')) {
          const value = window[key];
          if (value && typeof value === 'object') {
            const stringified = JSON.stringify(value);
            if (stringified.includes('availableOperations') && stringified !== lastStateCheck) {
              console.log('🎯 Global variable with availableOperations:', key);
              lastStateCheck = stringified;
              
              window.dispatchEvent(new CustomEvent('airtm-graphql-detected', {
                detail: { responseText: stringified, url: 'global-' + key }
              }));
            }
          }
        }
      });
      
    } catch (e) {
      // Ignore errors
    }
  }, 2000);
  
  console.log('🌍 Main world monitoring active');
})();