import React from 'react';
import { ActionButtonProps } from '../types';

const ActionButton: React.FC<ActionButtonProps> = ({
  icon,
  label,
  onClick,
  variant = 'secondary',
  disabled = false
}) => {
  const className = `action-button ${variant === 'primary' ? 'primary' : ''}`;

  return (
    <button
      className={className}
      onClick={onClick}
      disabled={disabled}
    >
      <span className="action-icon">{icon}</span>
      <span>{label}</span>
    </button>
  );
};

export default ActionButton;
