/**
 * Airtm Monitor Pro - Options Page Application
 * Vanilla JavaScript implementation for CSP compliance
 */

class AirtmOptionsApp {
  constructor() {
    this.settings = {
      monitoring: false,
      refreshInterval: 10,
      monitorBuyPage: true,
      monitorSellPage: true,
      autoAccept: false,
      telegramBotToken: '',
      telegramChatId: '',
      minAmount: 0,
      maxAmount: 10000,
      preferredCurrencies: ['USD'],
      blacklistedUsers: []
    };

    this.isConnected = false;
    this.saveTimeout = null;

    this.init();
  }
  
  async init() {
    try {
      // Show loading screen
      this.showLoading();
      
      // Initialize the application
      await this.loadSettings();
      await this.checkConnection();
      this.setupEventListeners();
      this.updateUI();
      
      // Hide loading screen and show app
      setTimeout(() => {
        this.hideLoading();
      }, 1000); // Minimum loading time for smooth UX
      
    } catch (error) {
      console.error('Failed to initialize options app:', error);
      this.showError('Failed to initialize application', error.message);
    }
  }
  
  showLoading() {
    const loadingScreen = document.getElementById('loading-screen');
    const app = document.getElementById('app');
    
    loadingScreen.classList.remove('hidden');
    app.classList.add('hidden');
  }
  
  hideLoading() {
    const loadingScreen = document.getElementById('loading-screen');
    const app = document.getElementById('app');
    
    loadingScreen.classList.add('hidden');
    app.classList.remove('hidden');
  }
  
  async loadSettings() {
    try {
      if (chrome?.storage?.sync) {
        const result = await chrome.storage.sync.get('settings');
        if (result.settings) {
          this.settings = { ...this.settings, ...result.settings };
        }
      }
    } catch (error) {
      console.error('Failed to load settings:', error);
      throw new Error('Unable to load settings from storage');
    }
  }
  
  async saveSettings() {
    try {
      if (chrome?.storage?.sync) {
        await chrome.storage.sync.set({ settings: this.settings });
        this.showSaveStatus('Settings saved successfully!', 'success');
      } else {
        throw new Error('Chrome storage not available');
      }
    } catch (error) {
      console.error('Failed to save settings:', error);
      this.showSaveStatus('Failed to save settings', 'error');
    }
  }
  
  async checkConnection() {
    try {
      // Simulate connection check
      this.isConnected = chrome?.runtime?.id ? true : false;
      this.updateConnectionStatus();
    } catch (error) {
      this.isConnected = false;
      this.updateConnectionStatus();
    }
  }
  
  updateConnectionStatus() {
    const statusElement = document.getElementById('connection-status');
    const statusText = statusElement.querySelector('span');
    
    if (this.isConnected) {
      statusElement.classList.add('connected');
      statusElement.classList.remove('disconnected');
      statusText.textContent = 'Connected';
    } else {
      statusElement.classList.add('disconnected');
      statusElement.classList.remove('connected');
      statusText.textContent = 'Disconnected';
    }
  }
  
  setupEventListeners() {
    // Navigation
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const section = link.dataset.section;
        this.showSection(section);
        this.setActiveNavLink(link);
      });
    });
    
    // Monitoring toggle
    const monitoringToggle = document.getElementById('monitoring-enabled');
    monitoringToggle.addEventListener('change', (e) => {
      this.settings.monitoring = e.target.checked;
      this.updateMonitoringStatus();
      this.debouncedSave();
    });
    
    // Refresh interval
    const refreshInterval = document.getElementById('refresh-interval');
    const refreshIntervalValue = document.getElementById('refresh-interval-value');
    refreshInterval.addEventListener('input', (e) => {
      const value = parseInt(e.target.value);
      this.settings.refreshInterval = value;
      refreshIntervalValue.textContent = `${value}s`;
      this.debouncedSave();
    });
    
    // Page monitoring checkboxes
    const monitorBuyPage = document.getElementById('monitor-buy-page');
    const monitorSellPage = document.getElementById('monitor-sell-page');
    
    monitorBuyPage.addEventListener('change', (e) => {
      this.settings.monitorBuyPage = e.target.checked;
      this.debouncedSave();
    });
    
    monitorSellPage.addEventListener('change', (e) => {
      this.settings.monitorSellPage = e.target.checked;
      this.debouncedSave();
    });
    
    // Auto accept toggle
    const autoAcceptToggle = document.getElementById('auto-accept-enabled');
    autoAcceptToggle.addEventListener('change', (e) => {
      this.settings.autoAccept = e.target.checked;
      this.updateAutoAcceptStatus();
      this.debouncedSave();
    });

    // Amount filters
    const minAmountInput = document.getElementById('min-amount');
    const maxAmountInput = document.getElementById('max-amount');

    minAmountInput.addEventListener('input', (e) => {
      this.settings.minAmount = parseFloat(e.target.value) || 0;
      this.debouncedSave();
    });

    maxAmountInput.addEventListener('input', (e) => {
      this.settings.maxAmount = parseFloat(e.target.value) || 10000;
      this.debouncedSave();
    });

    // Telegram settings
    const telegramBotToken = document.getElementById('telegram-bot-token');
    const telegramChatId = document.getElementById('telegram-chat-id');
    const testTelegramButton = document.getElementById('test-telegram');

    telegramBotToken.addEventListener('input', (e) => {
      this.settings.telegramBotToken = e.target.value;
      this.debouncedSave();
    });

    telegramChatId.addEventListener('input', (e) => {
      this.settings.telegramChatId = e.target.value;
      this.debouncedSave();
    });

    testTelegramButton.addEventListener('click', () => {
      this.testTelegramConnection();
    });

    // Save button
    const saveButton = document.getElementById('save-settings');
    saveButton.addEventListener('click', () => {
      this.saveSettings();
    });
  }
  
  showSection(sectionName) {
    // Hide all sections
    const sections = document.querySelectorAll('.content-section');
    sections.forEach(section => {
      section.classList.remove('active');
    });
    
    // Show target section
    const targetSection = document.getElementById(`${sectionName}-section`);
    if (targetSection) {
      targetSection.classList.add('active');
    }
  }
  
  setActiveNavLink(activeLink) {
    // Remove active class from all nav links
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
      link.classList.remove('active');
    });
    
    // Add active class to clicked link
    activeLink.classList.add('active');
  }
  
  updateUI() {
    // Update monitoring toggle
    const monitoringToggle = document.getElementById('monitoring-enabled');
    monitoringToggle.checked = this.settings.monitoring;
    
    // Update refresh interval
    const refreshInterval = document.getElementById('refresh-interval');
    const refreshIntervalValue = document.getElementById('refresh-interval-value');
    refreshInterval.value = this.settings.refreshInterval;
    refreshIntervalValue.textContent = `${this.settings.refreshInterval}s`;
    
    // Update page monitoring checkboxes
    const monitorBuyPage = document.getElementById('monitor-buy-page');
    const monitorSellPage = document.getElementById('monitor-sell-page');
    monitorBuyPage.checked = this.settings.monitorBuyPage;
    monitorSellPage.checked = this.settings.monitorSellPage;
    
    // Update auto accept toggle
    const autoAcceptToggle = document.getElementById('auto-accept-enabled');
    autoAcceptToggle.checked = this.settings.autoAccept;

    // Update amount filters
    const minAmountInput = document.getElementById('min-amount');
    const maxAmountInput = document.getElementById('max-amount');
    minAmountInput.value = this.settings.minAmount;
    maxAmountInput.value = this.settings.maxAmount;

    // Update Telegram settings
    const telegramBotToken = document.getElementById('telegram-bot-token');
    const telegramChatId = document.getElementById('telegram-chat-id');
    telegramBotToken.value = this.settings.telegramBotToken;
    telegramChatId.value = this.settings.telegramChatId;

    // Update last updated date
    const lastUpdatedElement = document.getElementById('last-updated');
    if (lastUpdatedElement) {
      lastUpdatedElement.textContent = new Date().toLocaleDateString();
    }

    // Update monitoring status
    this.updateMonitoringStatus();
    this.updateAutoAcceptStatus();
  }
  
  updateMonitoringStatus() {
    const statusText = document.getElementById('monitoring-status-text');
    statusText.textContent = this.settings.monitoring ? 'Active' : 'Disabled';
    statusText.style.color = this.settings.monitoring ? 'var(--success-600)' : 'var(--gray-600)';
  }

  updateAutoAcceptStatus() {
    const statusText = document.getElementById('auto-accept-status-text');
    statusText.textContent = this.settings.autoAccept ? 'Enabled' : 'Disabled';
    statusText.style.color = this.settings.autoAccept ? 'var(--success-600)' : 'var(--gray-600)';
  }

  async testTelegramConnection() {
    const testButton = document.getElementById('test-telegram');
    const originalText = testButton.textContent;

    try {
      testButton.textContent = 'Testing...';
      testButton.disabled = true;

      if (!this.settings.telegramBotToken || !this.settings.telegramChatId) {
        throw new Error('Please enter both bot token and chat ID');
      }

      // Simulate API test (in real implementation, this would call Telegram API)
      await new Promise(resolve => setTimeout(resolve, 2000));

      this.showSaveStatus('Telegram connection successful!', 'success');

    } catch (error) {
      this.showSaveStatus(`Telegram test failed: ${error.message}`, 'error');
    } finally {
      testButton.textContent = originalText;
      testButton.disabled = false;
    }
  }
  
  debouncedSave() {
    // Clear existing timeout
    if (this.saveTimeout) {
      clearTimeout(this.saveTimeout);
    }
    
    // Set new timeout
    this.saveTimeout = setTimeout(() => {
      this.saveSettings();
    }, 1000); // Save after 1 second of inactivity
  }
  
  showSaveStatus(message, type) {
    const saveStatus = document.getElementById('save-status');
    saveStatus.textContent = message;
    saveStatus.className = `save-status ${type}`;
    saveStatus.classList.remove('hidden');
    
    // Hide after 3 seconds
    setTimeout(() => {
      saveStatus.classList.add('hidden');
    }, 3000);
  }
  
  showError(title, message) {
    // Create simple error message
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message';
    errorDiv.innerHTML = `
      <h3>${title}</h3>
      <p>${message}</p>
      <button class="error-button" id="reload-button">Reload Page</button>
    `;

    document.body.appendChild(errorDiv);

    // Add event listener for reload button
    const reloadBtn = errorDiv.querySelector('#reload-button');
    reloadBtn.addEventListener('click', () => {
      window.location.reload();
    });
  }
}

// Error overlay styles (injected dynamically to avoid CSP issues)
function injectErrorStyles() {
  const style = document.createElement('style');
  style.textContent = `
    .error-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10000;
    }
    
    .error-modal {
      background: white;
      border-radius: var(--radius-xl);
      box-shadow: var(--shadow-xl);
      max-width: 500px;
      width: 90%;
      max-height: 80vh;
      overflow: hidden;
    }
    
    .error-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: var(--space-6);
      border-bottom: 1px solid var(--gray-200);
      background: var(--error-50);
    }
    
    .error-header h3 {
      color: var(--error-700);
      font-size: var(--font-size-xl);
      font-weight: 600;
    }
    
    .error-close {
      background: none;
      border: none;
      font-size: var(--font-size-2xl);
      color: var(--error-500);
      cursor: pointer;
      padding: var(--space-1);
    }
    
    .error-content {
      padding: var(--space-6);
    }
    
    .error-content p {
      color: var(--gray-700);
      margin-bottom: var(--space-6);
      line-height: 1.6;
    }
    
    .error-actions {
      display: flex;
      gap: var(--space-3);
      justify-content: flex-end;
    }
    
    .error-retry,
    .error-dismiss {
      padding: var(--space-3) var(--space-4);
      border-radius: var(--radius-md);
      font-weight: 500;
      cursor: pointer;
      transition: all var(--transition-fast);
    }
    
    .error-retry {
      background: var(--primary-500);
      color: white;
      border: none;
    }
    
    .error-retry:hover {
      background: var(--primary-600);
    }
    
    .error-dismiss {
      background: var(--gray-100);
      color: var(--gray-700);
      border: 1px solid var(--gray-300);
    }
    
    .error-dismiss:hover {
      background: var(--gray-200);
    }
  `;
  document.head.appendChild(style);
}

// Initialize the application when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  injectErrorStyles();
  new AirtmOptionsApp();
});

// Handle page visibility changes
document.addEventListener('visibilitychange', () => {
  if (!document.hidden) {
    // Page became visible, check connection
    if (window.airtmApp) {
      window.airtmApp.checkConnection();
    }
  }
});

// Export for debugging
window.AirtmOptionsApp = AirtmOptionsApp;
