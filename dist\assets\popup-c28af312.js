import{r as y,j as e,c as A}from"./globals-810552d8.js";import{i as E,s as b}from"./extension-context-5094bf00.js";const d=({d:a,className:r="w-4 h-4"})=>e.jsx("svg",{className:r,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:a})}),l={refresh:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15",settings:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z M15 12a3 3 0 11-6 0 3 3 0 016 0z",close:"M6 18L18 6M6 6l12 12",alert:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z",play:"M14.828 14.828a4 4 0 01-5.656 0M9 10h1.586a1 1 0 01.707.293l2.414 2.414a1 1 0 00.707.293H15",pause:"M10 9v6m4-6v6",check:"M5 13l4 4L19 7",search:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z",withdrawal:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z",crypto:"M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"};function T(a){var v,w,s,t,i,n,c;const r=a.walletCurrency,o=a.currency,h=a.makerPaymentMethod||a.takerPaymentMethod,m=((v=h==null?void 0:h.categoryId)==null?void 0:v.toLowerCase().includes("usdc"))||((t=(s=(w=h==null?void 0:h.version)==null?void 0:w.category)==null?void 0:s.translationTag)==null?void 0:t.toLowerCase().includes("usdc")),f=((i=a.metadata)==null?void 0:i.walletCurrencyPrecision)||(r==null?void 0:r.precision),u=((n=a.metadata)==null?void 0:n.localCurrencyPrecision)||(o==null?void 0:o.precision),S=f===6||u===6,C=((c=a.metadata)==null?void 0:c.isForThirdPartyPaymentMethod)===!1,j=m||S||C;let g=!1,p="",x=(o==null?void 0:o.symbol)||"$",N=a.grossAmount||"0";return j&&(a.operationType==="SELL"&&f===6?(g=!0,p="withdrawal",x="USDC",N=a.grossAmount||"0"):a.operationType==="BUY"&&u===6?(p="deposit",x="USDC"):(p="exchange",f===6&&(x="USDC"))),{amount:N,currency:x,isUSDC:j,isWithdrawal:g,operationType:p}}function M(){var f,u,S,C,j,g,p,x,N,v,w;const[a,r]=y.useState({offers:[],settings:null,stats:null,selectedOffer:null,isLoading:!0,error:null,isConnected:!1}),o=y.useCallback(async()=>{r(s=>({...s,isLoading:!0,error:null}));try{if(!E())throw new Error("Extension context not available");const s=await b({type:"GET_POPUP_DATA"});if(s!=null&&s.success&&s.data)r(t=>({...t,offers:s.data.offers||[],settings:s.data.settings||null,stats:s.data.stats||{totalOffers:0,newOffers:0,averageRate:0},isConnected:!0,isLoading:!1,error:null}));else throw new Error((s==null?void 0:s.error)||"Invalid response from background script")}catch(s){console.error("Popup data loading error:",s),r(t=>({...t,isLoading:!1,isConnected:!1,error:s instanceof Error?s.message:"Failed to load extension data"}))}},[]),h=y.useCallback(()=>{var t,i;const s=n=>{n.type==="OFFERS_UPDATED"?r(c=>({...c,offers:n.offers||[]})):n.type==="SETTINGS_UPDATED"?r(c=>({...c,settings:n.settings})):n.type==="STATS_UPDATED"&&r(c=>({...c,stats:n.stats}))};return E()?((i=(t=chrome.runtime)==null?void 0:t.onMessage)==null||i.addListener(s),()=>{var n,c;return(c=(n=chrome.runtime)==null?void 0:n.onMessage)==null?void 0:c.removeListener(s)}):()=>{}},[]);y.useEffect(()=>(o(),h()),[]);const m=y.useMemo(()=>({refresh:()=>o(),openSettings:()=>{try{if(!E())throw new Error("Extension context not available");chrome.runtime.openOptionsPage()}catch(s){console.error("Failed to open settings:",s),r(t=>({...t,error:"Failed to open settings page"}))}},selectOffer:s=>{r(t=>({...t,selectedOffer:s}))},updateSettings:async s=>{try{const t=await b({type:"SETTINGS_UPDATE",data:s});if(t!=null&&t.success)r(i=>({...i,settings:s}));else throw new Error((t==null?void 0:t.error)||"Failed to update settings")}catch(t){console.error("Settings update error:",t),r(i=>({...i,error:t instanceof Error?t.message:"Failed to update settings"}))}},acceptOffer:async s=>{try{const t=await b({type:"ACCEPT_OFFER",offerId:s.id||s.hash});if(!(t!=null&&t.success))throw new Error((t==null?void 0:t.error)||"Failed to accept offer")}catch(t){console.error("Accept offer error:",t),r(i=>({...i,error:t instanceof Error?t.message:"Failed to accept offer"}))}},rejectOffer:async s=>{try{const t=await b({type:"REJECT_OFFER",offerId:s.id||s.hash});if(!(t!=null&&t.success))throw new Error((t==null?void 0:t.error)||"Failed to reject offer")}catch(t){console.error("Reject offer error:",t),r(i=>({...i,error:t instanceof Error?t.message:"Failed to reject offer"}))}},dismissError:()=>{r(s=>({...s,error:null}))}}),[o]);return a.isLoading?e.jsx("div",{className:"modern-popup",children:e.jsxs("div",{className:"loading-state",children:[e.jsxs("div",{className:"loading-logo",children:[e.jsx("div",{className:"logo-circle",children:"A"}),e.jsx("div",{className:"loading-pulse"})]}),e.jsxs("div",{className:"loading-content",children:[e.jsx("h2",{className:"loading-title",children:"Airtm Monitor Pro"}),e.jsx("p",{className:"loading-text",children:"Connecting to extension..."}),e.jsx("div",{className:"loading-progress",children:e.jsx("div",{className:"progress-bar"})})]})]})}):e.jsxs("div",{className:"modern-popup",children:[e.jsxs("header",{className:"popup-header",children:[e.jsxs("div",{className:"header-brand",children:[e.jsxs("div",{className:"brand-logo",children:[e.jsx("div",{className:"logo-circle",children:"A"}),e.jsx("div",{className:`status-dot ${(f=a.settings)!=null&&f.monitoring?"active":"inactive"}`})]}),e.jsxs("div",{className:"brand-text",children:[e.jsx("h1",{className:"brand-title",children:"Airtm Monitor Pro"}),e.jsxs("p",{className:"brand-subtitle",children:[a.isConnected?"Connected":"Disconnected"," •",a.offers.length," offers"]})]})]}),e.jsxs("div",{className:"header-actions",children:[e.jsx("button",{onClick:m.refresh,className:"action-button secondary",title:"Refresh data",children:e.jsx(d,{d:l.refresh})}),e.jsx("button",{onClick:m.openSettings,className:"action-button primary",title:"Open settings",children:e.jsx(d,{d:l.settings})})]})]}),a.error&&e.jsxs("div",{className:"error-banner",children:[e.jsxs("div",{className:"error-content",children:[e.jsx(d,{d:l.alert,className:"error-icon"}),e.jsx("span",{className:"error-message",children:a.error})]}),e.jsx("button",{onClick:m.dismissError,className:"error-dismiss",title:"Dismiss error",children:e.jsx(d,{d:l.close})})]}),e.jsx("section",{className:"stats-section",children:e.jsxs("div",{className:"stats-grid",children:[e.jsxs("div",{className:"stat-card",children:[e.jsx("div",{className:"stat-value",children:((u=a.stats)==null?void 0:u.totalOffers)||0}),e.jsx("div",{className:"stat-label",children:"Total Offers"})]}),e.jsxs("div",{className:"stat-card",children:[e.jsx("div",{className:"stat-value",children:((S=a.stats)==null?void 0:S.newOffers)||0}),e.jsx("div",{className:"stat-label",children:"New Today"})]}),e.jsxs("div",{className:"stat-card",children:[e.jsx("div",{className:"stat-value",children:((C=a.stats)==null?void 0:C.acceptedOffers)||0}),e.jsx("div",{className:"stat-label",children:"Accepted"})]}),e.jsxs("div",{className:"stat-card",children:[e.jsx("div",{className:"stat-value",children:((j=a.stats)==null?void 0:j.rejectedOffers)||0}),e.jsx("div",{className:"stat-label",children:"Rejected"})]})]})}),e.jsxs("main",{className:"main-content",children:[e.jsxs("div",{className:"content-header",children:[e.jsx("h3",{className:"content-title",children:"Available Offers"}),e.jsx("div",{className:"content-meta",children:e.jsxs("div",{className:"live-status",children:[e.jsx("div",{className:"pulse-indicator"}),e.jsx("span",{children:"Live"})]})})]}),e.jsx("div",{className:"offers-container",children:a.offers.length===0?e.jsxs("div",{className:"empty-state",children:[e.jsx(d,{d:l.search,className:"empty-icon"}),e.jsx("h4",{className:"empty-title",children:"No offers available"}),e.jsx("p",{className:"empty-text",children:(g=a.settings)!=null&&g.monitoring?"Monitoring is active. New offers will appear here.":"Start monitoring to see offers."})]}):e.jsx("div",{className:"offers-list",children:a.offers.map(s=>{var i,n,c;const t=T(s);return e.jsxs("div",{className:`offer-item ${(((i=a.selectedOffer)==null?void 0:i.id)||((n=a.selectedOffer)==null?void 0:n.hash))===(s.id||s.hash)?"selected":""} ${t.isWithdrawal?"withdrawal":""} ${t.isUSDC?"usdc-operation":""}`,onClick:()=>m.selectOffer(s),children:[e.jsxs("div",{className:"offer-header",children:[e.jsxs("div",{className:"offer-currency",children:[t.currency,t.isUSDC&&e.jsx("span",{className:"crypto-badge",children:e.jsx(d,{d:l.crypto,className:"w-3 h-3"})})]}),e.jsxs("div",{className:"offer-type",children:[s.operationType,t.isWithdrawal&&e.jsx("span",{className:"withdrawal-badge",children:e.jsx(d,{d:l.withdrawal,className:"w-3 h-3"})})]})]}),e.jsxs("div",{className:"offer-details",children:[e.jsxs("div",{className:"offer-amount",children:[t.isWithdrawal?"-":"",t.currency==="USDC"?"":"$",parseFloat(t.amount).toFixed(t.isUSDC?6:2),t.currency==="USDC"?" USDC":""]}),e.jsxs("div",{className:"offer-rate",children:["Rate: ",s.rate||((c=s.displayRate)==null?void 0:c.rate)||"N/A"]}),t.isWithdrawal&&e.jsx("div",{className:"withdrawal-note",children:e.jsx("span",{className:"text-orange-600 text-xs",children:"Wallet deduction"})})]}),e.jsxs("div",{className:"offer-peer",children:[s.peer?`${s.peer.firstName} ${s.peer.lastName}`:"Unknown",t.operationType&&e.jsx("span",{className:"operation-type-badge",children:t.operationType})]})]},s.id||s.hash)})})})]}),e.jsxs("footer",{className:"control-panel",children:[a.selectedOffer&&e.jsxs("div",{className:"selected-offer",children:[e.jsxs("div",{className:"selected-info",children:[e.jsx("span",{className:"selected-label",children:"Selected:"}),e.jsx("span",{className:"selected-details",children:(()=>{const s=T(a.selectedOffer);return e.jsxs(e.Fragment,{children:[s.currency," •",s.isWithdrawal?"-":"",s.currency==="USDC"?"":"$",parseFloat(s.amount).toFixed(s.isUSDC?6:2),s.currency==="USDC"?" USDC":"",s.isWithdrawal&&e.jsx("span",{className:"text-orange-600 ml-1",children:"(withdrawal)"}),s.isUSDC&&!s.isWithdrawal&&e.jsx("span",{className:"text-blue-600 ml-1",children:"(USDC)"})]})})()})]}),e.jsxs("div",{className:"selected-actions",children:[e.jsxs("button",{onClick:()=>m.acceptOffer(a.selectedOffer),className:"action-button success",title:"Accept offer",children:[e.jsx(d,{d:l.check}),"Accept"]}),e.jsxs("button",{onClick:()=>m.rejectOffer(a.selectedOffer),className:"action-button danger",title:"Reject offer",children:[e.jsx(d,{d:l.close}),"Reject"]})]})]}),e.jsxs("div",{className:"main-controls",children:[e.jsxs("button",{onClick:()=>{var s;return m.updateSettings({...a.settings,monitoring:!((s=a.settings)!=null&&s.monitoring)})},className:`control-button ${(p=a.settings)!=null&&p.monitoring?"active":"inactive"}`,title:(x=a.settings)!=null&&x.monitoring?"Stop monitoring":"Start monitoring",children:[e.jsx(d,{d:(N=a.settings)!=null&&N.monitoring?l.pause:l.play}),e.jsx("span",{children:(v=a.settings)!=null&&v.monitoring?"Stop":"Start"})]}),e.jsxs("button",{onClick:()=>{var s;return m.updateSettings({...a.settings,autoAccept:!((s=a.settings)!=null&&s.autoAccept)})},className:`control-button ${(w=a.settings)!=null&&w.autoAccept?"active":"inactive"}`,title:"Toggle auto-accept",children:[e.jsx(d,{d:l.check}),e.jsx("span",{children:"Auto Accept"})]})]})]})]})}function D(){const a=document.documentElement,r=document.body,o=`
    width: 600px !important;
    height: 850px !important;
    min-width: 600px !important;
    min-height: 850px !important;
    max-width: 600px !important;
    max-height: 850px !important;
    margin: 0 !important;
    padding: 0 !important;
    overflow: hidden !important;
  `;a.style.cssText=o,r.style.cssText=o,r.className="popup",console.log("Popup dimensions enforced:",{htmlSize:`${a.offsetWidth}x${a.offsetHeight}`,bodySize:`${r.offsetWidth}x${r.offsetHeight}`,windowSize:`${window.innerWidth}x${window.innerHeight}`})}D();document.addEventListener("DOMContentLoaded",D);window.addEventListener("load",D);const O=document.getElementById("root");if(!O)throw new Error("Root element not found");O.style.cssText=`
  width: 600px !important;
  height: 850px !important;
  min-width: 600px !important;
  min-height: 850px !important;
  max-width: 600px !important;
  max-height: 850px !important;
  display: flex !important;
  flex-direction: column !important;
`;const U=A(O);U.render(e.jsx(M,{}));
