/**
 * Airtm GraphQL Schema Types
 * Based on actual AvailableOperations GraphQL response structure
 */

/** Fragment CurrencyData - matches actual API response */
export interface CurrencyData {
  id: string                                  // "USD", "EGP", "CNY"
  symbol: string                              // "$", "£", "¥"
  precision: number                           // 2
  __typename: 'Catalogs__Currency'

  // Additional fields found in afteraccepting.txt OperationDetails
  name?: string                               // "US Dollar", "Egyptian Pound"
  translationTag?: string                     // "CURRENCIES:USD", "CURRENCIES:EGP"
}

/** CountryInfo sub-object */
export interface CountryImage {
  id: string
  urls: {
    avatar: string
    logo: string
    medium: string
    original?: string
  }
  __typename: string
}

export interface CountryInfo {
  id: string
  image: CountryImage
  __typename: string
}

/** SecurityHub fragment */
export interface SecurityHub {
  id: string
  tierLevel: number
  facialVerified: boolean
  documentVerified: boolean
  verificationStatusName: string
  __typename: string
}

/** User numbers fragment */
export interface UserNumbers {
  id: string
  score: number  // This is the user rating
  completedOperations: number
  __typename: string
}

/** User preferences fragment */
export interface UserProfile {
  id: string
  avatar: string
  language: string
  __typename: string
}

export interface UserPreferences {
  id: string
  profile: UserProfile
  __typename: string
}

/** Full UserData fragment - corrected structure */
export interface UserData {
  id: string
  firstName: string
  lastName: string
  createdAt: string
  country: string  // Fixed: simple string, not union type
  countryInfo: CountryInfo
  securityHub: SecurityHub
  numbers: UserNumbers
  preferences: UserPreferences
  __typename: 'Auth__OperationUser' | 'Auth__User'
}

/** RateInfo fragment */
export interface RateInfo {
  id: string
  netAmount: string
  grossAmount: string
  fundsToReceiveMaker: string | null
  fundsToReceiveTaker: string | null
  fundsToSendMaker: string | null
  fundsToSendTaker: string | null
  __typename: string

  // Additional fields from afteraccepting.txt OperationDetails
  airtmFee?: string                           // "7.44"
  serviceStaticFeeWalletCurrency?: string     // "0.00"
  serviceStaticFeeLocalCurrency?: string      // "0.00"
  servicePercentage?: number                  // 1.36
  takerCommission?: string                    // "8.43"
  takerPercentage?: number                    // 1.55
  takerFeeWalletCurrency?: string            // "0.47"
  takerFixedFeeWalletCurrency?: string       // "0.3"
  takerFixedFee?: string                     // "14.87"
}

/** DisplayRate fragment - Updated to match current schema */
export interface DisplayRate {
  direction: 'TO_LOCAL_CURRENCY' | 'FROM_LOCAL_CURRENCY'
  rate: string  // Added rate field as per AvailableOperations.txt
  __typename: string
}

/** PaymentMethodInstance fragment */
export interface PaymentMethodCategory {
  id: string
  translationTag: string
  ancestor: {
    id: string
    translationTag: string
    __typename: string
  }
  __typename: string
}

export interface PaymentMethodVersion {
  id: string
  image: CountryImage
  category: PaymentMethodCategory
  __typename: string
}

export interface PaymentMethodInstance {
  id: string
  data: any
  categoryId: string
  isThirdPartyInstance: boolean
  version: PaymentMethodVersion
  __typename: string
}

/** Metadata interface for operations */
export interface OperationMetadata {
  isForThirdPartyPaymentMethod: boolean | null
  localCurrencyPrecision?: number
  walletCurrencyPrecision?: number
  quoteId?: string
  deviceSource?: string
  userDeviceId?: string
  previousStatus?: string

  // displayRateInfo structure from afteraccepting.txt
  displayRateInfo?: {
    tags: {
      fee: string | null
      commission: string | null
    }
    version: string
    direction: "fundsToSendMaker" | "fundsToReceiveMaker"
    currencyId: string                        // "EGP"
    exchangeRate: string                      // "49.5709" - Critical for conversion
    fundsToSend: string                       // "11"
    fundsToReceive: string                    // "514.55"
    tagPriceDiff: string | null
    takerFixedFee: string
    serviceFixedFee: string
    takerSublinearMultiplier: string
    takerCommissionPercentage: string
    takerSublinearExponential: string
    serviceSublinearMultiplier: string
    serviceCommissionPercentage: string
    serviceSublinearExponential: string
  }

  [key: string]: any
}

/** The core GraphQL operation response - Updated to match current schema */
export interface AirtmOperation {
  id: string
  hash: string
  operationType: 'BUY' | 'SELL'
  status: 'CREATED' | 'ACCEPTED' | 'REJECTED' | 'COMPLETED' | 'CANCELLED'
  isMine: boolean
  createdAt: string
  updatedAt: string | null
  grossAmount: string
  netAmount: string
  metadata: OperationMetadata
  walletCurrency: CurrencyData
  peer: UserData

  // Present on BUY/SELL operations - Updated per AvailableOperations.txt
  rate?: string  // Added direct rate field for BUY/SELL operations
  rateInfo?: RateInfo
  displayRate?: DisplayRate
  currency?: CurrencyData
  makerPaymentMethod?: PaymentMethodInstance

  __typename: 'Operations__Buy' | 'Operations__Sell'
}

/** GraphQL response wrapper */
export interface AirtmGraphQLResponse {
  data: {
    availableOperations: AirtmOperation[]
  }
  errors?: Array<{
    message: string
    locations?: Array<{
      line: number
      column: number
    }>
    path?: string[]
  }>
}

/** Filter input for AvailableOperations query */
export interface OperationsFilterInput {
  operationType?: 'BUY' | 'SELL'
  currencyId?: string
  minAmount?: number
  maxAmount?: number
  paymentMethodIds?: string[]
  countries?: string[]
}
