<!DOCTYPE html>
<html lang="en" class="options-page">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=0.8, maximum-scale=1.2">
  <title>Airtm Monitor Pro - Settings</title>

  <!-- Preload critical resources -->
  <link rel="preload" href="data:application/octet-stream;base64,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" as="script" crossorigin>

  <!-- Critical CSS for immediate rendering -->
  <style>
    /* Critical CSS for options page - ensure full window sizing */
    html.options-page, body.options {
      width: 100vw !important;
      height: 100vh !important;
      min-width: 100vw !important;
      min-height: 100vh !important;
      max-width: 100vw !important;
      max-height: none !important;
      margin: 0 !important;
      padding: 0 !important;
      overflow-x: hidden !important;
      overflow-y: auto !important;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      background: linear-gradient(135deg, #1e1b4b, #0f172a, #312e81);
    }

    #root {
      width: 100% !important;
      height: 100% !important;
      min-height: 100vh !important;
      display: flex !important;
      flex-direction: column !important;
    }

    /* Loading spinner for immediate feedback */
    .initial-loading {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      color: #a78bfa;
    }

    .initial-loading .spinner {
      width: 2rem;
      height: 2rem;
      border: 3px solid rgba(167, 139, 250, 0.3);
      border-top: 3px solid #a78bfa;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      to { transform: rotate(360deg); }
    }
  </style>
  <script type="module" crossorigin src="/assets/js/options-3ee066fc.js"></script>
  <link rel="modulepreload" crossorigin href="/assets/js/globals-5ef73408.js">
  <link rel="modulepreload" crossorigin href="/assets/js/index-357a2da0.js">
  <link rel="stylesheet" href="/assets/css/globals-944080e6.css">
  <link rel="stylesheet" href="/assets/css/index-b1f7fb87.css">
</head>
<body class="options">
  <div id="root">
    <!-- Initial loading state -->
    <div class="initial-loading">
      <div>
        <div class="spinner"></div>
        <p style="margin-top: 1rem; font-size: 1.125rem;">Loading Airtm Monitor Settings...</p>
      </div>
    </div>
  </div>

  <!-- CSP-compliant error handler -->
  <script src="./error-handler.js"></script>

  

  <!-- Debug helper for development -->
  <script src="./debug.js"></script>
  <script src="./csp-validator.js"></script>
</body>
</html>
