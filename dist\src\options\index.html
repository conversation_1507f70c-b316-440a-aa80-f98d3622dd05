<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Airtm Monitor Pro - Configuration</title>
  
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="/assets/style-92e5be74.css">
</head>
<body>
  <!-- Loading Screen -->
  <div id="loading-screen" class="loading-screen">
    <div class="loading-content">
      <div class="loading-logo">
        <svg viewBox="0 0 100 100" class="loading-spinner">
          <circle cx="50" cy="50" r="45" stroke="currentColor" stroke-width="8" fill="none" stroke-dasharray="283" stroke-dashoffset="283">
            <animate attributeName="stroke-dashoffset" dur="2s" values="283;0;283" repeatCount="indefinite"/>
          </circle>
        </svg>
        <div class="loading-icon">⚡</div>
      </div>
      <h2>Airtm Monitor Pro</h2>
      <p>Loading configuration panel...</p>
    </div>
  </div>

  <!-- Main Application -->
  <div id="app" class="app hidden">
    <!-- Header -->
    <header class="app-header">
      <div class="header-content">
        <div class="header-brand">
          <div class="brand-icon">⚡</div>
          <div class="brand-text">
            <h1>Airtm Monitor Pro</h1>
            <p>Advanced Trading Configuration</p>
          </div>
        </div>
        <div class="header-status">
          <div id="connection-status" class="status-indicator">
            <div class="status-dot"></div>
            <span>Checking connection...</span>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="app-main">
      <div class="main-container">
        <!-- Navigation Sidebar -->
        <nav class="app-nav">
          <div class="nav-section">
            <h3>Configuration</h3>
            <ul class="nav-list">
              <li><a href="#monitoring" class="nav-link active" data-section="monitoring">
                <span class="nav-icon">👁️</span>
                <span>Monitoring</span>
              </a></li>
              <li><a href="#automation" class="nav-link" data-section="automation">
                <span class="nav-icon">🤖</span>
                <span>Automation</span>
              </a></li>
              <li><a href="#notifications" class="nav-link" data-section="notifications">
                <span class="nav-icon">🔔</span>
                <span>Notifications</span>
              </a></li>
              <li><a href="#filters" class="nav-link" data-section="filters">
                <span class="nav-icon">🔍</span>
                <span>Filters</span>
              </a></li>
              <li><a href="#advanced" class="nav-link" data-section="advanced">
                <span class="nav-icon">⚙️</span>
                <span>Advanced</span>
              </a></li>
            </ul>
          </div>
          <div class="nav-section">
            <h3>Support</h3>
            <ul class="nav-list">
              <li><a href="#help" class="nav-link" data-section="help">
                <span class="nav-icon">❓</span>
                <span>Help</span>
              </a></li>
              <li><a href="#about" class="nav-link" data-section="about">
                <span class="nav-icon">ℹ️</span>
                <span>About</span>
              </a></li>
            </ul>
          </div>
        </nav>

        <!-- Content Area -->
        <div class="app-content">
          <!-- Monitoring Section -->
          <section id="monitoring-section" class="content-section active">
            <div class="section-header">
              <h2>Monitoring Configuration</h2>
              <p>Configure how the extension monitors Airtm for new offers</p>
            </div>

            <div class="settings-grid">
              <div class="setting-card">
                <div class="setting-header">
                  <h3>Monitoring Status</h3>
                  <div class="setting-toggle">
                    <input type="checkbox" id="monitoring-enabled" class="toggle-input">
                    <label for="monitoring-enabled" class="toggle-label">
                      <span class="toggle-slider"></span>
                    </label>
                  </div>
                </div>
                <p>Enable or disable automatic monitoring of Airtm offers</p>
                <div class="setting-status">
                  <span id="monitoring-status-text">Disabled</span>
                </div>
              </div>

              <div class="setting-card">
                <div class="setting-header">
                  <h3>Refresh Interval</h3>
                </div>
                <p>How often to check for new offers (in seconds)</p>
                <div class="setting-input-group">
                  <input type="range" id="refresh-interval" min="5" max="60" value="10" class="range-input">
                  <span id="refresh-interval-value" class="input-value">10s</span>
                </div>
              </div>

              <div class="setting-card">
                <div class="setting-header">
                  <h3>Page Monitoring</h3>
                </div>
                <p>Monitor specific Airtm pages for offers</p>
                <div class="checkbox-group">
                  <label class="checkbox-label">
                    <input type="checkbox" id="monitor-buy-page" checked>
                    <span class="checkbox-custom"></span>
                    <span>Buy Offers Page</span>
                  </label>
                  <label class="checkbox-label">
                    <input type="checkbox" id="monitor-sell-page" checked>
                    <span class="checkbox-custom"></span>
                    <span>Sell Offers Page</span>
                  </label>
                </div>
              </div>
            </div>
          </section>

          <!-- Automation Section -->
          <section id="automation-section" class="content-section">
            <div class="section-header">
              <h2>Automation Settings</h2>
              <p>Configure automatic actions and responses</p>
            </div>

            <div class="settings-grid">
              <div class="setting-card">
                <div class="setting-header">
                  <h3>Auto Accept</h3>
                  <div class="setting-toggle">
                    <input type="checkbox" id="auto-accept-enabled" class="toggle-input">
                    <label for="auto-accept-enabled" class="toggle-label">
                      <span class="toggle-slider"></span>
                    </label>
                  </div>
                </div>
                <p>Automatically accept offers that meet your criteria</p>
                <div class="setting-status">
                  <span id="auto-accept-status-text">Disabled</span>
                </div>
              </div>

              <div class="setting-card">
                <div class="setting-header">
                  <h3>Amount Filters</h3>
                </div>
                <p>Set minimum and maximum amounts for auto-acceptance</p>
                <div class="amount-filters">
                  <div class="filter-group">
                    <label>Minimum Amount ($)</label>
                    <input type="number" id="min-amount" min="0" step="0.01" class="amount-input">
                  </div>
                  <div class="filter-group">
                    <label>Maximum Amount ($)</label>
                    <input type="number" id="max-amount" min="0" step="0.01" class="amount-input">
                  </div>
                </div>
              </div>
            </div>
          </section>

          <!-- Notifications Section -->
          <section id="notifications-section" class="content-section">
            <div class="section-header">
              <h2>Notification Settings</h2>
              <p>Configure how you receive alerts about new offers</p>
            </div>

            <div class="settings-grid">
              <div class="setting-card">
                <div class="setting-header">
                  <h3>Telegram Integration</h3>
                </div>
                <p>Send notifications to your Telegram account</p>
                <div class="telegram-config">
                  <div class="input-group">
                    <label>Bot Token</label>
                    <input type="password" id="telegram-bot-token" placeholder="Enter your bot token" class="text-input">
                  </div>
                  <div class="input-group">
                    <label>Chat ID</label>
                    <input type="text" id="telegram-chat-id" placeholder="Enter your chat ID" class="text-input">
                  </div>
                  <button id="test-telegram" class="test-button">Test Connection</button>
                </div>
              </div>
            </div>
          </section>

          <!-- Help Section -->
          <section id="help-section" class="content-section">
            <div class="section-header">
              <h2>Help & Documentation</h2>
              <p>Learn how to use Airtm Monitor Pro effectively</p>
            </div>

            <div class="help-content">
              <div class="help-card">
                <h3>🚀 Getting Started</h3>
                <p>Follow these steps to set up your monitoring:</p>
                <ol>
                  <li>Enable monitoring in the Monitoring section</li>
                  <li>Configure your preferred refresh interval</li>
                  <li>Set up Telegram notifications (optional)</li>
                  <li>Configure automation rules (optional)</li>
                </ol>
              </div>

              <div class="help-card">
                <h3>🔧 Troubleshooting</h3>
                <p>Common issues and solutions:</p>
                <ul>
                  <li><strong>Extension not working:</strong> Check if you're logged into Airtm</li>
                  <li><strong>No notifications:</strong> Verify your Telegram bot configuration</li>
                  <li><strong>Auto-accept not working:</strong> Check your filter settings</li>
                </ul>
              </div>
            </div>
          </section>

          <!-- About Section -->
          <section id="about-section" class="content-section">
            <div class="section-header">
              <h2>About Airtm Monitor Pro</h2>
              <p>Information about this extension</p>
            </div>

            <div class="about-content">
              <div class="about-card">
                <h3>Version Information</h3>
                <p><strong>Version:</strong> 2.0.0</p>
                <p><strong>Build:</strong> Modern Vanilla Architecture</p>
                <p><strong>Last Updated:</strong> <span id="last-updated"></span></p>
              </div>

              <div class="about-card">
                <h3>Features</h3>
                <ul>
                  <li>✅ Real-time offer monitoring</li>
                  <li>✅ Telegram notifications</li>
                  <li>✅ Auto-accept functionality</li>
                  <li>✅ Advanced filtering</li>
                  <li>✅ CSP-compliant architecture</li>
                  <li>✅ Modern responsive design</li>
                </ul>
              </div>
            </div>
          </section>

          <!-- Save Button -->
          <div class="save-section">
            <button id="save-settings" class="save-button">
              <span class="button-icon">💾</span>
              <span>Save Configuration</span>
            </button>
            <div id="save-status" class="save-status hidden"></div>
          </div>
        </div>
      </div>
    </main>
  </div>

  <!-- Scripts -->
  <script src="./app.js"></script>
</body>
</html>
