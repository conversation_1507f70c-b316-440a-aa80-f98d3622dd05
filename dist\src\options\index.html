<!DOCTYPE html>
<html lang="en" class="options-page">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=0.8, maximum-scale=1.2">
  <title>Airtm Monitor Pro - Settings</title>

  <!-- Preload critical resources -->
  <link rel="preload" href="data:application/octet-stream;base64,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" as="script" crossorigin>

  <!-- Critical CSS for immediate rendering -->
  <style>
    /* Critical CSS for options page - ensure full window sizing */
    html.options-page, body.options {
      width: 100vw !important;
      height: 100vh !important;
      min-width: 100vw !important;
      min-height: 100vh !important;
      max-width: 100vw !important;
      max-height: none !important;
      margin: 0 !important;
      padding: 0 !important;
      overflow-x: hidden !important;
      overflow-y: auto !important;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      background: linear-gradient(135deg, #1e1b4b, #0f172a, #312e81);
    }

    #root {
      width: 100% !important;
      height: 100% !important;
      min-height: 100vh !important;
      display: flex !important;
      flex-direction: column !important;
    }

    /* Loading spinner for immediate feedback */
    .initial-loading {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      color: #a78bfa;
    }

    .initial-loading .spinner {
      width: 2rem;
      height: 2rem;
      border: 3px solid rgba(167, 139, 250, 0.3);
      border-top: 3px solid #a78bfa;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      to { transform: rotate(360deg); }
    }
  </style>
  <script type="module" crossorigin src="/assets/options-f5e5335a.js"></script>
  <link rel="modulepreload" crossorigin href="/assets/globals-31bbfa66.js">
  <link rel="modulepreload" crossorigin href="/assets/index-357a2da0.js">
  <link rel="stylesheet" href="/assets/globals-944080e6.css">
  <link rel="stylesheet" href="/assets/index-b1f7fb87.css">
</head>
<body class="options">
  <div id="root">
    <!-- Initial loading state -->
    <div class="initial-loading">
      <div>
        <div class="spinner"></div>
        <p style="margin-top: 1rem; font-size: 1.125rem;">Loading Airtm Monitor Settings...</p>
      </div>
    </div>
  </div>

  <!-- Error handling script -->
  <script>
    window.addEventListener('error', function(e) {
      console.error('Options page error:', e.error);
      const root = document.getElementById('root');
      if (root) {
        root.innerHTML = `
          <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; min-height: 100vh; color: white; text-align: center; padding: 2rem;">
            <h1 style="color: #ef4444; font-size: 2rem; margin-bottom: 1rem;">Configuration Error</h1>
            <p style="color: #94a3b8; font-size: 1.125rem; margin-bottom: 2rem;">Failed to load the options page. Please try refreshing or check the console for details.</p>
            <button onclick="window.location.reload()" style="background: linear-gradient(to right, #8b5cf6, #7c3aed); color: white; padding: 0.75rem 1.5rem; border-radius: 0.5rem; border: none; cursor: pointer; font-weight: 600;">
              Reload Page
            </button>
          </div>
        `;
      }
    });
  </script>

  

  <!-- Debug helper for development -->
  <script src="./debug.js"></script>
</body>
</html>
