<!DOCTYPE html>
<html lang="en" class="options-page">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=0.8, maximum-scale=1.2">
  <title>Airtm Monitor Pro - Settings</title>
  <style>
    /* Critical CSS for options page - ensure full window sizing */
    html.options-page, body.options {
      width: 100vw !important;
      height: 100vh !important;
      min-width: 100vw !important;
      min-height: 100vh !important;
      max-width: 100vw !important;
      max-height: none !important;
      margin: 0 !important;
      padding: 0 !important;
      overflow-x: hidden !important;
      overflow-y: auto !important;
    }
    #root {
      width: 100% !important;
      height: 100% !important;
      min-height: 100vh !important;
    }
  </style>
  <script type="module" crossorigin src="/assets/options-5f477295.js"></script>
  <link rel="modulepreload" crossorigin href="/assets/globals-457874bd.js">
  <link rel="modulepreload" crossorigin href="/assets/index-357a2da0.js">
  <link rel="stylesheet" href="/assets/globals-5895a18d.css">
</head>
<body class="options">
  <div id="root"></div>
  
</body>
</html>
