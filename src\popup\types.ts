/**
 * TypeScript interfaces and types for Airtm Monitor Pro Popup
 */
import React from 'react';

export interface Settings {
  monitoring: boolean;
  refreshInterval: number;
  monitorBuyPage: boolean;
  monitorSellPage: boolean;
  autoAccept: boolean;
  telegramBotToken: string;
  telegramChatId: string;
  minAmount: number;
  maxAmount: number;
  preferredCurrencies: string[];
  blacklistedUsers: string[];
}

export interface Stats {
  offersCount: number;
  monitoringStartTime: number | null;
  isConnected: boolean;
}

export interface PopupHeaderProps {
  isConnected: boolean;
}

export interface StatusCardsProps {
  stats: Stats;
  settings: Settings;
  onMonitoringToggle: (enabled: boolean) => void;
}

export interface QuickActionsProps {
  onOpenAirtm: () => void;
  onViewOffers: () => void;
  onOpenSettings: () => void;
}

export interface PopupFooterProps {
  onHelpClick: () => void;
  onAboutClick: () => void;
}

export interface MonitoringTimeProps {
  monitoringStartTime: number | null;
  isMonitoring: boolean;
}

export interface ToggleSwitchProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
  disabled?: boolean;
}

export interface ActionButtonProps {
  icon: string;
  label: string;
  onClick: () => void;
  variant?: 'primary' | 'secondary';
  disabled?: boolean;
}

export interface StatusItemProps {
  value: string | number | React.ReactNode;
  label: string;
}

export interface ChromeMessage {
  action: string;
  [key: string]: any;
}

export interface ChromeStorageResult {
  settings?: Partial<Settings>;
  stats?: Partial<Stats>;
  monitoringStartTime?: number;
}

// Chrome Extension API types
export interface ChromeStorage {
  sync: {
    get: (keys?: string | string[] | null) => Promise<any>;
    set: (items: any) => Promise<void>;
  };
  local: {
    get: (keys?: string | string[] | null) => Promise<any>;
    set: (items: any) => Promise<void>;
  };
}

export interface ChromeRuntime {
  sendMessage: (message: ChromeMessage) => void;
  openOptionsPage: () => void;
  getURL: (path: string) => string;
}

export interface ChromeTabs {
  create: (createProperties: { url: string }) => void;
}

export interface ChromeAPI {
  storage?: ChromeStorage;
  runtime?: ChromeRuntime;
  tabs?: ChromeTabs;
}

// Default values
export const DEFAULT_SETTINGS: Settings = {
  monitoring: false,
  refreshInterval: 10,
  monitorBuyPage: true,
  monitorSellPage: true,
  autoAccept: false,
  telegramBotToken: '',
  telegramChatId: '',
  minAmount: 0,
  maxAmount: 10000,
  preferredCurrencies: ['USD'],
  blacklistedUsers: []
};

export const DEFAULT_STATS: Stats = {
  offersCount: 0,
  monitoringStartTime: null,
  isConnected: true
};
