import React from 'react';
import { QuickActionsProps } from '../types';
import ActionButton from './ActionButton';

const QuickActions: React.FC<QuickActionsProps> = ({
  onOpenAirtm,
  onViewOffers,
  onOpenSettings
}) => {
  return (
    <div className="status-card">
      <h3>⚡ Quick Actions</h3>
      <div className="quick-actions">
        <ActionButton
          icon="🌐"
          label="Open Airtm"
          onClick={onOpenAirtm}
          variant="primary"
        />
        <ActionButton
          icon="📋"
          label="View Offers"
          onClick={onViewOffers}
          variant="secondary"
        />
        <ActionButton
          icon="⚙️"
          label="Settings"
          onClick={onOpenSettings}
          variant="secondary"
        />
      </div>
    </div>
  );
};

export default QuickActions;
