import{r as h,j as e,P as S,C as M,X as w,a as A}from"./globals-e740fbb8.js";import{D as T}from"./index-357a2da0.js";const g=({label:s,icon:v,value:d,onChange:f,placeholder:m,className:p=""})=>{const[n,i]=h.useState(""),[o,u]=h.useState([]),[c,t]=h.useState(!1),r=a=>{if(a.key==="Enter")if(a.preventDefault(),c)if(n.trim()){const l=[...o,n.trim()];u(l),i("")}else x();else n.trim()&&(u([n.trim()]),i(""),t(!0));else a.key==="Escape"&&(c?b():i(""))},x=()=>{o.length>0&&f([...d,...o]),u([]),t(!1),i("")},b=()=>{u([]),t(!1),i("")},N=a=>{const l=o.filter((E,j)=>j!==a);u(l),l.length===0&&t(!1)},k=a=>{const l=d.filter((E,j)=>j!==a);f(l)},C=()=>c?o.length===0?"Type next keyword and press Enter...":"Add another keyword or press Enter on empty to finish group":m;return e.jsxs("div",{className:`space-y-3 ${p}`,children:[e.jsxs("label",{className:"text-lg font-bold text-white flex items-center space-x-2",children:[e.jsx("span",{children:v}),e.jsx("span",{children:s})]}),e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",value:n,onChange:a=>i(a.target.value),onKeyDown:r,className:`w-full px-6 py-4 bg-slate-800/60 border rounded-2xl text-white text-lg placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-violet-500/50 focus:border-violet-500/50 backdrop-blur-sm transition-all duration-300 hover:border-violet-500/30 ${c?"border-yellow-500/50 ring-2 ring-yellow-500/20 bg-yellow-500/5":"border-slate-600/50"}`,placeholder:C()}),c&&e.jsxs("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center space-x-2",children:[e.jsx("span",{className:"text-yellow-400 text-sm font-medium",children:"Grouping Mode"}),e.jsx("div",{className:"w-2 h-2 bg-yellow-400 rounded-full animate-pulse"})]})]}),c&&e.jsxs("div",{className:"p-4 bg-gradient-to-r from-yellow-500/10 to-orange-500/10 border border-yellow-500/30 rounded-2xl backdrop-blur-sm",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsxs("h4",{className:"text-yellow-300 font-bold flex items-center space-x-2",children:[e.jsx(S,{className:"w-4 h-4"}),e.jsxs("span",{children:["Building Group (",o.length," items)"]})]}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsxs("button",{onClick:x,className:"px-3 py-1 bg-green-500/20 text-green-300 rounded-lg text-sm font-medium hover:bg-green-500/30 transition-colors flex items-center space-x-1",children:[e.jsx(M,{className:"w-3 h-3"}),e.jsx("span",{children:"Finish"})]}),e.jsxs("button",{onClick:b,className:"px-3 py-1 bg-red-500/20 text-red-300 rounded-lg text-sm font-medium hover:bg-red-500/30 transition-colors flex items-center space-x-1",children:[e.jsx(w,{className:"w-3 h-3"}),e.jsx("span",{children:"Cancel"})]})]})]}),o.length>0&&e.jsxs("div",{className:"space-y-2",children:[e.jsx("p",{className:"text-yellow-200/80 text-sm",children:"Keywords in current group:"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:o.map((a,l)=>e.jsxs("span",{className:"inline-flex items-center space-x-1 px-3 py-1 bg-yellow-500/20 text-yellow-200 rounded-lg text-sm border border-yellow-500/30",children:[e.jsx("span",{children:a}),e.jsx("button",{onClick:()=>N(l),className:"text-yellow-300 hover:text-red-300 transition-colors",children:e.jsx(w,{className:"w-3 h-3"})})]},l))})]}),e.jsxs("div",{className:"mt-3 text-yellow-200/70 text-xs space-y-1",children:[e.jsxs("p",{children:["• Press ",e.jsx("kbd",{className:"px-1 py-0.5 bg-yellow-500/20 rounded text-yellow-300",children:"Enter"})," to add current keyword to group"]}),e.jsxs("p",{children:["• Press ",e.jsx("kbd",{className:"px-1 py-0.5 bg-yellow-500/20 rounded text-yellow-300",children:"Enter"})," on empty input to finish group"]}),e.jsxs("p",{children:["• Press ",e.jsx("kbd",{className:"px-1 py-0.5 bg-yellow-500/20 rounded text-yellow-300",children:"Escape"})," to cancel grouping"]})]})]}),d.length>0&&e.jsxs("div",{className:"space-y-3",children:[e.jsxs("p",{className:"text-slate-300 text-sm font-medium",children:["Current ",s.toLowerCase(),":"]}),e.jsx("div",{className:"flex flex-wrap gap-2",children:d.map((a,l)=>e.jsxs("span",{className:"inline-flex items-center space-x-2 px-4 py-2 bg-violet-500/20 text-violet-200 rounded-xl text-sm border border-violet-500/30 hover:border-violet-400/50 transition-all duration-200",children:[e.jsx("span",{children:a}),e.jsx("button",{onClick:()=>k(l),className:"text-violet-300 hover:text-red-300 transition-colors",children:e.jsx(w,{className:"w-4 h-4"})})]},l))})]})]})},B=()=>{const[s,v]=h.useState(T),[d,f]=h.useState(!1),[m,p]=h.useState(""),[n,i]=h.useState(!1);h.useEffect(()=>{u(),o()},[]);const o=async()=>{try{await chrome.runtime.sendMessage({type:"PING"}),i(!0)}catch{i(!1),console.log("Background script not responding")}},u=async()=>{try{const r=await chrome.storage.sync.get("settings");r.settings&&v(r.settings)}catch(r){console.error("Error loading settings:",r)}},c=async()=>{try{f(!0),p(""),await chrome.storage.sync.set({settings:s});try{await chrome.runtime.sendMessage({type:"SETTINGS_UPDATE",settings:s})}catch(r){console.log("Background script not available:",r)}p("Settings saved successfully!"),setTimeout(()=>p(""),3e3)}catch(r){console.error("Error saving settings:",r),p("Error saving settings")}finally{f(!1)}},t=(r,x)=>{v(b=>({...b,[r]:x}))};return e.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-violet-950 via-slate-900 to-indigo-950",children:[e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-violet-600/20 via-purple-600/20 to-indigo-600/20 blur-3xl"}),e.jsx("div",{className:"relative backdrop-blur-xl bg-black/30 border-b border-violet-500/20",children:e.jsx("div",{className:"max-w-7xl mx-auto px-8 py-8",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"space-y-2",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-12 h-12 bg-gradient-to-br from-violet-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg",children:e.jsxs("svg",{className:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-4xl font-bold bg-gradient-to-r from-white via-violet-200 to-purple-200 bg-clip-text text-transparent",children:"Airtm Monitor"}),e.jsx("p",{className:"text-violet-300 text-lg font-medium",children:"Professional Configuration Center"})]})]})}),e.jsxs("div",{className:"flex items-center space-x-6",children:[e.jsxs("div",{className:`px-6 py-3 rounded-2xl text-sm font-bold flex items-center space-x-3 transition-all duration-500 ${n?"bg-gradient-to-r from-emerald-500/20 to-green-500/20 text-emerald-300 border border-emerald-500/30 shadow-lg shadow-emerald-500/10":"bg-gradient-to-r from-red-500/20 to-pink-500/20 text-red-300 border border-red-500/30 shadow-lg shadow-red-500/10"}`,children:[e.jsx("div",{className:`w-3 h-3 rounded-full animate-pulse ${n?"bg-emerald-400 shadow-lg shadow-emerald-400/50":"bg-red-400 shadow-lg shadow-red-400/50"}`}),e.jsx("span",{children:n?"System Online":"System Offline"})]}),e.jsx("button",{onClick:c,disabled:d,className:`px-8 py-4 rounded-2xl font-bold text-lg transition-all duration-500 transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-violet-500/30 ${d?"bg-gradient-to-r from-slate-600 to-slate-700 text-slate-400 cursor-not-allowed":"bg-gradient-to-r from-violet-600 to-purple-600 text-white hover:from-violet-500 hover:to-purple-500 shadow-2xl shadow-violet-500/25 hover:shadow-violet-500/40"}`,children:d?e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-2 border-slate-400 border-t-transparent"}),e.jsx("span",{children:"Saving Configuration..."})]}):e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),e.jsx("span",{children:"Save Configuration"})]})})]})]})})})]}),e.jsxs("div",{className:"max-w-7xl mx-auto px-8 py-12",children:[m&&e.jsx("div",{className:`mb-8 p-6 rounded-3xl backdrop-blur-xl border transition-all duration-500 ${m.includes("Error")?"bg-red-500/10 border-red-500/30 text-red-300 shadow-lg shadow-red-500/10":"bg-emerald-500/10 border-emerald-500/30 text-emerald-300 shadow-lg shadow-emerald-500/10"}`,children:e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:`w-4 h-4 rounded-full animate-pulse ${m.includes("Error")?"bg-red-400 shadow-lg shadow-red-400/50":"bg-emerald-400 shadow-lg shadow-emerald-400/50"}`}),e.jsx("span",{className:"font-bold text-lg",children:m})]})}),e.jsxs("div",{className:"grid gap-8",children:[e.jsxs("section",{className:"relative group",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-cyan-500/10 via-blue-500/10 to-violet-500/10 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-500"}),e.jsxs("div",{className:"relative backdrop-blur-xl bg-black/40 border border-cyan-500/20 rounded-3xl p-8 hover:border-cyan-400/30 transition-all duration-500",children:[e.jsxs("div",{className:"flex items-center space-x-4 mb-8",children:[e.jsx("div",{className:"w-14 h-14 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-xl shadow-cyan-500/25",children:e.jsx("svg",{className:"w-7 h-7 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"})})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-3xl font-bold bg-gradient-to-r from-cyan-300 to-blue-300 bg-clip-text text-transparent",children:"Core System"}),e.jsx("p",{className:"text-cyan-200/80 text-lg",children:"Essential monitoring controls"})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between p-6 bg-gradient-to-r from-slate-800/50 to-slate-700/50 rounded-2xl border border-slate-600/30 hover:border-cyan-500/30 transition-all duration-300",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-lg font-bold text-white",children:"System Monitoring"}),e.jsx("p",{className:"text-slate-300",children:"Enable real-time offer detection and processing"})]}),e.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[e.jsx("input",{type:"checkbox",checked:s.monitoring,onChange:r=>t("monitoring",r.target.checked),className:"sr-only peer"}),e.jsx("div",{className:"w-14 h-8 bg-slate-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-cyan-500/30 rounded-full peer peer-checked:after:translate-x-6 peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-7 after:w-7 after:transition-all peer-checked:bg-gradient-to-r peer-checked:from-cyan-500 peer-checked:to-blue-500 peer-checked:shadow-lg peer-checked:shadow-cyan-500/25"})]})]}),e.jsxs("div",{className:"flex items-center justify-between p-6 bg-gradient-to-r from-slate-800/50 to-slate-700/50 rounded-2xl border border-slate-600/30 hover:border-cyan-500/30 transition-all duration-300",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-lg font-bold text-white",children:"Auto-Accept Mode"}),e.jsx("p",{className:"text-slate-300",children:"Automatically accept offers matching your criteria"})]}),e.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[e.jsx("input",{type:"checkbox",checked:s.autoAccept,onChange:r=>t("autoAccept",r.target.checked),className:"sr-only peer"}),e.jsx("div",{className:"w-14 h-8 bg-slate-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-cyan-500/30 rounded-full peer peer-checked:after:translate-x-6 peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-7 after:w-7 after:transition-all peer-checked:bg-gradient-to-r peer-checked:from-cyan-500 peer-checked:to-blue-500 peer-checked:shadow-lg peer-checked:shadow-cyan-500/25"})]})]})]})]})]}),e.jsxs("section",{className:"relative group",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-emerald-500/10 via-green-500/10 to-teal-500/10 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-500"}),e.jsxs("div",{className:"relative backdrop-blur-xl bg-black/40 border border-emerald-500/20 rounded-3xl p-8 hover:border-emerald-400/30 transition-all duration-500",children:[e.jsxs("div",{className:"flex items-center space-x-4 mb-8",children:[e.jsx("div",{className:"w-14 h-14 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center shadow-xl shadow-emerald-500/25",children:e.jsxs("svg",{className:"w-7 h-7 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 17h5l-5 5v-5z"}),e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 19.5A2.5 2.5 0 0 1 6.5 17H20"})]})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-3xl font-bold bg-gradient-to-r from-emerald-300 to-teal-300 bg-clip-text text-transparent",children:"Alert System"}),e.jsx("p",{className:"text-emerald-200/80 text-lg",children:"Notification preferences and alerts"})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between p-6 bg-gradient-to-r from-slate-800/50 to-slate-700/50 rounded-2xl border border-slate-600/30 hover:border-emerald-500/30 transition-all duration-300",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-lg font-bold text-white",children:"Desktop Notifications"}),e.jsx("p",{className:"text-slate-300",children:"System notifications for new matching offers"})]}),e.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[e.jsx("input",{type:"checkbox",checked:s.notificationsEnabled,onChange:r=>t("notificationsEnabled",r.target.checked),className:"sr-only peer"}),e.jsx("div",{className:"w-14 h-8 bg-slate-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-emerald-500/30 rounded-full peer peer-checked:after:translate-x-6 peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-7 after:w-7 after:transition-all peer-checked:bg-gradient-to-r peer-checked:from-emerald-500 peer-checked:to-teal-500 peer-checked:shadow-lg peer-checked:shadow-emerald-500/25"})]})]}),e.jsxs("div",{className:"flex items-center justify-between p-6 bg-gradient-to-r from-slate-800/50 to-slate-700/50 rounded-2xl border border-slate-600/30 hover:border-emerald-500/30 transition-all duration-300",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-lg font-bold text-white",children:"Audio Alerts"}),e.jsx("p",{className:"text-slate-300",children:"Sound notifications for instant awareness"})]}),e.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[e.jsx("input",{type:"checkbox",checked:s.soundEnabled,onChange:r=>t("soundEnabled",r.target.checked),className:"sr-only peer"}),e.jsx("div",{className:"w-14 h-8 bg-slate-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-emerald-500/30 rounded-full peer peer-checked:after:translate-x-6 peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-7 after:w-7 after:transition-all peer-checked:bg-gradient-to-r peer-checked:from-emerald-500 peer-checked:to-teal-500 peer-checked:shadow-lg peer-checked:shadow-emerald-500/25"})]})]}),e.jsx("div",{className:"p-6 bg-gradient-to-r from-amber-500/10 to-orange-500/10 border border-amber-500/30 rounded-2xl backdrop-blur-sm",children:e.jsxs("div",{className:"flex items-start space-x-4",children:[e.jsx("div",{className:"w-6 h-6 text-amber-400 mt-1 flex-shrink-0",children:e.jsx("svg",{fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})})}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-bold text-amber-300",children:"Permission Required"}),e.jsx("p",{className:"text-amber-200/90 mt-2 leading-relaxed",children:"Enable browser notifications for this extension. Look for the notification icon in your address bar and grant permission for the best experience."})]})]})})]})]})]}),e.jsxs("section",{className:"relative group",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-violet-500/10 via-purple-500/10 to-fuchsia-500/10 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-500"}),e.jsxs("div",{className:"relative backdrop-blur-xl bg-black/40 border border-violet-500/20 rounded-3xl p-8 hover:border-violet-400/30 transition-all duration-500",children:[e.jsxs("div",{className:"flex items-center space-x-4 mb-8",children:[e.jsx("div",{className:"w-14 h-14 bg-gradient-to-br from-violet-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-xl shadow-violet-500/25",children:e.jsx("svg",{className:"w-7 h-7 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"})})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-3xl font-bold bg-gradient-to-r from-violet-300 to-purple-300 bg-clip-text text-transparent",children:"Smart Filters"}),e.jsx("p",{className:"text-violet-200/80 text-lg",children:"Advanced offer matching criteria"})]})]}),e.jsxs("div",{className:"space-y-8",children:[e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsxs("label",{className:"text-lg font-bold text-white flex items-center space-x-2",children:[e.jsx("span",{children:"💰"}),e.jsx("span",{children:"Minimum Amount"})]}),e.jsx("input",{type:"number",value:s.minAmount,onChange:r=>t("minAmount",parseFloat(r.target.value)||0),className:"w-full px-6 py-4 bg-slate-800/60 border border-slate-600/50 rounded-2xl text-white text-lg placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-violet-500/50 focus:border-violet-500/50 backdrop-blur-sm transition-all duration-300 hover:border-violet-500/30",placeholder:"0.00"})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("label",{className:"text-lg font-bold text-white flex items-center space-x-2",children:[e.jsx("span",{children:"💎"}),e.jsx("span",{children:"Maximum Amount"})]}),e.jsx("input",{type:"number",value:s.maxAmount,onChange:r=>t("maxAmount",parseFloat(r.target.value)||0),className:"w-full px-6 py-4 bg-slate-800/60 border border-slate-600/50 rounded-2xl text-white text-lg placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-violet-500/50 focus:border-violet-500/50 backdrop-blur-sm transition-all duration-300 hover:border-violet-500/30",placeholder:"0.00"})]})]}),e.jsx(g,{label:"Preferred Currencies",icon:"🌍",value:s.preferredCurrencies,onChange:r=>t("preferredCurrencies",r),placeholder:"Type currency and press Enter to start grouping (e.g., USD, EUR, GBP, BTC)"}),e.jsx(g,{label:"Keywords",icon:"🔍",value:s.keywords,onChange:r=>t("keywords",r),placeholder:"Type keyword and press Enter to start grouping (e.g., bitcoin, crypto, exchange)"}),e.jsx(g,{label:"Blacklist Keywords",icon:"🚫",value:s.blacklistKeywords,onChange:r=>t("blacklistKeywords",r),placeholder:"Type keyword and press Enter to start grouping (e.g., scam, fraud, avoid)"}),e.jsx(g,{label:"Payment Methods",icon:"💳",value:s.paymentMethods,onChange:r=>t("paymentMethods",r),placeholder:"Type payment method and press Enter to start grouping (e.g., PayPal, Bank Transfer, Wise)"}),e.jsx(g,{label:"Target Countries",icon:"🗺️",value:s.countries,onChange:r=>t("countries",r),placeholder:"Type country and press Enter to start grouping (e.g., US, UK, CA, DE, AU)"})]})]})]}),e.jsxs("section",{className:"relative group",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-500/10 via-indigo-500/10 to-purple-500/10 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-500"}),e.jsxs("div",{className:"relative backdrop-blur-xl bg-black/40 border border-blue-500/20 rounded-3xl p-8 hover:border-blue-400/30 transition-all duration-500",children:[e.jsxs("div",{className:"flex items-center space-x-4 mb-8",children:[e.jsx("div",{className:"w-14 h-14 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-xl shadow-blue-500/25",children:e.jsx("svg",{className:"w-7 h-7 text-white",fill:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{d:"M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z"})})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-3xl font-bold bg-gradient-to-r from-blue-300 to-indigo-300 bg-clip-text text-transparent",children:"Telegram Integration"}),e.jsx("p",{className:"text-blue-200/80 text-lg",children:"Instant notifications via Telegram"})]})]}),e.jsxs("div",{className:"space-y-8",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsxs("label",{className:"text-lg font-bold text-white flex items-center space-x-2",children:[e.jsx("span",{children:"🤖"}),e.jsx("span",{children:"Bot Token"})]}),e.jsx("input",{type:"password",value:s.telegramBotToken||"",onChange:r=>t("telegramBotToken",r.target.value),className:"w-full px-6 py-4 bg-slate-800/60 border border-slate-600/50 rounded-2xl text-white text-lg placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 backdrop-blur-sm transition-all duration-300 hover:border-blue-500/30",placeholder:"Enter your Telegram Bot Token"})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("label",{className:"text-lg font-bold text-white flex items-center space-x-2",children:[e.jsx("span",{children:"💬"}),e.jsx("span",{children:"Chat ID"})]}),e.jsx("input",{type:"text",value:s.telegramChatId||"",onChange:r=>t("telegramChatId",r.target.value),className:"w-full px-6 py-4 bg-slate-800/60 border border-slate-600/50 rounded-2xl text-white text-lg placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 backdrop-blur-sm transition-all duration-300 hover:border-blue-500/30",placeholder:"Enter your Telegram Chat ID"})]}),e.jsx("div",{className:"p-6 bg-gradient-to-r from-cyan-500/10 to-blue-500/10 border border-cyan-500/30 rounded-2xl backdrop-blur-sm",children:e.jsxs("div",{className:"flex items-start space-x-4",children:[e.jsx("div",{className:"w-6 h-6 text-cyan-400 mt-1 flex-shrink-0",children:e.jsx("svg",{fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})})}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-bold text-cyan-300",children:"Quick Setup Guide"}),e.jsxs("div",{className:"mt-3 space-y-2 text-cyan-200/90",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"w-6 h-6 bg-cyan-500/20 rounded-full flex items-center justify-center text-xs font-bold",children:"1"}),e.jsx("span",{children:"Message @BotFather on Telegram to create a new bot"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"w-6 h-6 bg-cyan-500/20 rounded-full flex items-center justify-center text-xs font-bold",children:"2"}),e.jsx("span",{children:"Copy the bot token and paste it in the field above"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"w-6 h-6 bg-cyan-500/20 rounded-full flex items-center justify-center text-xs font-bold",children:"3"}),e.jsx("span",{children:"Start a chat with your bot and send any message"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"w-6 h-6 bg-cyan-500/20 rounded-full flex items-center justify-center text-xs font-bold",children:"4"}),e.jsx("span",{children:"Get your chat ID from @userinfobot or browser console"})]})]})]})]})})]})]})]}),e.jsxs("section",{className:"relative group",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-purple-500/10 via-fuchsia-500/10 to-pink-500/10 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-500"}),e.jsxs("div",{className:"relative backdrop-blur-xl bg-black/40 border border-purple-500/20 rounded-3xl p-8 hover:border-purple-400/30 transition-all duration-500",children:[e.jsxs("div",{className:"flex items-center space-x-4 mb-8",children:[e.jsx("div",{className:"w-14 h-14 bg-gradient-to-br from-purple-500 to-fuchsia-600 rounded-2xl flex items-center justify-center shadow-xl shadow-purple-500/25",children:e.jsx("svg",{className:"w-7 h-7 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"})})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-3xl font-bold bg-gradient-to-r from-purple-300 to-pink-300 bg-clip-text text-transparent",children:"Keyboard Shortcuts"}),e.jsx("p",{className:"text-purple-200/80 text-lg",children:"Power user hotkeys for quick actions"})]})]}),e.jsx("div",{className:"space-y-6",children:Object.entries(s.hotkeys).map(([r,x])=>e.jsx("div",{className:"p-6 bg-gradient-to-r from-slate-800/50 to-slate-700/50 rounded-2xl border border-slate-600/30 hover:border-purple-500/30 transition-all duration-300",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-lg font-bold text-white",children:x.description}),e.jsx("p",{className:"text-slate-300",children:"Quick access hotkey"})]}),e.jsx("input",{type:"text",value:x.combination,onChange:b=>t("hotkeys",{...s.hotkeys,[r]:{...x,combination:b.target.value}}),className:"px-6 py-4 bg-slate-800/60 border border-slate-600/50 rounded-2xl text-white text-lg placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/50 backdrop-blur-sm transition-all duration-300 hover:border-purple-500/30",placeholder:"Ctrl+Shift+Key"})]})},r))}),e.jsx("div",{className:"p-6 bg-gradient-to-r from-fuchsia-500/10 to-purple-500/10 border border-fuchsia-500/30 rounded-2xl backdrop-blur-sm mt-8",children:e.jsxs("div",{className:"flex items-start space-x-4",children:[e.jsx("div",{className:"w-6 h-6 text-fuchsia-400 mt-1 flex-shrink-0",children:e.jsx("svg",{fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})})}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-bold text-fuchsia-300",children:"Chrome Extension Settings"}),e.jsx("p",{className:"text-fuchsia-200/90 mt-2 leading-relaxed",children:"Keyboard shortcuts can be customized in Chrome Extensions settings. Navigate to chrome://extensions/shortcuts to configure global hotkeys."})]})]})})]})]})]})]})]})},y=document.getElementById("root");if(!y)throw new Error("Root element not found");const L=A(y);L.render(e.jsx(B,{}));
