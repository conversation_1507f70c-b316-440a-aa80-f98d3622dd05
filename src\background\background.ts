/**
 * Background Service Worker for Airtm Monitor Pro Extension
 * Handles offer processing, notifications, and external integrations
 */

import type { ExtensionMessage, ExtensionSettings, Offer, OfferStats } from '../types'
import { DEFAULT_SETTINGS } from '../types'
import { notificationManager } from '../services/NotificationManager'
import { AirTMPaymentMethodMatcher } from '../utils/AirTMPaymentMethodMatcher'
import { isExtensionContextValid, isContextInvalidationError } from '../utils/extension-context'

// Global state
// let isMonitoring = false // Removed unused variable
let currentSettings: ExtensionSettings = DEFAULT_SETTINGS
const paymentMethodMatcher: AirTMPaymentMethodMatcher = new AirTMPaymentMethodMatcher()
const offerStats: OfferStats = {
  totalOffers: 0,
  newOffers: 0,
  acceptedOffers: 0,
  rejectedOffers: 0,
  averageRate: 0,
  lastUpdate: new Date().toISOString()
}

// Track notified offers to prevent duplicates
let notifiedOffers: Map<string, number> = new Map()
let lastNotificationCleanup = Date.now()
const NOTIFICATION_CLEANUP_INTERVAL = 2 * 60 * 1000 // 2 minutes
const NOTIFICATION_MEMORY_DURATION = 60 * 60 * 1000 // 1 hour

/**
 * Load notified offers from storage
 */
async function loadNotifiedOffers(): Promise<void> {
  try {
    const result = await chrome.storage.local.get('notified_offers')
    
    if (result.notified_offers) {
      // Convert the stored object back to a Map
      notifiedOffers = new Map(Object.entries(result.notified_offers))
      console.log(`Loaded ${notifiedOffers.size} notified offers from storage`)
    } else {
      notifiedOffers = new Map()
      console.log('No previously notified offers found in storage')
    }
  } catch (error) {
    console.error('Error loading notified offers:', error)
    notifiedOffers = new Map()
  }
}

/**
 * Save notified offers to storage
 */
async function saveNotifiedOffers(): Promise<void> {
  try {
    // Convert Map to a plain object for storage
    const notifiedOffersObj = Object.fromEntries(notifiedOffers)
    await chrome.storage.local.set({ notified_offers: notifiedOffersObj })
  } catch (error) {
    console.error('Error saving notified offers:', error)
  }
}

// Global initialization flag to prevent multiple initializations
let isInitialized = false

/**
 * Initialize background service worker
 */
async function initializeBackground(): Promise<void> {
  // Prevent multiple initializations
  if (isInitialized) {
    console.log('Background service worker already initialized, skipping...')
    return
  }

  console.log('Airtm Monitor Pro: Background service worker initializing...')

  try {
    // Check if Chrome APIs are available
    if (typeof chrome === 'undefined' || !chrome.runtime) {
      throw new Error('Chrome runtime APIs not available')
    }

    // Load notified offers from storage
    await loadNotifiedOffers()
    
    // Load settings
    await loadSettings()

    // Setup message listeners
    setupMessageListeners()

    // Setup alarm listeners
    setupAlarmListeners()

    // Setup notification click handlers
    setupNotificationHandlers()

    // Inject content scripts into existing Airtm tabs
    await injectContentScriptsIntoExistingTabs()

    // Set extension badge based on monitoring state
    updateExtensionBadge()

    // Mark as initialized
    isInitialized = true
    console.log('Background service worker initialized successfully')

    // Run currency conversion test on startup
    testCurrencyConversion()

  } catch (error) {
    console.error('Error initializing background service worker:', error)
    
    // Try to set error badge if possible
    try {
      chrome.action?.setBadgeText({ text: '!' })
      chrome.action?.setBadgeBackgroundColor({ color: '#ff0000' })
    } catch (badgeError) {
      console.error('Could not set error badge:', badgeError)
    }
  }
}

/**
 * Setup message listeners
 */
function setupMessageListeners(): void {
  // Remove any existing listeners to prevent duplicates
  if (chrome.runtime.onMessage.hasListeners()) {
    chrome.runtime.onMessage.removeListener
  }

  chrome.runtime.onMessage.addListener((message: ExtensionMessage, sender, sendResponse) => {
    console.log('Background received message:', message.type, 'from:', sender.tab?.url || 'popup/options')

    // Validate message structure
    if (!message || typeof message.type !== 'string') {
      console.error('Invalid message received:', message)
      sendResponse({ success: false, error: 'Invalid message format' })
      return
    }

    try {
      switch (message.type) {
        case 'OFFERS_UPDATE':
          handleOffersUpdate(message.data)
            .then(() => sendResponse({ success: true }))
            .catch(error => {
              console.error('Error handling offers update:', error)
              sendResponse({ success: false, error: error.message })
            })
          return true

        case 'SETTINGS_UPDATE':
          handleSettingsUpdate(message.data)
            .then(() => sendResponse({ success: true }))
            .catch(error => {
              console.error('Error handling settings update:', error)
              sendResponse({ success: false, error: error.message })
            })
          return true

        case 'ACCEPT_OFFER':
          handleAcceptOffer(message.data?.offerId)
            .then(result => sendResponse({ success: true, result }))
            .catch(error => {
              console.error('Error handling accept offer:', error)
              sendResponse({ success: false, error: error.message })
            })
          return true

        case 'REJECT_OFFER':
          handleRejectOffer(message.data?.offerId)
            .then(result => sendResponse({ success: true, result }))
            .catch(error => {
              console.error('Error handling reject offer:', error)
              sendResponse({ success: false, error: error.message })
            })
          return true

        case 'OPERATION_DETAILS_UPDATE':
          handleOperationDetailsUpdate(message.data)
            .then(() => sendResponse({ success: true }))
            .catch(error => {
              console.error('Error handling operation details update:', error)
              sendResponse({ success: false, error: error.message })
            })
          return true

        case 'OPERATION_ACCEPTED':
          handleOperationAccepted(message.data)
            .then(() => sendResponse({ success: true }))
            .catch(error => {
              console.error('Error handling operation accepted:', error)
              sendResponse({ success: false, error: error.message })
            })
          return true

        case 'OPERATION_NOT_AVAILABLE':
          handleOperationNotAvailable(message.data)
            .then(() => sendResponse({ success: true }))
            .catch(error => {
              console.error('Error handling operation not available:', error)
              sendResponse({ success: false, error: error.message })
            })
          return true

        case 'OPERATION_ACCEPT_ERROR':
          handleOperationAcceptError(message.data)
            .then(() => sendResponse({ success: true }))
            .catch(error => {
              console.error('Error handling operation accept error:', error)
              sendResponse({ success: false, error: error.message })
            })
          return true

        case 'SEND_TELEGRAM_OPERATION_UPDATE':
          sendTelegramOperationStatusUpdate(message.data)
            .then(() => sendResponse({ success: true }))
            .catch(error => {
              console.error('Error sending Telegram operation update:', error)
              sendResponse({ success: false, error: error.message })
            })
          return true

        case 'OPERATION_DECLINED':
          handleOperationDeclined(message.data)
            .then(() => sendResponse({ success: true }))
            .catch(error => {
              console.error('Error handling operation declined:', error)
              sendResponse({ success: false, error: error.message })
            })
          return true

        case 'OPERATION_DECLINE_ERROR':
          handleOperationDeclineError(message.data)
            .then(() => sendResponse({ success: true }))
            .catch(error => {
              console.error('Error handling operation decline error:', error)
              sendResponse({ success: false, error: error.message })
            })
          return true

        case 'GET_POPUP_DATA':
          handleGetPopupData()
            .then(data => sendResponse({ success: true, data }))
            .catch(error => {
              console.error('Error handling get popup data:', error)
              sendResponse({ success: false, error: error.message })
            })
          return true

        case 'PING':
          console.log('Ping received, responding with alive status')
          sendResponse({ success: true, message: 'Background script is alive', timestamp: new Date().toISOString() })
          break

        default:
          console.warn('Unknown message type:', message.type)
          sendResponse({ success: false, error: 'Unknown message type: ' + message.type })
      }
    } catch (error) {
      console.error('Error in message listener:', error)
      sendResponse({ success: false, error: 'Internal error: ' + (error instanceof Error ? error.message : 'Unknown error') })
    }
  })

  // Add connection error handler
  chrome.runtime.onConnect.addListener((port) => {
    console.log('Port connected:', port.name)
    port.onDisconnect.addListener(() => {
      console.log('Port disconnected:', port.name)
      if (chrome.runtime.lastError) {
        console.error('Port disconnect error:', chrome.runtime.lastError)
      }
    })
  })

  // Setup Chrome commands handler
  if (chrome.commands && chrome.commands.onCommand) {
    chrome.commands.onCommand.addListener((command) => {
      console.log('Chrome command received:', command)
      
      // Send command to active Airtm tab
      chrome.tabs.query({ url: '*://app.airtm.com/peer-transfers/available*' }, (tabs) => {
        if (tabs.length > 0 && tabs[0].id) {
          chrome.tabs.sendMessage(tabs[0].id, {
            type: 'EXECUTE_COMMAND',
            command: command
          }).catch(error => {
            console.log('Could not send command to content script:', error)
          })
        } else {
          console.log('No active Airtm tab found for command:', command)
        }
      })
    })
  }
}

/**
 * Handle GET_POPUP_DATA request from popup
 */
async function handleGetPopupData(): Promise<{ offers: Offer[], settings: ExtensionSettings, stats: OfferStats }> {
  try {
    // Get current offers from storage
    const result = await chrome.storage.local.get('current_offers')
    const offers = result.current_offers || []
    
    console.log('Sending popup data:', {
      offersCount: offers.length,
      settings: currentSettings,
      stats: offerStats
    })
    
    return {
      offers,
      settings: currentSettings,
      stats: offerStats
    }
  } catch (error) {
    console.error('Error getting popup data:', error)
    throw error
  }
}

/**
 * Clean up old notified offers from memory
 */
function cleanupNotifiedOffers(): void {
  const now = Date.now()
  
  // Only cleanup if enough time has passed
  if (now - lastNotificationCleanup < NOTIFICATION_CLEANUP_INTERVAL) {
    return
  }
  
  // Remove offers older than NOTIFICATION_MEMORY_DURATION
  const cutoffTime = now - NOTIFICATION_MEMORY_DURATION
  let removedCount = 0
  
  for (const [offerId, timestamp] of notifiedOffers.entries()) {
    if (timestamp < cutoffTime) {
      notifiedOffers.delete(offerId)
      removedCount++
    }
  }
  
  lastNotificationCleanup = now
  
  if (removedCount > 0) {
    console.log(`🧹 Cleaned up ${removedCount} old notified offers (older than 1 hour)`)
    // Save changes to storage after cleanup
    saveNotifiedOffers()
  }
}

/**
 * Check if an offer has already been notified
 */
function isOfferAlreadyNotified(offerId: string): boolean {
  return notifiedOffers.has(offerId)
}

/**
 * Mark an offer as notified
 */
function markOfferAsNotified(offerId: string): void {
  notifiedOffers.set(offerId, Date.now())
  // Save to storage to persist between browser sessions
  saveNotifiedOffers()
}

/**
 * Handle offers update from content script
 */
async function handleOffersUpdate(data: any): Promise<void> {
  try {
    const { offers, timestamp } = data
    
    if (!Array.isArray(offers)) {
      throw new Error('Invalid offers data')
    }

    console.log('Processing ' + offers.length + ' offers')

    // Store current offers for popup access
    await chrome.storage.local.set({ current_offers: offers })

    // Clean up old notified offers periodically
    cleanupNotifiedOffers()

    // Check if monitoring is enabled
    if (!currentSettings.monitoring) {
      console.log('Monitoring disabled, skipping offer processing')
      // Still update statistics for UI display
      updateOfferStats(offers)
      offerStats.lastUpdate = timestamp || new Date().toISOString()
      
      // Notify popup of offers and stats even when monitoring is disabled
      try {
        chrome.runtime.sendMessage({
          type: 'OFFERS_PROCESSED',
          data: {
            offers: offers,
            stats: offerStats
          }
        }).catch(error => {
          // Popup might not be open, this is normal
          console.log('Could not notify popup (popup may be closed):', error.message)
        })
      } catch (error) {
        console.log('Error sending OFFERS_PROCESSED message:', error)
      }
      
      return
    }

    // Update statistics
    updateOfferStats(offers)

    // Apply filters
    const filteredOffers = applyFilters(offers)
    console.log(filteredOffers.length + ' offers passed filters')

    // Filter out already notified offers to prevent duplicates
    const newOffers = filteredOffers.filter(offer => !isOfferAlreadyNotified(offer.id))
    console.log(newOffers.length + ' new offers (not previously notified)')

    // Play sound notification once if there are new offers and sound is enabled
    if (newOffers.length > 0 && currentSettings.soundEnabled && currentSettings.notificationsEnabled) {
      console.log('Playing sound notification once for ' + newOffers.length + ' new offers')
      await playNotificationSound()
    }

    // Process each new offer (without playing sound for each one)
    for (const offer of newOffers) {
      // Mark offer as notified before processing
      markOfferAsNotified(offer.id)
      await processOffer(offer, false) // Pass false to skip playing sound again
    }

    // Update last update time
    offerStats.lastUpdate = timestamp || new Date().toISOString()

    // Notify popup of processed offers and updated stats
    try {
      chrome.runtime.sendMessage({
        type: 'OFFERS_PROCESSED',
        data: {
          offers: offers,
          stats: offerStats
        }
      }).catch(error => {
        // Popup might not be open, this is normal
        console.log('Could not notify popup (popup may be closed):', error.message)
      })
    } catch (error) {
      console.log('Error sending OFFERS_PROCESSED message:', error)
    }

  } catch (error) {
    console.error('Error handling offers update:', error)
  }
}

/**
 * Apply filters to offers
 */
function applyFilters(offers: Offer[]): Offer[] {
  return offers.filter(offer => {
    // Amount filter
    const grossAmount = typeof offer.grossAmount === 'string' ? parseFloat(offer.grossAmount) : offer.grossAmount
    if (grossAmount < currentSettings.minAmount || grossAmount > currentSettings.maxAmount) {
      return false
    }

    // Currency filter
    if (currentSettings.preferredCurrencies.length > 0) {
      const currencyCode = offer.currency?.symbol || offer.walletCurrency?.symbol || ''
      if (!currentSettings.preferredCurrencies.includes(currencyCode)) {
        return false
      }
    }

    // Payment method filter with fuzzy matching
    if (currentSettings.paymentMethods.length > 0) {
      const rawPaymentMethod = offer.makerPaymentMethod?.version?.category?.translationTag || ''
      
      if (currentSettings.fuzzyMatching.enabled) {
        // Use fuzzy matching
        const matchResult = paymentMethodMatcher.matchAnyPaymentMethod(
          rawPaymentMethod,
          currentSettings.paymentMethods
        )
        
        if (!matchResult.isMatch) {
          console.log(`Offer ${offer.id} filtered out: payment method "${rawPaymentMethod}" doesn't match any configured methods (best score: ${matchResult.score.toFixed(2)})`);
          return false
        } else {
          console.log(`Offer ${offer.id} matched payment method "${rawPaymentMethod}" with alias "${matchResult.matchedAlias}" (score: ${matchResult.score.toFixed(2)})`);
        }
      } else {
        // Use legacy exact matching
        let paymentMethod = rawPaymentMethod
        if (paymentMethod.startsWith('CATEGORY_TREE:AIRTM_')) {
          paymentMethod = paymentMethod.replace('CATEGORY_TREE:AIRTM_', '')
        }
        if (paymentMethod.startsWith('E_TRANSFER_')) {
          paymentMethod = paymentMethod.replace('E_TRANSFER_', '')
        }
        if (!currentSettings.paymentMethods.some(pm => paymentMethod.includes(pm))) {
          console.log(`Offer ${offer.id} filtered out: payment method "${paymentMethod}" doesn't match any configured methods (legacy matching)`);
          return false
        }
      }
    }

    // Country filter
    if (currentSettings.countries.length > 0) {
      const country = offer.peer?.country || ''
      if (!currentSettings.countries.includes(country)) {
        return false
      }
    }

    // Keywords filter
    if (currentSettings.keywords.length > 0) {
      const searchText = JSON.stringify(offer).toLowerCase()
      if (!currentSettings.keywords.some(keyword => searchText.includes(keyword.toLowerCase()))) {
        return false
      }
    }

    // Blacklist keywords filter
    if (currentSettings.blacklistKeywords.length > 0) {
      const searchText = JSON.stringify(offer).toLowerCase()
      if (currentSettings.blacklistKeywords.some(keyword => searchText.includes(keyword.toLowerCase()))) {
        console.log(`Offer ${offer.id} filtered out by blacklist keyword`)
        return false
      }
    }

    return true
  })
}

/**
 * Maximize Chrome window and focus on detected offer
 */
async function maximizeWindowAndFocusOffer(offer: Offer): Promise<void> {
  try {
    // Find Airtm tab
    const tabs = await chrome.tabs.query({ url: '*://app.airtm.com/peer-transfers/available*' })
    
    if (tabs.length === 0) {
      console.log('No Airtm tab found, opening new tab')
      // Create new tab if none exists
      const newTab = await chrome.tabs.create({
        url: 'https://app.airtm.com/peer-transfers/available',
        active: true
      })
      
      // Wait for tab to load
      await new Promise(resolve => setTimeout(resolve, 3000))
      
      // Get the window containing the new tab
      if (newTab.windowId) {
        try {
          await chrome.windows.update(newTab.windowId, {
            focused: true,
            state: 'maximized'
          })
          console.log('✅ New window maximized and focused')
        } catch (windowError) {
          console.warn('⚠️ Failed to maximize new window:', windowError)
          // Try alternative approach - just focus without maximizing
          try {
            await chrome.windows.update(newTab.windowId, { focused: true })
            console.log('✅ New window focused (without maximizing)')
          } catch (focusError) {
            console.error('❌ Failed to focus new window:', focusError)
          }
        }
      }
      
      return
    }

    const tab = tabs[0]
    
    // Get the window containing the Airtm tab
    if (tab.windowId) {
      // First, try to maximize and focus the window
      try {
        await chrome.windows.update(tab.windowId, {
          focused: true,
          state: 'maximized'
        })
        console.log('✅ Window maximized and focused for offer:', offer.id)
      } catch (windowError) {
        console.warn('⚠️ Failed to maximize window:', windowError)
        // Try alternative approach - just focus without maximizing
        try {
          await chrome.windows.update(tab.windowId, { focused: true })
          console.log('✅ Window focused (without maximizing) for offer:', offer.id)
        } catch (focusError) {
          console.error('❌ Failed to focus window:', focusError)
          // Continue anyway to try tab activation
        }
      }
      
      // Activate the tab with retry logic
      let tabActivated = false
      for (let attempt = 1; attempt <= 3; attempt++) {
        try {
          await chrome.tabs.update(tab.id!, {
            active: true
          })
          console.log(`✅ Tab activated (attempt ${attempt}) for offer:`, offer.id)
          tabActivated = true
          break
        } catch (tabError) {
          console.warn(`⚠️ Tab activation attempt ${attempt} failed:`, tabError)
          if (attempt < 3) {
            // Wait before retry
            await new Promise(resolve => setTimeout(resolve, 500))
          }
        }
      }
      
      if (!tabActivated) {
        console.error('❌ Failed to activate tab after 3 attempts')
      }
      
      // Send message to content script to scroll to and highlight the offer
      // Add delay to ensure window is focused before sending message
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      try {
        await chrome.tabs.sendMessage(tab.id!, {
          type: 'FOCUS_OFFER',
          data: { offerId: offer.id }
        })
        console.log('✅ Focus message sent to content script for offer:', offer.id)
      } catch (messageError) {
        console.log('⚠️ Could not send focus message to content script:', messageError)
        // This is not critical, continue without throwing
      }
    } else {
      console.error('❌ No window ID found for Airtm tab')
    }
    
  } catch (error) {
    console.error('❌ Error maximizing window and focusing offer:', error)
    // Don't throw the error to prevent breaking the offer processing pipeline
  }
}

/**
 * Process individual offer
 * @param offer The offer to process
 * @param playSound Whether to play sound notification (defaults to true)
 */
async function processOffer(offer: Offer, playSound: boolean = true): Promise<void> {
  try {
    console.log('Processing offer ' + offer.id)

    // Maximize Chrome window and focus on the offer
    await maximizeWindowAndFocusOffer(offer)

    // Send notifications
    if (currentSettings.notificationsEnabled) {
      await sendNotification(offer, playSound)
    }

    // Send Telegram message
    console.log('Checking Telegram settings:', {
      botToken: currentSettings.telegramBotToken ? '***SET***' : 'NOT SET',
      chatId: currentSettings.telegramChatId ? '***SET***' : 'NOT SET'
    })
    
    if (currentSettings.telegramBotToken && currentSettings.telegramChatId) {
      console.log('Sending Telegram message for offer:', offer.id)
      await sendTelegramMessage(offer)
    } else {
      console.log('Telegram not configured - skipping message')
    }

    // Send webhook message
    if (currentSettings.webhookUrl) {
      console.log('Sending webhook message for offer:', offer.id)
      await sendWebhookMessage(offer)
    } else {
      console.log('Webhook not configured - skipping webhook')
    }

    // Auto-accept if enabled
    if (currentSettings.autoAccept) {
      await handleAcceptOffer(offer.id)
    }

  } catch (error) {
    console.error('Error processing offer ' + offer.id + ':', error)
  }
}

/**
 * Send Chrome notification
 * @param offer The offer to send notification for
 * @param playSound Whether to play sound notification (defaults to true)
 */
async function sendNotification(offer: Offer, playSound: boolean = true): Promise<void> {
  try {
    // Update notification manager settings based on current settings
    notificationManager.updateSettings({
      // Note: Overlay notifications feature removed - using Chrome notifications only
      autoCloseDelay: 30000,
      // Only play sound if both global setting is enabled and playSound parameter is true
      playSound: currentSettings.soundEnabled && playSound
    })

    // Show notification using the manager
    await notificationManager.showNotification(offer)

  } catch (error) {
    console.error('Error sending notification:', error)
    
    // Fallback to basic Chrome notification with currency conversion
    try {
      const conversion = convertCurrencyAmount(offer)
      const title = 'New ' + offer.operationType + ' Offer'

      // Build amount display for notification
      let amountDisplay = `${conversion.amount} ${conversion.currency}`
      if (conversion.conversionNote) {
        amountDisplay += ` ${conversion.conversionNote}`
      }

      await chrome.notifications.create({
        type: 'basic',
        iconUrl: 'icons/icon48.png',
        title,
        message: amountDisplay,
        contextMessage: 'From: ' + (offer.peer?.firstName || '') + ' ' + (offer.peer?.lastName || ''),
        buttons: [
          { title: 'Accept' },
          { title: 'Reject' }
        ]
      })

      // Play sound if enabled and playSound parameter is true
      if (currentSettings.soundEnabled && playSound) {
        await playNotificationSound()
      }
    } catch (fallbackError) {
      console.error('Fallback notification also failed:', fallbackError)
    }
  }
}

/**
 * Send Telegram message
 */
// Overloaded function for sending Telegram messages
async function sendTelegramMessage(offer: Offer): Promise<void>
async function sendTelegramMessage(message: string): Promise<void>
async function sendTelegramMessage(input: Offer | string): Promise<void> {
  try {
    const message = typeof input === 'string' ? input : formatTelegramMessage(input)
    const url = 'https://api.telegram.org/bot' + currentSettings.telegramBotToken + '/sendMessage'
    
    console.log('Sending Telegram message to:', currentSettings.telegramChatId)
    console.log('Message content:', message)

    const response = await fetch(url, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        chat_id: currentSettings.telegramChatId,
        text: message,
        parse_mode: 'Markdown'
      })
    })
    
    const responseData = await response.json()
    
    if (response.ok) {
      console.log('✅ Telegram message sent successfully:', responseData)
    } else {
      console.error('❌ Telegram API error:', responseData)
    }

  } catch (error) {
    console.error('❌ Error sending Telegram message:', error)
  }
}

/**
 * Send webhook message
 */
async function sendWebhookMessage(offer: Offer): Promise<void> {
  try {
    const webhookData = {
      timestamp: new Date().toISOString(),
      offer: offer,
      event: 'new_offer',
      metadata: {
        extensionVersion: '1.0.0',
        source: 'airtm-monitor-pro'
      }
    }
    
    console.log('Sending webhook to:', currentSettings.webhookUrl)
    console.log('Webhook payload:', webhookData)

    const response = await fetch(currentSettings.webhookUrl, {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'User-Agent': 'Airtm-Monitor-Pro/1.0.0'
      },
      body: JSON.stringify(webhookData)
    })
    
    if (response.ok) {
      console.log('✅ Webhook sent successfully:', response.status)
    } else {
      console.error('❌ Webhook failed with status:', response.status, await response.text())
    }

  } catch (error) {
    console.error('❌ Error sending webhook:', error)
  }
}

/**
 * Get country flag emoji from country code
 */
function getCountryFlag(countryCode: string): string {
  const flagMap: { [key: string]: string } = {
    'USA': '🇺🇸', 'US': '🇺🇸',
    'IND': '🇮🇳', 'IN': '🇮🇳',
    'GBR': '🇬🇧', 'GB': '🇬🇧', 'UK': '🇬🇧',
    'CAN': '🇨🇦', 'CA': '🇨🇦',
    'AUS': '🇦🇺', 'AU': '🇦🇺',
    'DEU': '🇩🇪', 'DE': '🇩🇪', 'GER': '🇩🇪',
    'FRA': '🇫🇷', 'FR': '🇫🇷',
    'ESP': '🇪🇸', 'ES': '🇪🇸',
    'ITA': '🇮🇹', 'IT': '🇮🇹',
    'BRA': '🇧🇷', 'BR': '🇧🇷',
    'MEX': '🇲🇽', 'MX': '🇲🇽',
    'ARG': '🇦🇷', 'AR': '🇦🇷',
    'COL': '🇨🇴', 'CO': '🇨🇴',
    'VEN': '🇻🇪', 'VE': '🇻🇪',
    'PER': '🇵🇪', 'PE': '🇵🇪',
    'CHL': '🇨🇱', 'CL': '🇨🇱',
    'URY': '🇺🇾', 'UY': '🇺🇾',
    'ECU': '🇪🇨', 'EC': '🇪🇨',
    'BOL': '🇧🇴', 'BO': '🇧🇴',
    'PRY': '🇵🇾', 'PY': '🇵🇾',
    'CHN': '🇨🇳', 'CN': '🇨🇳',
    'JPN': '🇯🇵', 'JP': '🇯🇵',
    'KOR': '🇰🇷', 'KR': '🇰🇷',
    'RUS': '🇷🇺', 'RU': '🇷🇺',
    'TUR': '🇹🇷', 'TR': '🇹🇷',
    'EGY': '🇪🇬', 'EG': '🇪🇬',
    'ZAF': '🇿🇦', 'ZA': '🇿🇦',
    'NGA': '🇳🇬', 'NG': '🇳🇬',
    'KEN': '🇰🇪', 'KE': '🇰🇪',
    'GHA': '🇬🇭', 'GH': '🇬🇭',
    'MAR': '🇲🇦', 'MA': '🇲🇦',
    'TUN': '🇹🇳', 'TN': '🇹🇳',
    'DZA': '🇩🇿', 'DZ': '🇩🇿',
    'MOZ': '🇲🇿', 'MZ': '🇲🇿',  // Mozambique
    'PAN': '🇵🇦', 'PA': '🇵🇦',  // Panama
    'IDN': '🇮🇩', 'ID': '🇮🇩',
    'THA': '🇹🇭', 'TH': '🇹🇭',
    'VNM': '🇻🇳', 'VN': '🇻🇳',
    'PHL': '🇵🇭', 'PH': '🇵🇭',
    'MYS': '🇲🇾', 'MY': '🇲🇾',
    'SGP': '🇸🇬', 'SG': '🇸🇬',
    'PAK': '🇵🇰', 'PK': '🇵🇰',
    'BGD': '🇧🇩', 'BD': '🇧🇩',
    'LKA': '🇱🇰', 'LK': '🇱🇰',
    'NPL': '🇳🇵', 'NP': '🇳🇵',
    'AFG': '🇦🇫', 'AF': '🇦🇫',
    'IRN': '🇮🇷', 'IR': '🇮🇷',
    'IRQ': '🇮🇶', 'IQ': '🇮🇶',
    'SAU': '🇸🇦', 'SA': '🇸🇦',
    'ARE': '🇦🇪', 'AE': '🇦🇪',
    'QAT': '🇶🇦', 'QA': '🇶🇦',
    'KWT': '🇰🇼', 'KW': '🇰🇼',
    'BHR': '🇧🇭', 'BH': '🇧🇭',
    'OMN': '🇴🇲', 'OM': '🇴🇲',
    'JOR': '🇯🇴', 'JO': '🇯🇴',
    'LBN': '🇱🇧', 'LB': '🇱🇧',
    'SYR': '🇸🇾', 'SY': '🇸🇾',
    'ISR': '🇮🇱', 'IL': '🇮🇱',
    'PSE': '🇵🇸', 'PS': '🇵🇸'
  }
  
  return flagMap[countryCode.toUpperCase()] || countryCode
}

/**
 * Currency conversion utility for multi-currency operations
 */
interface CurrencyConversionResult {
  amount: string
  currency: string
  originalAmount?: string
  originalCurrency?: string
  exchangeRate?: string
  conversionNote?: string
  operationType?: 'withdrawal' | 'deposit' | 'exchange' | null
}

/**
 * Enhanced USDC detection logic
 */
function isUSDCOperation(offer: Offer): { isUSDC: boolean; usdcField: 'wallet' | 'local' | null; operationType: 'withdrawal' | 'deposit' | 'exchange' | null } {
  const walletCurrency = offer.walletCurrency
  const localCurrency = offer.currency
  const paymentMethod = offer.makerPaymentMethod || offer.takerPaymentMethod

  // Check for USDC indicators
  const hasUSDCPaymentMethod = paymentMethod?.categoryId?.toLowerCase().includes('usdc') ||
                              paymentMethod?.version?.category?.translationTag?.toLowerCase().includes('usdc')

  // Check precision (USDC typically uses 6 decimals, fiat uses 2)
  const walletPrecision = offer.metadata?.walletCurrencyPrecision || walletCurrency?.precision
  const localPrecision = offer.metadata?.localCurrencyPrecision || localCurrency?.precision

  const hasUSDCPrecision = walletPrecision === 6 || localPrecision === 6

  // Check for internal Airtm operations (likely USDC)
  const isInternalOperation = offer.metadata?.isForThirdPartyPaymentMethod === false

  // Enhanced withdrawal-like operation detection
  const categoryId = paymentMethod?.categoryId?.toLowerCase() || ''
  const translationTag = paymentMethod?.version?.category?.translationTag?.toLowerCase() || ''

  const isGiftCard = categoryId.includes('gift-card') || translationTag.includes('gift_card')
  const isBankTransfer = categoryId.includes('bank') || translationTag.includes('bank')
  const isCardPayment = categoryId.includes('card') || translationTag.includes('card')
  const isExternalTransfer = categoryId.includes('transfer') || translationTag.includes('transfer')
  const isExternalService = isGiftCard || isBankTransfer || isCardPayment || isExternalTransfer

  // Determine operation type
  let operationType: 'withdrawal' | 'deposit' | 'exchange' | null = null
  let usdcField: 'wallet' | 'local' | null = null

  // Check for external service operations first
  if (offer.operationType === 'BUY' && isExternalService) {
    // BUY operations for external services = withdrawals (spending wallet money)
    operationType = 'withdrawal'
    usdcField = 'wallet'  // Money comes from wallet
    return { isUSDC: false, usdcField, operationType }
  } else if (offer.operationType === 'SELL' && isExternalService) {
    // SELL operations for external services = deposits (receiving money to wallet)
    operationType = 'deposit'
    usdcField = 'wallet'  // Money goes to wallet
    return { isUSDC: false, usdcField, operationType }
  } else if (hasUSDCPaymentMethod || hasUSDCPrecision || isInternalOperation) {
    if (offer.operationType === 'SELL' && walletPrecision === 6) {
      // SELL with USDC wallet = withdrawal (USDC → external currency)
      operationType = 'withdrawal'
      usdcField = 'wallet'
    } else if (offer.operationType === 'BUY' && localPrecision === 6) {
      // BUY with USDC local = deposit (external currency → USDC)
      operationType = 'deposit'
      usdcField = 'local'
    } else if (walletPrecision === 6 || localPrecision === 6) {
      // Exchange involving USDC
      operationType = 'exchange'
      usdcField = walletPrecision === 6 ? 'wallet' : 'local'
    }

    return { isUSDC: true, usdcField, operationType }
  }

  return { isUSDC: false, usdcField: null, operationType: null }
}

function convertCurrencyAmount(offer: Offer): CurrencyConversionResult {
  try {
    // Enhanced USDC detection
    const usdcInfo = isUSDCOperation(offer)

    // Extract currency information with USDC context
    let walletCurrency = offer.walletCurrency?.symbol || '$'
    let localCurrency = offer.currency?.symbol || offer.walletCurrency?.symbol || 'Unknown'

    // Apply USDC labeling
    if (usdcInfo.isUSDC) {
      if (usdcInfo.usdcField === 'wallet') {
        walletCurrency = 'USDC'
      } else if (usdcInfo.usdcField === 'local') {
        localCurrency = 'USDC'
      }
    }

    // Handle same currency with USDC context
    if ((walletCurrency === localCurrency || !offer.currency) && !usdcInfo.isUSDC) {
      const amount = parseFloat(offer.grossAmount || '0')
      return {
        amount: formatAmount(amount),
        currency: walletCurrency
      }
    }

    // Check if we have rateInfo with actual transaction amounts (post-acceptance data)
    const rateInfo = offer.rateInfo as any
    if (rateInfo && rateInfo.fundsToSendTaker && rateInfo.fundsToReceiveTaker) {
      // Handle BUY vs SELL operations with USDC context
      let localAmount: number
      let walletAmount: number
      let conversionNote = ''

      if (offer.operationType === 'BUY') {
        // BUY: User buys wallet currency by paying local currency
        localAmount = parseFloat(rateInfo.fundsToReceiveTaker)  // Amount user pays (local currency)
        walletAmount = parseFloat(rateInfo.fundsToSendTaker)    // Amount user receives (wallet currency)

        if (usdcInfo.operationType === 'deposit') {
          conversionNote = '(USDC deposit)'
        }
      } else {
        // SELL: User sells wallet currency to receive local currency
        localAmount = parseFloat(rateInfo.fundsToSendTaker)     // Amount user receives (local currency)
        walletAmount = parseFloat(rateInfo.fundsToReceiveTaker) // Amount user sends (wallet currency)

        if (usdcInfo.operationType === 'withdrawal') {
          // For withdrawals, show the deduction and final amount
          const totalFees = walletAmount - localAmount
          conversionNote = `(withdrawal, fees: $${totalFees.toFixed(2)})`
        }
      }

      console.log(`Using rateInfo amounts for ${offer.operationType}: ${walletAmount} ${walletCurrency} ↔ ${localAmount} ${localCurrency} (from rateInfo)${usdcInfo.isUSDC ? ' [USDC operation]' : ''}`)

      // For withdrawal operations, prioritize showing the USDC deduction
      if (usdcInfo.operationType === 'withdrawal') {
        return {
          amount: formatAmount(walletAmount),
          currency: walletCurrency,
          originalAmount: formatAmount(localAmount),
          originalCurrency: localCurrency,
          exchangeRate: (localAmount / walletAmount).toFixed(4),
          conversionNote,
          operationType: 'withdrawal'
        }
      }

      // For other operations, show local currency as primary (what user pays/receives)
      return {
        amount: formatAmount(localAmount),
        currency: localCurrency,
        originalAmount: formatAmount(walletAmount),
        originalCurrency: walletCurrency,
        exchangeRate: (localAmount / walletAmount).toFixed(4),
        conversionNote,
        operationType: usdcInfo.operationType
      }
    }

    // Fallback to rate-based conversion for pre-acceptance offers
    let walletAmount: number
    let exchangeRate: number | null = null
    let rateSource = ''

    // Determine which wallet amount to use
    if (offer.netAmount && parseFloat(offer.netAmount) > 0) {
      walletAmount = parseFloat(offer.netAmount)  // Amount after fees
    } else {
      walletAmount = parseFloat(offer.grossAmount || '0')  // Gross amount
    }

    // Extract exchange rate from available sources with enhanced schema support
    // Priority order per currency conversion.txt requirements:
    // 1. operation.rate (primary - from AvailableOperations)
    // 2. operation.displayRate.rate (secondary)
    // 3. operation.rateInfo.exchangeRate (fallback)
    // 4. metadata.displayRateInfo.exchangeRate (last resort)
    // 5. takerPaymentMethod rate info (new schema field)
    const metadata = offer.metadata as any
    const takerPaymentMethod = offer.takerPaymentMethod as any

    if (offer.rate) {
      // Primary: From afteraccepting.txt: rate: "49.57"
      exchangeRate = parseFloat(offer.rate)
      rateSource = 'operation.rate'
    } else if (offer.displayRate?.rate) {
      // Secondary: From afteraccepting.txt: displayRate.rate: "47.42396313364055299539"
      exchangeRate = parseFloat(offer.displayRate.rate)
      rateSource = 'displayRate.rate'
    } else if (rateInfo && rateInfo.exchangeRate) {
      // Fallback: Legacy rateInfo.exchangeRate
      exchangeRate = parseFloat(rateInfo.exchangeRate)
      rateSource = 'rateInfo.exchangeRate'
    } else if (metadata?.displayRateInfo?.exchangeRate) {
      // Last resort: From afteraccepting.txt: metadata.displayRateInfo.exchangeRate: "49.5709"
      exchangeRate = parseFloat(metadata.displayRateInfo.exchangeRate)
      rateSource = 'metadata.displayRateInfo.exchangeRate'
    } else if (takerPaymentMethod?.rateInfo?.exchangeRate) {
      // New schema field: takerPaymentMethod rate information
      exchangeRate = parseFloat(takerPaymentMethod.rateInfo.exchangeRate)
      rateSource = 'takerPaymentMethod.rateInfo.exchangeRate'
    }

    // For USDC operations near parity, use 1.0 if no rate found
    if ((!exchangeRate || exchangeRate <= 0) && usdcInfo.isUSDC) {
      exchangeRate = 1.0
      rateSource = 'usdc_parity_default'
    }

    // If no exchange rate found, fall back to wallet currency
    if (!exchangeRate || exchangeRate <= 0) {
      return {
        amount: formatAmount(walletAmount),
        currency: walletCurrency,
        conversionNote: '(rate pending)'
      }
    }

    // Determine conversion direction and calculate local amount
    let localAmount: number
    const direction = offer.displayRate?.direction || 'TO_LOCAL_CURRENCY'

    if (direction === 'TO_LOCAL_CURRENCY') {
      // Convert from wallet currency to local currency
      localAmount = walletAmount * exchangeRate
    } else if (direction === 'FROM_LOCAL_CURRENCY') {
      // Convert from local currency to wallet currency (reverse)
      localAmount = walletAmount / exchangeRate
    } else {
      // Default behavior based on operation type
      if (offer.operationType === 'SELL') {
        // SELL: User sells wallet currency, receives local currency
        localAmount = walletAmount * exchangeRate
      } else {
        // BUY: User buys wallet currency with local currency
        localAmount = walletAmount * exchangeRate
      }
    }

    console.log(`Currency conversion: ${walletAmount} ${walletCurrency} → ${localAmount} ${localCurrency} (rate: ${exchangeRate}, source: ${rateSource}, direction: ${direction})${usdcInfo.isUSDC ? ' [USDC operation]' : ''}`)

    // Enhanced return with USDC context
    const result: CurrencyConversionResult = {
      amount: formatAmount(localAmount),
      currency: localCurrency,
      originalAmount: formatAmount(walletAmount),
      originalCurrency: walletCurrency,
      exchangeRate: exchangeRate.toString()
    }

    // Add USDC-specific context
    if (usdcInfo.isUSDC) {
      result.operationType = usdcInfo.operationType

      if (usdcInfo.operationType === 'withdrawal') {
        // For withdrawals, show USDC deduction as primary
        result.amount = formatAmount(walletAmount)
        result.currency = walletCurrency
        result.originalAmount = formatAmount(localAmount)
        result.originalCurrency = localCurrency
        result.conversionNote = `(withdrawal to ${localCurrency})`
      } else if (usdcInfo.operationType === 'deposit') {
        result.conversionNote = `(deposit from ${walletCurrency})`
      } else if (usdcInfo.operationType === 'exchange') {
        result.conversionNote = '(USDC exchange)'
      }
    }

    return result

  } catch (error) {
    console.error('Error in currency conversion:', error)

    // Fallback to original amount
    const fallbackAmount = parseFloat(offer.grossAmount || '0')
    const fallbackCurrency = offer.currency?.symbol || offer.walletCurrency?.symbol || 'Unknown'

    return {
      amount: formatAmount(fallbackAmount),
      currency: fallbackCurrency,
      conversionNote: '(conversion error)'
    }
  }
}

/**
 * Format amount with proper decimal places and thousand separators
 */
function formatAmount(amount: number): string {
  if (amount >= 1000) {
    return amount.toLocaleString('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    })
  } else {
    return amount.toFixed(2).replace(/\.?0+$/, '')
  }
}

/**
 * Test currency conversion with afteraccepting.txt example
 * This function verifies the conversion logic with real data
 */
function testCurrencyConversion(): void {
  console.log('🧪 Testing currency conversion with afteraccepting.txt example...')

  // Create test offer based on afteraccepting.txt data
  const testOffer: Offer = ({
    id: '0d77a20b-f2ab-4dd6-8924-9fd102a37652',
    hash: 'F2ABQP4DD6DK8924',
    operationType: 'SELL',
    status: 'ACCEPTED',
    isMine: false,
    createdAt: '2025-06-12T16:23:51.645Z',
    updatedAt: '2025-06-12T16:24:00.386Z',
    grossAmount: '11',           // Wallet currency gross amount
    netAmount: '10.38',          // Wallet currency net amount
    metadata: {
      isForThirdPartyPaymentMethod: true,
      localCurrencyPrecision: 2,
      walletCurrencyPrecision: 2
    },
    walletCurrency: {
      symbol: '$',
      id: 'USD',
      name: 'US Dollar',
      precision: 2,
      __typename: 'Catalogs__Currency'
    },
    currency: {
      symbol: '£',
      id: 'EGP',
      name: 'Egyptian Pound',
      precision: 2,
      __typename: 'Catalogs__Currency'
    },
    peer: {
      id: '90e670af-de7f-45ba-8de2-e2698558271d',
      firstName: 'احمد',
      lastName: 'محمد السيد عبدالحافظ',
      createdAt: '2022-04-23T18:23:27.468Z',
      country: 'EGY',
      countryInfo: {
        id: 'EGY',
        __typename: 'Catalogs__Country'
      },
      numbers: {
        id: '90e670af-de7f-45ba-8de2-e2698558271d',
        score: 4.98,
        completedOperations: 162,
        __typename: 'Numbers__User'
      },
      __typename: 'Auth__OperationUser'
    },

    // Rate information from afteraccepting.txt
    rate: '49.57',
    displayRate: {
      rate: '47.42396313364055299539',
      direction: 'TO_LOCAL_CURRENCY',
      __typename: 'Operations__DisplayRate'
    },
    rateInfo: {
      fundsToSendTaker: '514.55',      // Local currency amount (EGP)
      fundsToReceiveTaker: '10.85',    // Wallet currency amount (USD)
      grossAmount: '545.28',           // Local currency gross
      netAmount: '514.55',             // Local currency net
      exchangeRate: '49.5709'          // From metadata.displayRateInfo
    } as any,

    __typename: 'Operations__Sell'
  } as unknown) as Offer

  // Test the conversion
  const result = convertCurrencyAmount(testOffer)

  console.log('📊 Currency Conversion Test Results:')
  console.log(`   Input: ${testOffer.grossAmount} ${testOffer.walletCurrency?.symbol} (gross)`)
  console.log(`   Input: ${testOffer.netAmount} ${testOffer.walletCurrency?.symbol} (net)`)
  console.log(`   Expected Local: 514.55 £`)
  console.log(`   Expected Wallet: 10.85 $`)
  console.log(`   Actual Result: ${result.amount} ${result.currency}`)
  console.log(`   Original Amount: ${result.originalAmount} ${result.originalCurrency}`)
  console.log(`   Exchange Rate: ${result.exchangeRate}`)
  console.log(`   Conversion Note: ${result.conversionNote || 'None'}`)

  // Verify the result
  const expectedLocal = 514.55
  const actualLocal = parseFloat(result.amount.replace(/,/g, ''))
  const isCorrect = Math.abs(actualLocal - expectedLocal) < 0.01

  console.log(`✅ Test Result: ${isCorrect ? 'PASSED' : 'FAILED'}`)
  if (!isCorrect) {
    console.log(`   Expected: ${expectedLocal}, Got: ${actualLocal}`)
  }

  // Test Telegram message format
  const telegramMessage = formatTelegramMessage(testOffer)
  console.log('📱 Telegram Message:')
  console.log(telegramMessage)

  // Test same-currency scenario (USD to USD)
  console.log('\n🧪 Testing same-currency scenario (USD to USD)...')
  const sameCurrencyOffer: Offer = ({
    id: '055c2bee-d063-4ced-acac-27b623954fa5',
    hash: 'D063YU4CEDVGACAC',
    operationType: 'SELL',
    status: 'CREATED',
    isMine: false,
    createdAt: '2025-06-14T13:19:58.291Z',
    updatedAt: null,
    grossAmount: '62.16',
    netAmount: '58.87',
    metadata: {
      isForThirdPartyPaymentMethod: true,
      localCurrencyPrecision: 2,
      walletCurrencyPrecision: 2
    },
    walletCurrency: {
      symbol: '$',
      id: 'USD',
      name: 'US Dollar',
      precision: 2,
      __typename: 'Catalogs__Currency'
    },
    currency: {
      symbol: '$',  // Same as wallet currency
      id: 'USD',
      name: 'US Dollar',
      precision: 2,
      __typename: 'Catalogs__Currency'
    },
    peer: {
      id: '8021567e-1d57-446e-ae65-ffe04f622241',
      firstName: 'IRIS DANIELA',
      lastName: 'S.',
      createdAt: '2025-03-17T00:20:06.523Z',
      country: 'PAN',
      countryInfo: {
        id: 'PAN',
        __typename: 'Catalogs__Country'
      },
      numbers: {
        id: '8021567e-1d57-446e-ae65-ffe04f622241',
        score: 4.47,
        completedOperations: 32,
        __typename: 'Numbers__User'
      },
      __typename: 'Auth__OperationUser'
    },
    rate: '1',
    rateInfo: {
      fundsToReceiveTaker: '61.23',
      fundsToSendTaker: '58.86',
    } as any,
    displayRate: {
      direction: 'TO_WALLET_CURRENCY',
      rate: '1.04026503567787971458',
      __typename: 'Operations__DisplayRate'
    },
    makerPaymentMethod: {
      version: {
        category: {
          translationTag: 'CATEGORY_TREE:AIRTM_MOBILE_YAPPY'
        }
      }
    } as any,
    __typename: 'Operations__Sell'
  } as unknown) as Offer

  const sameCurrencyResult = convertCurrencyAmount(sameCurrencyOffer)
  console.log('📊 Same Currency Test Results:')
  console.log(`   Expected: 62.16 $ (no conversion)`)
  console.log(`   Actual Result: ${sameCurrencyResult.amount} ${sameCurrencyResult.currency}`)
  console.log(`   Original Amount: ${sameCurrencyResult.originalAmount || 'None'}`)
  console.log(`   Conversion Note: ${sameCurrencyResult.conversionNote || 'None'}`)

  const sameCurrencyMessage = formatTelegramMessage(sameCurrencyOffer)
  console.log('📱 Same Currency Telegram Message:')
  console.log(sameCurrencyMessage)

  // Test BUY operation scenario (USD wallet ← CNY local)
  console.log('\n🧪 Testing BUY operation scenario (CNY to USD)...')
  const buyOperationOffer: Offer = ({
    id: '8d0a8483-3ea0-43b9-9a93-c48f22abe919',
    hash: '3EA0YE43B9EC9A93',
    operationType: 'BUY',
    status: 'CREATED',
    isMine: false,
    createdAt: '2025-06-14T13:49:43.184Z',
    updatedAt: null,
    grossAmount: '278.97',
    netAmount: '269.84',
    metadata: {
      isForThirdPartyPaymentMethod: null,
      localCurrencyPrecision: 2,
      walletCurrencyPrecision: 2
    },
    walletCurrency: {
      symbol: '$',
      id: 'USD',
      name: 'US Dollar',
      precision: 2,
      __typename: 'Catalogs__Currency'
    },
    currency: {
      symbol: '¥',
      id: 'CNY',
      name: 'Chinese Yuan',
      precision: 2,
      __typename: 'Catalogs__Currency'
    },
    peer: {
      id: 'd37923bd-23c8-4f2c-99fc-15c665e090ea',
      firstName: 'ALY ADRIANO',
      lastName: 'S.',
      createdAt: '2025-02-11T17:11:19.413Z',
      country: 'MOZ',
      countryInfo: {
        id: 'MOZ',
        __typename: 'Catalogs__Country'
      },
      numbers: {
        id: 'd37923bd-23c8-4f2c-99fc-15c665e090ea',
        score: 4.28,
        completedOperations: 82,
        __typename: 'Numbers__User'
      },
      __typename: 'Auth__OperationUser'
    },
    rate: '7.1692',
    rateInfo: {
      fundsToReceiveTaker: '2000',     // CNY amount user pays
      fundsToSendTaker: '273.75',      // USD amount user receives
      netAmount: '1934.55',
      grossAmount: '2000'
    } as any,
    displayRate: {
      direction: 'TO_LOCAL_CURRENCY',
      rate: '7.30593607305936073059',
      __typename: 'Operations__DisplayRate'
    },
    makerPaymentMethod: {
      version: {
        category: {
          translationTag: 'CATEGORY_TREE:AIRTM_E_TRANSFER_ALIPAY'
        }
      }
    } as any,
    __typename: 'Operations__Buy'
  } as unknown) as Offer

  const buyOperationResult = convertCurrencyAmount(buyOperationOffer)
  console.log('📊 BUY Operation Test Results:')
  console.log(`   Expected: 2,000 ¥ (273.75 $)`)
  console.log(`   Actual Result: ${buyOperationResult.amount} ${buyOperationResult.currency}`)
  console.log(`   Original Amount: ${buyOperationResult.originalAmount} ${buyOperationResult.originalCurrency}`)
  console.log(`   Exchange Rate: ${buyOperationResult.exchangeRate}`)

  const buyOperationMessage = formatTelegramMessage(buyOperationOffer)
  console.log('📱 BUY Operation Telegram Message:')
  console.log(buyOperationMessage)

  // Test USDC withdrawal scenario
  console.log('\n🧪 Testing USDC withdrawal scenario...')
  const usdcWithdrawalOffer: Offer = ({
    id: 'withdrawal-001',
    hash: 'WITHDRAWAL001',
    operationType: 'SELL',
    status: 'CREATED',
    isMine: true,
    createdAt: '2025-06-14T15:30:00.000Z',
    updatedAt: null,
    grossAmount: '14.23',
    netAmount: '13.85',
    metadata: {
      isForThirdPartyPaymentMethod: false,
      walletCurrencyPrecision: 6,    // USDC precision
      localCurrencyPrecision: 2      // USD precision
    },
    walletCurrency: {
      symbol: '$',
      id: 'USD',
      name: 'US Dollar',
      precision: 6,
      __typename: 'Catalogs__Currency'
    },
    currency: {
      symbol: '$',
      id: 'USD',
      name: 'US Dollar',
      precision: 2,
      __typename: 'Catalogs__Currency'
    },
    peer: {
      id: 'withdrawal-peer',
      firstName: 'Bank',
      lastName: 'Withdrawal',
      createdAt: '2025-01-01T00:00:00.000Z',
      country: 'USA',
      countryInfo: {
        id: 'USA',
        __typename: 'Catalogs__Country'
      },
      numbers: {
        id: 'withdrawal-peer',
        score: 5.0,
        completedOperations: 1000,
        __typename: 'Numbers__User'
      },
      __typename: 'Auth__OperationUser'
    },
    rate: '1.0',
    rateInfo: {
      fundsToReceiveTaker: '14.23',    // USDC debited from wallet
      fundsToSendTaker: '13.50',       // USD sent to bank
      grossAmount: '14.23',
      netAmount: '13.85'
    } as any,
    displayRate: {
      direction: 'TO_LOCAL_CURRENCY',
      rate: '1.0',
      __typename: 'Operations__DisplayRate'
    },
    makerPaymentMethod: {
      categoryId: 'airtm:bank:withdrawal',
      version: {
        category: {
          translationTag: 'CATEGORY_TREE:AIRTM_BANK_WITHDRAWAL'
        }
      }
    } as any,
    __typename: 'Operations__Sell'
  } as unknown) as Offer

  const usdcWithdrawalResult = convertCurrencyAmount(usdcWithdrawalOffer)
  console.log('📊 USDC Withdrawal Test Results:')
  console.log(`   Expected: -14.23 USDC → 13.50 USD (fees: $0.73)`)
  console.log(`   Actual Result: ${usdcWithdrawalResult.amount} ${usdcWithdrawalResult.currency}`)
  console.log(`   Original Amount: ${usdcWithdrawalResult.originalAmount} ${usdcWithdrawalResult.originalCurrency}`)
  console.log(`   Operation Type: ${usdcWithdrawalResult.operationType}`)
  console.log(`   Conversion Note: ${usdcWithdrawalResult.conversionNote || 'None'}`)

  const usdcWithdrawalMessage = formatTelegramMessage(usdcWithdrawalOffer)
  console.log('📱 USDC Withdrawal Telegram Message:')
  console.log(usdcWithdrawalMessage)

  console.log('\n🧪 Currency conversion tests completed.')
}

/**
 * Format offer for Telegram message with rich visual details and proper currency conversion
 */
function formatTelegramMessage(offer: Offer): string {
  // Get converted currency amount with USDC detection
  const conversion = convertCurrencyAmount(offer)
  const usdcInfo = isUSDCOperation(offer)

  // Extract visual elements
  const serviceLogo = offer.makerPaymentMethod?.version?.image?.urls?.logo ||
                     offer.makerPaymentMethod?.version?.image?.urls?.medium || ''
  const peerAvatar = offer.peer?.preferences?.profile?.avatar || ''
  const countryFlag = offer.peer?.countryInfo?.image?.urls?.avatar || ''

  // Format user information
  const user = ((offer.peer?.firstName || '') + ' ' + (offer.peer?.lastName || '')).trim()
  const rating = offer.peer?.numbers?.score || 0
  const trades = offer.peer?.numbers?.completedOperations || 0
  const country = offer.peer?.country || 'Unknown'
  const countryDisplay = getCountryFlag(country)

  // Get payment method with enhanced context (USDC + gift cards)
  const rawPaymentMethod = offer.makerPaymentMethod?.version?.category?.translationTag || 'Unknown'
  let paymentMethod = rawPaymentMethod
  if (paymentMethod.startsWith('CATEGORY_TREE:AIRTM_')) {
    paymentMethod = paymentMethod.replace('CATEGORY_TREE:AIRTM_', '')
  }
  if (paymentMethod.startsWith('E_TRANSFER_')) {
    paymentMethod = paymentMethod.replace('E_TRANSFER_', '')
  }
  if (paymentMethod.startsWith('GIFT_CARD_')) {
    paymentMethod = paymentMethod.replace('GIFT_CARD_', '')
  }
  // Replace underscores with spaces and capitalize words
  paymentMethod = paymentMethod.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, (l: string) => l.toUpperCase())

  // Extract service name for gift cards
  const categoryId = offer.makerPaymentMethod?.categoryId || ''
  if (categoryId.includes('gift-card')) {
    const parts = categoryId.split(':')
    if (parts.length > 2) {
      let serviceName = parts[parts.length - 1]
        .replace(/[-_]/g, ' ')
        .replace(/\b\w/g, (l: string) => l.toUpperCase())

      // Special cases for known services
      if (serviceName.toLowerCase() === 'ebay') serviceName = 'eBay'
      if (serviceName.toLowerCase() === 'paypal') serviceName = 'PayPal'
      if (serviceName.toLowerCase() === 'amazon') serviceName = 'Amazon'

      paymentMethod = `${serviceName} Gift Card`
    }
  }

  // Enhanced amount display with USDC and withdrawal context
  let amountDisplay = ''

  if (usdcInfo.operationType === 'withdrawal') {
    // For withdrawals, show deduction clearly
    amountDisplay = `-${conversion.amount} ${conversion.currency}`
    if (conversion.originalAmount && conversion.originalCurrency) {
      amountDisplay += ` → ${conversion.originalAmount} ${conversion.originalCurrency}`
    }
  } else {
    // For other operations
    amountDisplay = `${conversion.amount} ${conversion.currency}`

    // Add original amount if conversion occurred
    if (conversion.originalAmount && conversion.originalCurrency &&
        conversion.originalCurrency !== conversion.currency) {
      amountDisplay += ` (${conversion.originalAmount} ${conversion.originalCurrency})`
    }
  }

  // Add conversion note if present
  if (conversion.conversionNote) {
    amountDisplay += ` ${conversion.conversionNote}`
  }

  // Add operation type indicators with context
  let operationIndicator = ''
  const isGiftCard = categoryId.includes('gift-card')
  const isBankTransfer = categoryId.includes('bank') || paymentMethod.includes('bank')
  const isCardPayment = categoryId.includes('card') || paymentMethod.includes('card')

  if (usdcInfo.operationType === 'withdrawal') {
    if (isGiftCard) {
      operationIndicator = '🎁 '  // Gift card purchase
    } else {
      operationIndicator = '💸 '  // Regular withdrawal
    }
  } else if (usdcInfo.operationType === 'deposit') {
    if (isBankTransfer) {
      operationIndicator = '🏦 '  // Bank deposit
    } else if (isCardPayment) {
      operationIndicator = '💳 '  // Card deposit
    } else {
      operationIndicator = '💰 '  // Regular deposit
    }
  } else if (usdcInfo.isUSDC) {
    operationIndicator = '🪙 '
  }

  // Format timestamps
  const timestamp = new Date(offer.createdAt).toLocaleString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  })

  const monthYear = new Date(offer.createdAt).toLocaleDateString('en-US', {
    month: 'short',
    year: 'numeric'
  })

  // Build enhanced message with visual elements
  let message = ''

  // Service logo and name section
  if (serviceLogo) {
    message += `🖼️ [Service Logo](${serviceLogo})\n`
  }
  message += `📋 ${paymentMethod}\n\n`

  // Main transaction line with operation indicator
  message += `${operationIndicator}${amountDisplay} : ${paymentMethod}\n\n`

  // Peer information with avatar
  if (peerAvatar) {
    message += `👤 [Avatar](${peerAvatar}) `
  } else {
    message += `👤 `
  }
  message += `${user} ${rating}⭐ (${trades} trades)\n`

  // Location and timing information
  if (countryFlag) {
    message += `🏳️ [Flag](${countryFlag}) ${country} | ${monthYear} | ${timestamp}\n`
  } else {
    message += `${countryDisplay} ${country} | ${monthYear} | ${timestamp}\n`
  }

  // Exchange rate information (if available)
  if (offer.displayRate?.rate) {
    const rate = parseFloat(offer.displayRate.rate).toFixed(4)
    const fromCurrency = usdcInfo.usdcField === 'wallet' ? 'USDC' : (offer.walletCurrency?.symbol || '$')
    const toCurrency = usdcInfo.usdcField === 'local' ? 'USDC' : (offer.currency?.symbol || '$')
    message += `💱 $1 ${fromCurrency} = $${rate} ${toCurrency}`
  }

  return message
}

/**
 * Play notification sound
 */
// Track offscreen document state
let offscreenDocumentExists = false
let soundPlayingPromise: Promise<void> | null = null

async function playNotificationSound(): Promise<void> {
  // If sound is already playing, wait for it to complete
  if (soundPlayingPromise) {
    return soundPlayingPromise
  }

  soundPlayingPromise = (async () => {
    try {
      // Try to create offscreen document (will fail if already exists)
      if (!offscreenDocumentExists) {
        try {
          await chrome.offscreen.createDocument({
            url: 'sounds/beep.html',
            reasons: [chrome.offscreen.Reason.AUDIO_PLAYBACK],
            justification: 'Play notification sound for new offers'
          })
          offscreenDocumentExists = true
          // Removed delay for instant sound playback
        } catch (error: any) {
          // If document already exists, that's fine
          if (error.message?.includes('Only a single offscreen document may be created')) {
            offscreenDocumentExists = true
          } else {
            throw error
          }
        }
      }

      // Send message to offscreen document to play sound
      try {
        await chrome.runtime.sendMessage({ type: 'PLAY_SOUND' })
      } catch (error) {
        console.log('Message to offscreen document failed:', error)
        // Reset state and try to recreate
        offscreenDocumentExists = false
        throw error
      }

      // Removed delay for instant sound playback

    } catch (error) {
      console.error('Error playing notification sound:', error)
      // Reset state on error
      offscreenDocumentExists = false
    } finally {
      // Clean up - close the offscreen document after use
      try {
        if (offscreenDocumentExists) {
          await chrome.offscreen.closeDocument()
          offscreenDocumentExists = false
        }
      } catch (error) {
        // Ignore errors when closing document
        offscreenDocumentExists = false
      }
      soundPlayingPromise = null
    }
  })()

  return soundPlayingPromise
}

/**
 * Handle offer acceptance with context recovery
 */
async function handleAcceptOffer(offerId: string): Promise<boolean> {
  try {
    // Check extension context before proceeding
    if (!isExtensionContextValid()) {
      console.warn('⚠️ Extension context invalid during offer acceptance, cannot proceed')
      throw new Error('Extension context is invalid. Please reload the extension.')
    }

    // Send message to content script
    const tabs = await chrome.tabs.query({ url: '*://app.airtm.com/peer-transfers/available*' })

    if (tabs.length === 0) {
      throw new Error('Airtm tab not found')
    }

    const response = await chrome.tabs.sendMessage(tabs[0].id!, {
      type: 'ACCEPT_OFFER',
      data: { offerId }
    })

    if (response?.success) {
      offerStats.acceptedOffers++
      console.log('Offer ' + offerId + ' accepted successfully')

      // Start monitoring for operation URL redirection
      startOperationUrlMonitoring(offerId, tabs[0].id!)

      return true
    } else {
      throw new Error(response?.error || 'Failed to accept offer')
    }

  } catch (error) {
    console.error('Error accepting offer ' + offerId + ':', error)

    // Check if error is due to context invalidation using standardized function
    if (isContextInvalidationError(error)) {
      console.warn('⚠️ Connection error detected, likely due to extension context invalidation')

      // Send Telegram notification about the issue if configured
      if (currentSettings.telegramBotToken && currentSettings.telegramChatId) {
        try {
          await sendTelegramMessage(`⚠️ Extension Context Issue\n\nFailed to accept offer ${offerId}\nReason: Extension context lost\n\nPlease reload the extension or refresh the page.`)
        } catch (telegramError) {
          console.error('Failed to send Telegram notification about context issue:', telegramError)
        }
      }
    }

    throw error
  }
}

/**
 * Handle operation details update from GraphQL interception
 */
async function handleOperationDetailsUpdate(data: any): Promise<void> {
  try {
    console.log('📋 Processing operation details update:', data)
    
    const { operation, timestamp, source } = data
    
    if (!operation || !operation.id) {
      console.warn('Invalid operation data received')
      return
    }
    
    // Store operation details
    await chrome.storage.local.set({
      [`operation_${operation.id}`]: {
        ...operation,
        lastUpdated: timestamp,
        source
      }
    })
    
    // Send Telegram notification about operation status
    if (currentSettings.telegramBotToken && currentSettings.telegramChatId) {
      await sendTelegramOperationUpdate(operation)
    }
    
    console.log(`✅ Operation ${operation.id} details updated - Status: ${operation.status}`)
    
  } catch (error) {
    console.error('❌ Error handling operation details update:', error)
  }
}

/**
 * Start monitoring for operation URL redirection after offer acceptance
 */
function startOperationUrlMonitoring(offerId: string, tabId: number): void {
  console.log(`🔍 Starting URL monitoring for accepted offer: ${offerId}`)
  
  let checkCount = 0
  const maxChecks = 30 // Check for 3 seconds (30 * 100ms)
  
  const urlCheckInterval = setInterval(async () => {
    try {
      checkCount++
      
      // Get current tab URL
      const tab = await chrome.tabs.get(tabId)
      const currentUrl = tab.url
      
      console.log(`🔍 URL check ${checkCount}/${maxChecks}: ${currentUrl}`)
      
      // Check if redirected to operation URL pattern
      const operationUrlPattern = /https:\/\/app\.airtm\.com\/operations\/[A-Z0-9]+/
      
      if (currentUrl && operationUrlPattern.test(currentUrl)) {
        console.log('✅ Operation URL detected:', currentUrl)
        
        // Extract operation ID from URL
        const operationId = currentUrl.split('/operations/')[1]
        
        // Update Telegram status to "accepted"
        if (currentSettings.telegramBotToken && currentSettings.telegramChatId) {
          await sendTelegramMessage(`🎯 Offer ${offerId} accepted successfully!\n📋 Operation ID: ${operationId}\n🔗 URL: ${currentUrl}\n⏰ Status: Accepted`)
        }
        
        // Store accepted operation info
        await chrome.storage.local.set({
          [`accepted_operation_${offerId}`]: {
            operationId,
            url: currentUrl,
            timestamp: new Date().toISOString(),
            status: 'accepted'
          }
        })
        
        clearInterval(urlCheckInterval)
        return
      }
      
      // If max checks reached without finding operation URL
      if (checkCount >= maxChecks) {
        console.log('⚠️ Operation URL not detected within timeout')
        
        // Update Telegram status to "Not Available"
        if (currentSettings.telegramBotToken && currentSettings.telegramChatId) {
          await sendTelegramMessage(`❌ Offer ${offerId} acceptance failed or timed out\n⏰ Status: Not Available`)
        }
        
        // Store failed operation info
        await chrome.storage.local.set({
          [`failed_operation_${offerId}`]: {
            timestamp: new Date().toISOString(),
            status: 'not_available',
            reason: 'url_not_detected'
          }
        })
        
        clearInterval(urlCheckInterval)
      }
      
    } catch (error) {
      console.error('❌ Error during URL monitoring:', error)
      clearInterval(urlCheckInterval)
    }
  }, 100) // Check every 100ms
}

/**
 * Send Telegram message for operation updates with proper currency conversion
 */
async function sendTelegramOperationUpdate(operation: any): Promise<void> {
  try {
    // Extract displayRateInfo from metadata if available (OperationDetails structure)
    const displayRateInfo = operation.metadata?.displayRateInfo

    // Convert operation to Offer-like structure for currency conversion
    const offerLike: Offer = {
      id: operation.id || '',
      hash: operation.hash || '',
      operationType: operation.operationType || 'UNKNOWN',
      status: operation.status || 'UNKNOWN',
      isMine: operation.isMine || false,
      createdAt: operation.createdAt || '',
      updatedAt: operation.updatedAt || null,
      grossAmount: operation.grossAmount || '0',
      netAmount: operation.netAmount || '0',
      // Pass metadata with displayRateInfo for rate source priority
      metadata: {
        ...operation.metadata,
        displayRateInfo: displayRateInfo
      },
      walletCurrency: operation.walletCurrency || { symbol: '$' },
      peer: operation.secondParty || operation.peer || { firstName: 'Unknown', lastName: '' },

      // Include rate information for conversion with enhanced rateInfo
      rate: operation.rate,
      displayRate: operation.displayRate,
      rateInfo: {
        ...operation.rateInfo,
        // Keep original rateInfo fields as they contain the actual transaction amounts
        // Don't override fundsToSendTaker/fundsToReceiveTaker from displayRateInfo
        // as they represent different data structures
      },
      currency: operation.currency,
      makerPaymentMethod: operation.makerPaymentMethod,

      __typename: operation.__typename || 'Operations__Unknown'
    } as Offer

    // Get converted currency amount
    const conversion = convertCurrencyAmount(offerLike)

    // Format user information
    const user = operation.secondParty ?
      `${operation.secondParty.firstName || ''} ${operation.secondParty.lastName || ''}`.trim() :
      operation.peer ?
      `${operation.peer.firstName || ''} ${operation.peer.lastName || ''}`.trim() :
      'Unknown'

    // Build amount display
    let amountDisplay = `${conversion.amount} ${conversion.currency}`
    if (conversion.conversionNote) {
      amountDisplay += ` ${conversion.conversionNote}`
    }
    if (conversion.originalAmount && conversion.originalCurrency &&
        conversion.originalCurrency !== conversion.currency) {
      amountDisplay += ` (${conversion.originalAmount} ${conversion.originalCurrency})`
    }

    const message = `📋 Operation Update\n` +
      `🆔 ID: ${operation.hash || operation.id}\n` +
      `📊 Status: ${operation.status}\n` +
      `💰 Amount: ${amountDisplay}\n` +
      `🔄 Type: ${operation.operationType}\n` +
      `👤 Partner: ${user}\n` +
      `⏰ Updated: ${new Date().toLocaleString()}`

    await sendTelegramMessage(message)
  } catch (error) {
    console.error('❌ Error sending Telegram operation update:', error)
  }
}

/**
 * Handle successful operation acceptance
 */
async function handleOperationAccepted(data: any): Promise<void> {
  try {
    console.log('✅ Processing operation acceptance:', data)
    
    // Store acceptance data
    await chrome.storage.local.set({
      [`accepted_operation_${data.operationId}`]: {
        ...data,
        processedAt: new Date().toISOString()
      }
    })
    
    // Update statistics
    offerStats.acceptedOffers++
    
    console.log('✅ Operation acceptance processed successfully')
  } catch (error) {
    console.error('❌ Error handling operation accepted:', error)
  }
}

/**
 * Handle operation not available error
 */
async function handleOperationNotAvailable(data: any): Promise<void> {
  try {
    console.log('🚫 Processing operation not available:', data)
    
    // Store unavailable operation data
    await chrome.storage.local.set({
      [`unavailable_operation_${data.operationId || 'unknown'}`]: {
        ...data,
        processedAt: new Date().toISOString()
      }
    })
    
    console.log('🚫 Operation not available processed successfully')
  } catch (error) {
    console.error('❌ Error handling operation not available:', error)
  }
}

/**
 * Handle operation accept error
 */
async function handleOperationAcceptError(data: any): Promise<void> {
  try {
    console.log('⚠️ Processing operation accept error:', data)
    
    // Store error data
    await chrome.storage.local.set({
      [`error_operation_${Date.now()}`]: {
        ...data,
        processedAt: new Date().toISOString()
      }
    })
    
    console.log('⚠️ Operation accept error processed successfully')
  } catch (error) {
    console.error('❌ Error handling operation accept error:', error)
  }
}

/**
 * Handle operation declined successfully
 */
async function handleOperationDeclined(data: any): Promise<void> {
  try {
    console.log('🚫 Processing operation decline:', data)
    
    // Store decline data
    await chrome.storage.local.set({
      [`declined_operation_${Date.now()}`]: {
        ...data,
        processedAt: new Date().toISOString()
      }
    })
    
    // Update statistics
    offerStats.rejectedOffers++
    
    console.log('🚫 Operation decline processed successfully')
  } catch (error) {
    console.error('❌ Error handling operation declined:', error)
  }
}

/**
 * Handle operation decline error
 */
async function handleOperationDeclineError(data: any): Promise<void> {
  try {
    console.log('⚠️ Processing operation decline error:', data)
    
    // Store decline error data
    await chrome.storage.local.set({
      [`decline_error_operation_${Date.now()}`]: {
        ...data,
        processedAt: new Date().toISOString()
      }
    })
    
    console.log('⚠️ Operation decline error processed successfully')
  } catch (error) {
    console.error('❌ Error handling operation decline error:', error)
  }
}

/**
 * Send Telegram operation status update
 */
async function sendTelegramOperationStatusUpdate(data: any): Promise<void> {
  try {
    console.log('📱 Sending Telegram operation status update:', data)
    
    const { operationId, status } = data
    
    let message: string
    if (status === 'accepted') {
      message = `✅ *Operation Accepted*\n\n` +
        `🆔 Operation ID: ${operationId}\n` +
        `📊 Status: Successfully accepted\n` +
        `⏰ Time: ${new Date().toLocaleString()}\n\n` +
        `🎉 Great! The operation was accepted successfully.`
    } else if (status === 'Not Available') {
      message = `🚫 *Operation Not Available*\n\n` +
        `🆔 Operation ID: ${operationId}\n` +
        `📊 Status: No longer available\n` +
        `⏰ Time: ${new Date().toLocaleString()}\n\n` +
        `😔 The operation was taken by someone else or expired.`
    } else {
      message = `📋 *Operation Update*\n\n` +
        `🆔 Operation ID: ${operationId}\n` +
        `📊 Status: ${status}\n` +
        `⏰ Time: ${new Date().toLocaleString()}`
    }
    
    await sendTelegramMessage(message)
    console.log('📱 Telegram operation status update sent successfully')
  } catch (error) {
    console.error('❌ Error sending Telegram operation status update:', error)
  }
}

/**
 * Handle offer rejection with context recovery
 */
async function handleRejectOffer(offerId: string): Promise<boolean> {
  try {
    // Check extension context before proceeding
    if (!isExtensionContextValid()) {
      console.warn('⚠️ Extension context invalid during offer rejection, cannot proceed')
      throw new Error('Extension context is invalid. Please reload the extension.')
    }

    // Send message to content script
    const tabs = await chrome.tabs.query({ url: '*://app.airtm.com/peer-transfers/available*' })

    if (tabs.length === 0) {
      throw new Error('Airtm tab not found')
    }

    const response = await chrome.tabs.sendMessage(tabs[0].id!, {
      type: 'REJECT_OFFER',
      data: { offerId }
    })

    if (response?.success) {
      offerStats.rejectedOffers++
      console.log('Offer ' + offerId + ' rejected successfully')
      return true
    } else {
      throw new Error(response?.error || 'Failed to reject offer')
    }

  } catch (error) {
    console.error('Error rejecting offer ' + offerId + ':', error)

    // Check if error is due to context invalidation using standardized function
    if (isContextInvalidationError(error)) {
      console.warn('⚠️ Connection error detected, likely due to extension context invalidation')

      // Send Telegram notification about the issue if configured
      if (currentSettings.telegramBotToken && currentSettings.telegramChatId) {
        try {
          await sendTelegramMessage(`⚠️ Extension Context Issue\n\nFailed to reject offer ${offerId}\nReason: Extension context lost\n\nPlease reload the extension or refresh the page.`)
        } catch (telegramError) {
          console.error('Failed to send Telegram notification about context issue:', telegramError)
        }
      }
    }

    throw error
  }
}



/**
 * Update offer statistics
 */
function updateOfferStats(offers: Offer[]): void {
  offerStats.totalOffers = offers.length
  offerStats.newOffers = offers.filter(offer => {
    const createdAt = new Date(offer.createdAt)
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000)
    return createdAt > fiveMinutesAgo
  }).length

  // Average rate calculation removed
  offerStats.averageRate = 0
}

/**
 * Load settings from storage
 */
async function loadSettings(): Promise<void> {
  try {
    const result = await chrome.storage.sync.get('settings')
    currentSettings = { ...DEFAULT_SETTINGS, ...result.settings }
    
    // Always enable monitoring when Chrome starts
    currentSettings.monitoring = true
    
    // Update fuzzy matcher configuration
    updateFuzzyMatcherConfig()
    
    // Save the updated settings back to storage
    await chrome.storage.sync.set({ settings: currentSettings })
    
    console.log('Settings loaded and monitoring enabled:', currentSettings)
  } catch (error) {
    console.error('Error loading settings:', error)
    currentSettings = { ...DEFAULT_SETTINGS, monitoring: true }
    updateFuzzyMatcherConfig()
  }
}

/**
 * Handle settings update
 */
async function handleSettingsUpdate(newSettings: Partial<ExtensionSettings>): Promise<void> {
  try {
    currentSettings = { ...currentSettings, ...newSettings }
    
    // Update fuzzy matcher configuration if fuzzy matching settings changed
    if (newSettings.fuzzyMatching) {
      updateFuzzyMatcherConfig()
    }
    
    await chrome.storage.sync.set({ settings: currentSettings })
    console.log('Settings updated:', currentSettings)
    
    // Update extension badge based on monitoring state
    updateExtensionBadge()
  } catch (error) {
    console.error('Error updating settings:', error)
    throw error
  }
}

/**
 * Update extension badge based on current monitoring state
 */
function updateExtensionBadge(): void {
  if (currentSettings.monitoring) {
    chrome.action.setBadgeText({ text: 'ON' })
    chrome.action.setBadgeBackgroundColor({ color: '#10b981' }) // Green
  } else {
    chrome.action.setBadgeText({ text: 'OFF' })
    chrome.action.setBadgeBackgroundColor({ color: '#6b7280' }) // Gray
  }
}

/**
 * Update fuzzy matcher configuration based on current settings
 */
function updateFuzzyMatcherConfig(): void {
  try {
    // Ensure fuzzyMatching exists with default values
    const fuzzyConfig = currentSettings.fuzzyMatching || {
      enabled: false,
      threshold: 0.8,
      enableAliases: true,
      customAliases: {}
    }
    
    paymentMethodMatcher.updateConfig({
      threshold: fuzzyConfig.threshold,
      enableAliases: fuzzyConfig.enableAliases,
      customAliases: fuzzyConfig.customAliases
    })
    console.log('Fuzzy matcher configuration updated:', fuzzyConfig)
  } catch (error) {
    console.error('Error updating fuzzy matcher configuration:', error)
  }
}

/**
 * Setup alarm listeners
 */
function setupAlarmListeners(): void {
  chrome.alarms.onAlarm.addListener((alarm) => {
    console.log('Alarm triggered:', alarm.name)
    
    switch (alarm.name) {
      case 'monitoring-check':
        // Periodic monitoring check
        break
      case 'cleanup-storage':
        cleanupStorage()
        break
    }
  })

  // Create periodic alarms
  chrome.alarms.create('cleanup-storage', { periodInMinutes: 60 })
}

/**
 * Setup notification handlers
 */
function setupNotificationHandlers(): void {
  // Handle Chrome notification button clicks
  chrome.notifications.onButtonClicked.addListener(async (notificationId, buttonIndex) => {
    try {
      // Extract offer ID from notification ID
      const offerIdMatch = notificationId.match(/offer_(.+)_\d+/)
      const offerId = offerIdMatch ? offerIdMatch[1] : notificationId
      
      if (buttonIndex === 0) {
        // Accept button
        await handleAcceptOffer(offerId)
      } else if (buttonIndex === 1) {
        // Reject button
        await handleRejectOffer(offerId)
      }
      
      // Clear the notification
      chrome.notifications.clear(notificationId)
      
    } catch (error) {
      console.error('Error handling notification button click:', error)
    }
  })

  // Handle messages from notification manager
  chrome.runtime.onMessage.addListener((message, sender, _sendResponse) => {
    if (message.type === 'ACCEPT_OFFER' || message.type === 'REJECT_OFFER' || message.type === 'IGNORE_OFFER') {
      notificationManager.handleMessage(message, sender)
    }
  })
}

/**
 * Cleanup old storage data
 */
async function cleanupStorage(): Promise<void> {
  try {
    console.log('Cleaning up storage...')
    
    // Remove old offers (older than 24 hours)
    const result = await chrome.storage.local.get(['airtm_offers_timestamp'])
    const timestamp = result.airtm_offers_timestamp
    
    if (timestamp) {
      const age = Date.now() - new Date(timestamp).getTime()
      const maxAge = 24 * 60 * 60 * 1000 // 24 hours
      
      if (age > maxAge) {
        await chrome.storage.local.remove(['airtm_offers', 'airtm_offers_timestamp'])
        console.log('Old offers cleaned up')
      }
    }
    
  } catch (error) {
    console.error('Error cleaning up storage:', error)
  }
}

/**
 * Inject content scripts into existing Airtm tabs
 */
async function injectContentScriptsIntoExistingTabs(): Promise<void> {
  try {
    console.log('🔍 Checking for existing Airtm tabs...')
    
    // Find all Airtm tabs
    const tabs = await chrome.tabs.query({ 
      url: '*://app.airtm.com/peer-transfers/available*' 
    })
    
    if (tabs.length === 0) {
      console.log('📭 No existing Airtm tabs found')
      return
    }
    
    console.log(`🎯 Found ${tabs.length} existing Airtm tab(s)`)
    console.log('ℹ️ Content scripts will be automatically injected by Chrome based on manifest.json')
    
    // Content scripts are automatically injected by Chrome based on manifest.json
    // No manual injection needed for content scripts declared in manifest
    for (const tab of tabs) {
      if (tab.id && tab.url) {
        console.log(`📋 Found Airtm tab ${tab.id}: ${tab.url}`)
      }
    }
  } catch (error) {
    console.error('❌ Error checking existing tabs:', error)
  }
}

// Keep service worker alive
function keepServiceWorkerAlive() {
  const keepAlive = () => {
    try {
      if (chrome?.runtime?.getPlatformInfo) {
        chrome.runtime.getPlatformInfo(() => {
          // This keeps the service worker alive
          if (chrome.runtime.lastError) {
            console.warn('Keep alive error:', chrome.runtime.lastError)
          }
        })
      }
    } catch (error) {
      console.warn('Keep alive failed:', error)
    }
  }

  // Initial call
  keepAlive()
  
  // Set up interval
  setInterval(keepAlive, 20000) // Every 20 seconds
}

// Initialize background service worker
initializeBackground().then(() => {
  keepServiceWorkerAlive()
})

// Handle service worker startup
chrome.runtime.onStartup.addListener(() => {
  console.log('Service worker starting up...')
  initializeBackground()
})

// Handle extension installation
chrome.runtime.onInstalled.addListener((details) => {
  console.log('Extension installed/updated:', details.reason)
  initializeBackground()
})
