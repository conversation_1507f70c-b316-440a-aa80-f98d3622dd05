<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=400, height=600, initial-scale=1.0, user-scalable=no">
  <title>Airtm Monitor Pro</title>
  <style>
    /* Popup-specific CSS - Inline for immediate loading */
    :root {
      --primary-500: #0ea5e9;
      --secondary-500: #8b5cf6;
      --success-500: #10b981;
      --warning-500: #f59e0b;
      --error-500: #ef4444;
      --gray-50: #f9fafb;
      --gray-100: #f3f4f6;
      --gray-200: #e5e7eb;
      --gray-300: #d1d5db;
      --gray-400: #9ca3af;
      --gray-500: #6b7280;
      --gray-600: #4b5563;
      --gray-700: #374151;
      --gray-800: #1f2937;
      --gray-900: #111827;
      --space-1: 0.25rem;
      --space-2: 0.5rem;
      --space-3: 0.75rem;
      --space-4: 1rem;
      --space-6: 1.5rem;
      --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
      --radius-md: 0.5rem;
      --radius-lg: 0.75rem;
      --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
      --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      --transition-fast: 150ms ease-in-out;
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    html, body {
      width: 400px !important;
      height: 600px !important;
      min-width: 400px !important;
      min-height: 600px !important;
      max-width: 400px !important;
      max-height: 600px !important;
      margin: 0 !important;
      padding: 0 !important;
      overflow: hidden !important;
      font-family: var(--font-family);
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    #popup-container {
      width: 400px !important;
      height: 600px !important;
      display: flex;
      flex-direction: column;
      background: white;
      position: relative;
    }

    /* Header */
    .popup-header {
      background: linear-gradient(135deg, var(--primary-500), var(--secondary-500));
      color: white;
      padding: var(--space-4);
      display: flex;
      align-items: center;
      gap: var(--space-3);
      box-shadow: var(--shadow-md);
    }

    .popup-icon {
      width: 32px;
      height: 32px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: var(--radius-md);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.25rem;
    }

    .popup-title {
      flex: 1;
    }

    .popup-title h1 {
      font-size: 1.125rem;
      font-weight: 600;
      margin-bottom: 0.125rem;
    }

    .popup-title p {
      font-size: 0.75rem;
      opacity: 0.9;
    }

    .popup-status {
      display: flex;
      align-items: center;
      gap: var(--space-1);
      font-size: 0.75rem;
    }

    .status-dot {
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background: var(--success-500);
    }

    /* Content */
    .popup-content {
      flex: 1;
      padding: var(--space-4);
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      gap: var(--space-4);
    }

    .status-card {
      background: var(--gray-50);
      border: 1px solid var(--gray-200);
      border-radius: var(--radius-lg);
      padding: var(--space-4);
    }

    .status-card h3 {
      font-size: 0.875rem;
      font-weight: 600;
      color: var(--gray-900);
      margin-bottom: var(--space-2);
    }

    .status-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--space-3);
    }

    .status-item {
      text-align: center;
    }

    .status-value {
      font-size: 1.25rem;
      font-weight: 700;
      color: var(--primary-500);
      margin-bottom: var(--space-1);
    }

    .status-label {
      font-size: 0.75rem;
      color: var(--gray-600);
    }

    .quick-actions {
      display: flex;
      flex-direction: column;
      gap: var(--space-2);
    }

    .action-button {
      display: flex;
      align-items: center;
      gap: var(--space-2);
      padding: var(--space-3);
      background: white;
      border: 1px solid var(--gray-200);
      border-radius: var(--radius-md);
      cursor: pointer;
      transition: all var(--transition-fast);
      font-size: 0.875rem;
      font-weight: 500;
      color: var(--gray-700);
    }

    .action-button:hover {
      background: var(--gray-50);
      border-color: var(--primary-500);
      color: var(--primary-600);
    }

    .action-button.primary {
      background: linear-gradient(135deg, var(--primary-500), var(--secondary-500));
      color: white;
      border-color: transparent;
    }

    .action-button.primary:hover {
      transform: translateY(-1px);
      box-shadow: var(--shadow-md);
    }

    .action-icon {
      font-size: 1rem;
    }

    /* Footer */
    .popup-footer {
      padding: var(--space-3) var(--space-4);
      background: var(--gray-50);
      border-top: 1px solid var(--gray-200);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .footer-link {
      font-size: 0.75rem;
      color: var(--gray-500);
      text-decoration: none;
      cursor: pointer;
    }

    .footer-link:hover {
      color: var(--primary-500);
    }

    /* Loading state */
    .loading {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      color: var(--gray-500);
    }

    .spinner {
      width: 20px;
      height: 20px;
      border: 2px solid var(--gray-300);
      border-top: 2px solid var(--primary-500);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-right: var(--space-2);
    }

    @keyframes spin {
      to { transform: rotate(360deg); }
    }

    /* Toggle switch */
    .toggle-switch {
      position: relative;
      display: inline-block;
      width: 44px;
      height: 24px;
    }

    .toggle-input {
      opacity: 0;
      width: 0;
      height: 0;
    }

    .toggle-slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: var(--gray-300);
      transition: 0.3s;
      border-radius: 24px;
    }

    .toggle-slider:before {
      position: absolute;
      content: "";
      height: 18px;
      width: 18px;
      left: 3px;
      bottom: 3px;
      background-color: white;
      transition: 0.3s;
      border-radius: 50%;
    }

    .toggle-input:checked + .toggle-slider {
      background: linear-gradient(135deg, var(--primary-500), var(--secondary-500));
    }

    .toggle-input:checked + .toggle-slider:before {
      transform: translateX(20px);
    }
  </style>
</head>
<body>
  <div id="popup-container">
    <!-- Header -->
    <div class="popup-header">
      <div class="popup-icon">⚡</div>
      <div class="popup-title">
        <h1>Airtm Monitor Pro</h1>
        <p>Real-time offer monitoring</p>
      </div>
      <div class="popup-status">
        <div class="status-dot"></div>
        <span id="connection-text">Connected</span>
      </div>
    </div>

    <!-- Content -->
    <div class="popup-content">
      <!-- Status Overview -->
      <div class="status-card">
        <h3>📊 Monitoring Status</h3>
        <div class="status-grid">
          <div class="status-item">
            <div class="status-value" id="offers-count">0</div>
            <div class="status-label">Active Offers</div>
          </div>
          <div class="status-item">
            <div class="status-value" id="monitoring-time">00:00</div>
            <div class="status-label">Monitoring Time</div>
          </div>
        </div>
      </div>

      <!-- Quick Toggle -->
      <div class="status-card">
        <h3>🎛️ Quick Controls</h3>
        <div class="action-button" id="toggle-monitoring">
          <span class="action-icon">👁️</span>
          <span>Monitoring</span>
          <div style="margin-left: auto;">
            <label class="toggle-switch">
              <input type="checkbox" id="monitoring-toggle" class="toggle-input">
              <span class="toggle-slider"></span>
            </label>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="status-card">
        <h3>⚡ Quick Actions</h3>
        <div class="quick-actions">
          <button class="action-button primary" id="open-airtm">
            <span class="action-icon">🌐</span>
            <span>Open Airtm</span>
          </button>
          <button class="action-button" id="view-offers">
            <span class="action-icon">📋</span>
            <span>View Offers</span>
          </button>
          <button class="action-button" id="open-settings">
            <span class="action-icon">⚙️</span>
            <span>Settings</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <div class="popup-footer">
      <a href="#" class="footer-link" id="help-link">Help</a>
      <span style="font-size: 0.75rem; color: var(--gray-400);">v2.0.0</span>
      <a href="#" class="footer-link" id="about-link">About</a>
    </div>
  </div>

  <!-- External Script -->
  <script src="popup.js"></script>
</body>
</html>
