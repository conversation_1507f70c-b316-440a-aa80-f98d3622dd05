<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Classical Music Setup for AirTM Extension</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .step {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #007cba;
        }
        .warning {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        a {
            color: #007cba;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
        code {
            background: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>🎵 Replace Beep Sound with Classical Music</h1>
    
    <div class="container">
        <h2>Quick Setup Guide</h2>
        
        <div class="step">
            <h3>Step 1: Download Classical Music</h3>
            <p>Choose from these free sources:</p>
            <ul>
                <li><strong>Pixabay:</strong> <a href="https://pixabay.com/music/search/classical/" target="_blank">Free Classical Music</a> (No attribution required)</li>
                <li><strong>Free Music Archive:</strong> <a href="https://freemusicarchive.org/genre/Classical/" target="_blank">Classical Genre</a> (Various licenses)</li>
                <li><strong>Chosic:</strong> <a href="https://www.chosic.com/free-music/classical/" target="_blank">Classical Music</a> (Attribution required)</li>
            </ul>
            <p><strong>Recommended:</strong> Look for short pieces (30 seconds or less) suitable for notifications.</p>
        </div>
        
        <div class="step">
            <h3>Step 2: Prepare the Audio File</h3>
            <p>1. Download your chosen classical music file</p>
            <p>2. Rename it to <code>classical.mp3</code></p>
            <p>3. Ensure it's in MP3 format and under 1MB for best performance</p>
        </div>
        
        <div class="step">
            <h3>Step 3: Replace the Current Sound</h3>
            <p>1. Navigate to your extension folder: <code>sounds/</code></p>
            <p>2. Backup the current <code>beep.mp3</code> (rename to <code>beep-backup.mp3</code>)</p>
            <p>3. Copy your downloaded classical music file to the sounds folder</p>
            <p>4. Rename your classical music file to <code>beep.mp3</code> (replacing the original)</p>
            <p><strong>Important:</strong> The file MUST be named exactly <code>beep.mp3</code> for the extension to use it!</p>
        </div>
        
        <div class="step">
            <h3>Step 4: Update the Extension</h3>
            <p>1. Run <code>npm run build</code> in your extension directory</p>
            <p>2. Reload the extension in Chrome</p>
            <p>3. Test the new sound using the extension's test notification feature</p>
        </div>
    </div>
    
    <div class="warning">
        <h3>⚠️ Important Notes</h3>
        <ul>
            <li>Make sure the audio file is not too long (recommended: 3-10 seconds)</li>
            <li>Check the license requirements if using music that requires attribution</li>
            <li>Keep a backup of the original beep.mp3 in case you want to revert</li>
            <li>The extension has been updated to fix the "single offscreen document" error</li>
        </ul>
    </div>
    
    <div class="container">
        <h2>🎼 Recommended Classical Pieces for Notifications</h2>
        <ul>
            <li><strong>Bach - Air on G String</strong> (short excerpt)</li>
            <li><strong>Mozart - Eine kleine Nachtmusik</strong> (opening)</li>
            <li><strong>Beethoven - Ode to Joy</strong> (main theme)</li>
            <li><strong>Pachelbel - Canon in D</strong> (opening)</li>
            <li><strong>Vivaldi - Four Seasons</strong> (Spring opening)</li>
        </ul>
    </div>
</body>
</html>