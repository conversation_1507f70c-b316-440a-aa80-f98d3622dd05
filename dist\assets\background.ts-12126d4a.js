var re=Object.defineProperty;var ne=(e,t,o)=>t in e?re(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o;var M=(e,t,o)=>(ne(e,typeof t!="symbol"?t+"":t,o),o);import{D as L}from"./index-357a2da0.js";import{i as J,a as V}from"./extension-context-5094bf00.js";const $=class ${constructor(){M(this,"activeNotifications",new Map);M(this,"settings",{autoCloseDelay:3e4,playSound:!0})}static getInstance(){return $.instance||($.instance=new $),$.instance}updateSettings(t){this.settings={...this.settings,...t}}async showNotification(t,o){const r={...this.settings,...o};try{await this.showChromeNotification(t,r),await this.highlightOfferOnPage(t),r.playSound&&await this.playNotificationSound()}catch(n){console.error("Error showing notification:",n)}}async showChromeNotification(t,o){var a,u;if(typeof chrome>"u"||!chrome.notifications)throw new Error("Chrome notifications not available");const r=`offer_${t.id}_${Date.now()}`,n=`New ${t.operationType} Offer`,s=`${t.grossAmount} ${((a=t.currency)==null?void 0:a.symbol)||((u=t.walletCurrency)==null?void 0:u.symbol)}`;await chrome.notifications.create(r,{type:"basic",iconUrl:"icons/icon48.png",title:n,message:s,contextMessage:t.peer?`From: ${t.peer.firstName} ${t.peer.lastName}`:void 0,buttons:[{title:"Accept"},{title:"Ignore"}],requireInteraction:!0}),this.activeNotifications.set(t.id,r),setTimeout(()=>{this.closeNotification(t.id)},o.autoCloseDelay)}async highlightOfferOnPage(t){var o;try{const r=await chrome.tabs.query({active:!0,currentWindow:!0});(o=r[0])!=null&&o.id&&await chrome.tabs.sendMessage(r[0].id,{type:"HIGHLIGHT_OFFER",data:{offer:t}})}catch(r){console.log("Could not highlight offer on page:",r)}}closeNotification(t){const o=this.activeNotifications.get(t);if(o){try{typeof o=="string"&&o.startsWith("offer_")&&chrome.notifications.clear(o)}catch(r){console.error("Error closing notification:",r)}this.activeNotifications.delete(t)}}closeAllNotifications(){for(const[t]of this.activeNotifications)this.closeNotification(t)}async playNotificationSound(){var t;try{if(typeof chrome<"u"&&chrome.offscreen){try{await chrome.offscreen.createDocument({url:"sounds/beep.html",reasons:["AUDIO_PLAYBACK"],justification:"Play notification sound"})}catch(o){if(!((t=o.message)!=null&&t.includes("Only a single offscreen document")))throw o}chrome.runtime.sendMessage({type:"PLAY_SOUND"})}}catch(o){console.error("Error playing notification sound:",o)}}handleMessage(t,o){var r,n;switch(t.type){case"ACCEPT_OFFER":this.handleOfferAction("accept",(r=t.data)==null?void 0:r.offerId);break;case"IGNORE_OFFER":this.handleOfferAction("ignore",(n=t.data)==null?void 0:n.offerId);break}}async handleOfferAction(t,o){if(o)try{if(this.closeNotification(o),typeof chrome<"u"&&chrome.runtime){const r=t==="accept"?"ACCEPT_OFFER":"REJECT_OFFER";await chrome.runtime.sendMessage({type:r,data:{offerId:o}})}}catch(r){console.error(`Error handling ${t} action:`,r)}}static isSupported(){return typeof chrome<"u"&&!!chrome.notifications}getAvailableMethods(){const t=[];return typeof chrome<"u"&&chrome.notifications&&t.push("chrome"),t}};M($,"instance");let B=$;const Y=B.getInstance();class ae{constructor(t={}){M(this,"config");M(this,"defaultAliases",{paypal:["paypal","pp","pay pal"],zelle:["zelle","zell","zele"],venmo:["venmo","venm","vemo"],cashapp:["cashapp","cash app","cash-app","$cashtag"],wise:["wise","transferwise","transfer wise"],revolut:["revolut","revolt","revol"],skrill:["skrill","skril","skrilla"],neteller:["neteller","neteler","netelr"],perfectmoney:["perfectmoney","perfect money","pm","perfect-money"],webmoney:["webmoney","web money","wm","web-money"],bitcoin:["bitcoin","btc","bit coin"],ethereum:["ethereum","eth","ether"],usdt:["usdt","tether","usd-t"],binance:["binance","bnb","binance pay"],bank:["bank","banking","bank transfer","wire"],credit:["credit","credit card","cc","visa","mastercard"],debit:["debit","debit card","dc"]});this.config={threshold:.7,enableAliases:!0,customAliases:{},...t}}normalizePaymentMethod(t){return t?t.replace(/^CATEGORY_TREE:AIRTM_/i,"").replace(/^E_TRANSFER_/i,"").replace(/^AIRTM_/i,"").replace(/[_-]/g," ").toLowerCase().trim().replace(/\s+/g," "):""}levenshteinDistance(t,o){const r=Array(o.length+1).fill(null).map(()=>Array(t.length+1).fill(null));for(let n=0;n<=t.length;n++)r[0][n]=n;for(let n=0;n<=o.length;n++)r[n][0]=n;for(let n=1;n<=o.length;n++)for(let s=1;s<=t.length;s++){const a=t[s-1]===o[n-1]?0:1;r[n][s]=Math.min(r[n][s-1]+1,r[n-1][s]+1,r[n-1][s-1]+a)}return r[o.length][t.length]}calculateSimilarity(t,o){if(t===o)return 1;if(!t||!o)return 0;const r=Math.max(t.length,o.length);return 1-this.levenshteinDistance(t,o)/r}wordBasedMatch(t,o){const r=t.split(" ").filter(a=>a.length>0),n=o.split(" ").filter(a=>a.length>0);if(n.length===0)return 0;let s=0;for(const a of n)r.some(u=>u.includes(a)||a.includes(u)||this.calculateSimilarity(u,a)>.8)&&s++;return s/n.length}getAliases(t){const o=this.normalizePaymentMethod(t),r=[o];if(this.config.enableAliases){for(const[,n]of Object.entries(this.config.customAliases))if(n.some(s=>this.normalizePaymentMethod(s)===o)){r.push(...n.map(s=>this.normalizePaymentMethod(s)));break}for(const[,n]of Object.entries(this.defaultAliases))if(n.some(s=>this.normalizePaymentMethod(s)===o)){r.push(...n.map(s=>this.normalizePaymentMethod(s)));break}}return[...new Set(r)]}matchPaymentMethod(t,o){const r=this.normalizePaymentMethod(t),n=this.getAliases(o);let s=0,a="";for(const u of n){if(r===u)return{isMatch:!0,score:1,matchedAlias:u};if(r.includes(u)||u.includes(r)){const i=Math.max(u.length/r.length,r.length/u.length)*.9;i>s&&(s=i,a=u)}const y=this.wordBasedMatch(r,u)*.85;y>s&&(s=y,a=u);const l=this.calculateSimilarity(r,u)*.8;l>s&&(s=l,a=u)}return{isMatch:s>=this.config.threshold,score:s,matchedAlias:s>=this.config.threshold?a:void 0}}matchAnyPaymentMethod(t,o){let r={isMatch:!1,score:0};for(const n of o){const s=this.matchPaymentMethod(t,n);if(s.score>r.score&&(r=s),s.score===1)break}return r}updateConfig(t){this.config={...this.config,...t}}getConfig(){return{...this.config}}}let c=L;const q=new ae,T={totalOffers:0,newOffers:0,acceptedOffers:0,rejectedOffers:0,averageRate:0,lastUpdate:new Date().toISOString()};let I=new Map,K=Date.now();const se=2*60*1e3,ie=60*60*1e3;async function ce(){try{const e=await chrome.storage.local.get("notified_offers");e.notified_offers?(I=new Map(Object.entries(e.notified_offers)),console.log(`Loaded ${I.size} notified offers from storage`)):(I=new Map,console.log("No previously notified offers found in storage"))}catch(e){console.error("Error loading notified offers:",e),I=new Map}}async function Q(){try{const e=Object.fromEntries(I);await chrome.storage.local.set({notified_offers:e})}catch(e){console.error("Error saving notified offers:",e)}}let Z=!1;async function G(){var e,t;if(Z){console.log("Background service worker already initialized, skipping...");return}console.log("Airtm Monitor Pro: Background service worker initializing...");try{if(typeof chrome>"u"||!chrome.runtime)throw new Error("Chrome runtime APIs not available");await ce(),await De(),le(),ve(),Ue(),await ke(),oe(),Z=!0,console.log("Background service worker initialized successfully"),Ce()}catch(o){console.error("Error initializing background service worker:",o);try{(e=chrome.action)==null||e.setBadgeText({text:"!"}),(t=chrome.action)==null||t.setBadgeBackgroundColor({color:"#ff0000"})}catch(r){console.error("Could not set error badge:",r)}}}function le(){chrome.runtime.onMessage.hasListeners()&&chrome.runtime.onMessage.removeListener,chrome.runtime.onMessage.addListener((e,t,o)=>{var r,n,s;if(console.log("Background received message:",e.type,"from:",((r=t.tab)==null?void 0:r.url)||"popup/options"),!e||typeof e.type!="string"){console.error("Invalid message received:",e),o({success:!1,error:"Invalid message format"});return}try{switch(e.type){case"OFFERS_UPDATE":return fe(e.data).then(()=>o({success:!0})).catch(a=>{console.error("Error handling offers update:",a),o({success:!1,error:a.message})}),!0;case"SETTINGS_UPDATE":return $e(e.data).then(()=>o({success:!0})).catch(a=>{console.error("Error handling settings update:",a),o({success:!1,error:a.message})}),!0;case"ACCEPT_OFFER":return W((n=e.data)==null?void 0:n.offerId).then(a=>o({success:!0,result:a})).catch(a=>{console.error("Error handling accept offer:",a),o({success:!1,error:a.message})}),!0;case"REJECT_OFFER":return te((s=e.data)==null?void 0:s.offerId).then(a=>o({success:!0,result:a})).catch(a=>{console.error("Error handling reject offer:",a),o({success:!1,error:a.message})}),!0;case"OPERATION_DETAILS_UPDATE":return _e(e.data).then(()=>o({success:!0})).catch(a=>{console.error("Error handling operation details update:",a),o({success:!1,error:a.message})}),!0;case"OPERATION_ACCEPTED":return be(e.data).then(()=>o({success:!0})).catch(a=>{console.error("Error handling operation accepted:",a),o({success:!1,error:a.message})}),!0;case"OPERATION_NOT_AVAILABLE":return Se(e.data).then(()=>o({success:!0})).catch(a=>{console.error("Error handling operation not available:",a),o({success:!1,error:a.message})}),!0;case"OPERATION_ACCEPT_ERROR":return Re(e.data).then(()=>o({success:!0})).catch(a=>{console.error("Error handling operation accept error:",a),o({success:!1,error:a.message})}),!0;case"SEND_TELEGRAM_OPERATION_UPDATE":return Ie(e.data).then(()=>o({success:!0})).catch(a=>{console.error("Error sending Telegram operation update:",a),o({success:!1,error:a.message})}),!0;case"OPERATION_DECLINED":return Ne(e.data).then(()=>o({success:!0})).catch(a=>{console.error("Error handling operation declined:",a),o({success:!1,error:a.message})}),!0;case"OPERATION_DECLINE_ERROR":return Pe(e.data).then(()=>o({success:!0})).catch(a=>{console.error("Error handling operation decline error:",a),o({success:!1,error:a.message})}),!0;case"GET_POPUP_DATA":return ue().then(a=>o({success:!0,data:a})).catch(a=>{console.error("Error handling get popup data:",a),o({success:!1,error:a.message})}),!0;case"PING":console.log("Ping received, responding with alive status"),o({success:!0,message:"Background script is alive",timestamp:new Date().toISOString()});break;default:console.warn("Unknown message type:",e.type),o({success:!1,error:"Unknown message type: "+e.type})}}catch(a){console.error("Error in message listener:",a),o({success:!1,error:"Internal error: "+(a instanceof Error?a.message:"Unknown error")})}}),chrome.runtime.onConnect.addListener(e=>{console.log("Port connected:",e.name),e.onDisconnect.addListener(()=>{console.log("Port disconnected:",e.name),chrome.runtime.lastError&&console.error("Port disconnect error:",chrome.runtime.lastError)})}),chrome.commands&&chrome.commands.onCommand&&chrome.commands.onCommand.addListener(e=>{console.log("Chrome command received:",e),chrome.tabs.query({url:"*://app.airtm.com/peer-transfers/available*"},t=>{t.length>0&&t[0].id?chrome.tabs.sendMessage(t[0].id,{type:"EXECUTE_COMMAND",command:e}).catch(o=>{console.log("Could not send command to content script:",o)}):console.log("No active Airtm tab found for command:",e)})})}async function ue(){try{const t=(await chrome.storage.local.get("current_offers")).current_offers||[];return console.log("Sending popup data:",{offersCount:t.length,settings:c,stats:T}),{offers:t,settings:c,stats:T}}catch(e){throw console.error("Error getting popup data:",e),e}}function de(){const e=Date.now();if(e-K<se)return;const t=e-ie;let o=0;for(const[r,n]of I.entries())n<t&&(I.delete(r),o++);K=e,o>0&&(console.log(`🧹 Cleaned up ${o} old notified offers (older than 1 hour)`),Q())}function me(e){return I.has(e)}function ge(e){I.set(e,Date.now()),Q()}async function fe(e){try{const{offers:t,timestamp:o}=e;if(!Array.isArray(t))throw new Error("Invalid offers data");if(console.log("Processing "+t.length+" offers"),await chrome.storage.local.set({current_offers:t}),de(),!c.monitoring){console.log("Monitoring disabled, skipping offer processing"),H(t),T.lastUpdate=o||new Date().toISOString();try{chrome.runtime.sendMessage({type:"OFFERS_PROCESSED",data:{offers:t,stats:T}}).catch(s=>{console.log("Could not notify popup (popup may be closed):",s.message)})}catch(s){console.log("Error sending OFFERS_PROCESSED message:",s)}return}H(t);const r=pe(t);console.log(r.length+" offers passed filters");const n=r.filter(s=>!me(s.id));console.log(n.length+" new offers (not previously notified)"),n.length>0&&c.soundEnabled&&c.notificationsEnabled&&(console.log("Playing sound notification once for "+n.length+" new offers"),await ee());for(const s of n)ge(s.id),await ye(s,!1);T.lastUpdate=o||new Date().toISOString();try{chrome.runtime.sendMessage({type:"OFFERS_PROCESSED",data:{offers:t,stats:T}}).catch(s=>{console.log("Could not notify popup (popup may be closed):",s.message)})}catch(s){console.log("Error sending OFFERS_PROCESSED message:",s)}}catch(t){console.error("Error handling offers update:",t)}}function pe(e){return e.filter(t=>{var r,n,s,a,u,y;const o=typeof t.grossAmount=="string"?parseFloat(t.grossAmount):t.grossAmount;if(o<c.minAmount||o>c.maxAmount)return!1;if(c.preferredCurrencies.length>0){const l=((r=t.currency)==null?void 0:r.symbol)||((n=t.walletCurrency)==null?void 0:n.symbol)||"";if(!c.preferredCurrencies.includes(l))return!1}if(c.paymentMethods.length>0){const l=((u=(a=(s=t.makerPaymentMethod)==null?void 0:s.version)==null?void 0:a.category)==null?void 0:u.translationTag)||"";if(c.fuzzyMatching.enabled){const i=q.matchAnyPaymentMethod(l,c.paymentMethods);if(i.isMatch)console.log(`Offer ${t.id} matched payment method "${l}" with alias "${i.matchedAlias}" (score: ${i.score.toFixed(2)})`);else return console.log(`Offer ${t.id} filtered out: payment method "${l}" doesn't match any configured methods (best score: ${i.score.toFixed(2)})`),!1}else{let i=l;if(i.startsWith("CATEGORY_TREE:AIRTM_")&&(i=i.replace("CATEGORY_TREE:AIRTM_","")),i.startsWith("E_TRANSFER_")&&(i=i.replace("E_TRANSFER_","")),!c.paymentMethods.some(d=>i.includes(d)))return console.log(`Offer ${t.id} filtered out: payment method "${i}" doesn't match any configured methods (legacy matching)`),!1}}if(c.countries.length>0){const l=((y=t.peer)==null?void 0:y.country)||"";if(!c.countries.includes(l))return!1}if(c.keywords.length>0){const l=JSON.stringify(t).toLowerCase();if(!c.keywords.some(i=>l.includes(i.toLowerCase())))return!1}if(c.blacklistKeywords.length>0){const l=JSON.stringify(t).toLowerCase();if(c.blacklistKeywords.some(i=>l.includes(i.toLowerCase())))return console.log(`Offer ${t.id} filtered out by blacklist keyword`),!1}return!0})}async function he(e){try{const t=await chrome.tabs.query({url:"*://app.airtm.com/peer-transfers/available*"});if(t.length===0){console.log("No Airtm tab found, opening new tab");const r=await chrome.tabs.create({url:"https://app.airtm.com/peer-transfers/available",active:!0});if(await new Promise(n=>setTimeout(n,3e3)),r.windowId)try{await chrome.windows.update(r.windowId,{focused:!0,state:"maximized"}),console.log("✅ New window maximized and focused")}catch(n){console.warn("⚠️ Failed to maximize new window:",n);try{await chrome.windows.update(r.windowId,{focused:!0}),console.log("✅ New window focused (without maximizing)")}catch(s){console.error("❌ Failed to focus new window:",s)}}return}const o=t[0];if(o.windowId){try{await chrome.windows.update(o.windowId,{focused:!0,state:"maximized"}),console.log("✅ Window maximized and focused for offer:",e.id)}catch(n){console.warn("⚠️ Failed to maximize window:",n);try{await chrome.windows.update(o.windowId,{focused:!0}),console.log("✅ Window focused (without maximizing) for offer:",e.id)}catch(s){console.error("❌ Failed to focus window:",s)}}let r=!1;for(let n=1;n<=3;n++)try{await chrome.tabs.update(o.id,{active:!0}),console.log(`✅ Tab activated (attempt ${n}) for offer:`,e.id),r=!0;break}catch(s){console.warn(`⚠️ Tab activation attempt ${n} failed:`,s),n<3&&await new Promise(a=>setTimeout(a,500))}r||console.error("❌ Failed to activate tab after 3 attempts"),await new Promise(n=>setTimeout(n,1e3));try{await chrome.tabs.sendMessage(o.id,{type:"FOCUS_OFFER",data:{offerId:e.id}}),console.log("✅ Focus message sent to content script for offer:",e.id)}catch(n){console.log("⚠️ Could not send focus message to content script:",n)}}else console.error("❌ No window ID found for Airtm tab")}catch(t){console.error("❌ Error maximizing window and focusing offer:",t)}}async function ye(e,t=!0){try{console.log("Processing offer "+e.id),await he(e),c.notificationsEnabled&&await we(e,t),console.log("Checking Telegram settings:",{botToken:c.telegramBotToken?"***SET***":"NOT SET",chatId:c.telegramChatId?"***SET***":"NOT SET"}),c.telegramBotToken&&c.telegramChatId?(console.log("Sending Telegram message for offer:",e.id),await U(e)):console.log("Telegram not configured - skipping message"),c.webhookUrl?(console.log("Sending webhook message for offer:",e.id),await Ae(e)):console.log("Webhook not configured - skipping webhook"),c.autoAccept&&await W(e.id)}catch(o){console.error("Error processing offer "+e.id+":",o)}}async function we(e,t=!0){var o,r;try{Y.updateSettings({autoCloseDelay:3e4,playSound:c.soundEnabled&&t}),await Y.showNotification(e)}catch(n){console.error("Error sending notification:",n);try{const s=v(e),a="New "+e.operationType+" Offer";let u=`${s.amount} ${s.currency}`;s.conversionNote&&(u+=` ${s.conversionNote}`),await chrome.notifications.create({type:"basic",iconUrl:"icons/icon48.png",title:a,message:u,contextMessage:"From: "+(((o=e.peer)==null?void 0:o.firstName)||"")+" "+(((r=e.peer)==null?void 0:r.lastName)||""),buttons:[{title:"Accept"},{title:"Reject"}]}),c.soundEnabled&&t&&await ee()}catch(s){console.error("Fallback notification also failed:",s)}}}async function U(e){try{const t=typeof e=="string"?e:F(e),o="https://api.telegram.org/bot"+c.telegramBotToken+"/sendMessage";console.log("Sending Telegram message to:",c.telegramChatId),console.log("Message content:",t);const r=await fetch(o,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({chat_id:c.telegramChatId,text:t,parse_mode:"Markdown"})}),n=await r.json();r.ok?console.log("✅ Telegram message sent successfully:",n):console.error("❌ Telegram API error:",n)}catch(t){console.error("❌ Error sending Telegram message:",t)}}async function Ae(e){try{const t={timestamp:new Date().toISOString(),offer:e,event:"new_offer",metadata:{extensionVersion:"1.0.0",source:"airtm-monitor-pro"}};console.log("Sending webhook to:",c.webhookUrl),console.log("Webhook payload:",t);const o=await fetch(c.webhookUrl,{method:"POST",headers:{"Content-Type":"application/json","User-Agent":"Airtm-Monitor-Pro/1.0.0"},body:JSON.stringify(t)});o.ok?console.log("✅ Webhook sent successfully:",o.status):console.error("❌ Webhook failed with status:",o.status,await o.text())}catch(t){console.error("❌ Error sending webhook:",t)}}function Ee(e){return{USA:"🇺🇸",US:"🇺🇸",IND:"🇮🇳",IN:"🇮🇳",GBR:"🇬🇧",GB:"🇬🇧",UK:"🇬🇧",CAN:"🇨🇦",CA:"🇨🇦",AUS:"🇦🇺",AU:"🇦🇺",DEU:"🇩🇪",DE:"🇩🇪",GER:"🇩🇪",FRA:"🇫🇷",FR:"🇫🇷",ESP:"🇪🇸",ES:"🇪🇸",ITA:"🇮🇹",IT:"🇮🇹",BRA:"🇧🇷",BR:"🇧🇷",MEX:"🇲🇽",MX:"🇲🇽",ARG:"🇦🇷",AR:"🇦🇷",COL:"🇨🇴",CO:"🇨🇴",VEN:"🇻🇪",VE:"🇻🇪",PER:"🇵🇪",PE:"🇵🇪",CHL:"🇨🇱",CL:"🇨🇱",URY:"🇺🇾",UY:"🇺🇾",ECU:"🇪🇨",EC:"🇪🇨",BOL:"🇧🇴",BO:"🇧🇴",PRY:"🇵🇾",PY:"🇵🇾",CHN:"🇨🇳",CN:"🇨🇳",JPN:"🇯🇵",JP:"🇯🇵",KOR:"🇰🇷",KR:"🇰🇷",RUS:"🇷🇺",RU:"🇷🇺",TUR:"🇹🇷",TR:"🇹🇷",EGY:"🇪🇬",EG:"🇪🇬",ZAF:"🇿🇦",ZA:"🇿🇦",NGA:"🇳🇬",NG:"🇳🇬",KEN:"🇰🇪",KE:"🇰🇪",GHA:"🇬🇭",GH:"🇬🇭",MAR:"🇲🇦",MA:"🇲🇦",TUN:"🇹🇳",TN:"🇹🇳",DZA:"🇩🇿",DZ:"🇩🇿",MOZ:"🇲🇿",MZ:"🇲🇿",PAN:"🇵🇦",PA:"🇵🇦",IDN:"🇮🇩",ID:"🇮🇩",THA:"🇹🇭",TH:"🇹🇭",VNM:"🇻🇳",VN:"🇻🇳",PHL:"🇵🇭",PH:"🇵🇭",MYS:"🇲🇾",MY:"🇲🇾",SGP:"🇸🇬",SG:"🇸🇬",PAK:"🇵🇰",PK:"🇵🇰",BGD:"🇧🇩",BD:"🇧🇩",LKA:"🇱🇰",LK:"🇱🇰",NPL:"🇳🇵",NP:"🇳🇵",AFG:"🇦🇫",AF:"🇦🇫",IRN:"🇮🇷",IR:"🇮🇷",IRQ:"🇮🇶",IQ:"🇮🇶",SAU:"🇸🇦",SA:"🇸🇦",ARE:"🇦🇪",AE:"🇦🇪",QAT:"🇶🇦",QA:"🇶🇦",KWT:"🇰🇼",KW:"🇰🇼",BHR:"🇧🇭",BH:"🇧🇭",OMN:"🇴🇲",OM:"🇴🇲",JOR:"🇯🇴",JO:"🇯🇴",LBN:"🇱🇧",LB:"🇱🇧",SYR:"🇸🇾",SY:"🇸🇾",ISR:"🇮🇱",IL:"🇮🇱",PSE:"🇵🇸",PS:"🇵🇸"}[e.toUpperCase()]||e}function X(e){var b,_,N,A,w,C,S,P,R,x,j;const t=e.walletCurrency,o=e.currency,r=e.makerPaymentMethod||e.takerPaymentMethod,n=((b=r==null?void 0:r.categoryId)==null?void 0:b.toLowerCase().includes("usdc"))||((A=(N=(_=r==null?void 0:r.version)==null?void 0:_.category)==null?void 0:N.translationTag)==null?void 0:A.toLowerCase().includes("usdc")),s=((w=e.metadata)==null?void 0:w.walletCurrencyPrecision)||(t==null?void 0:t.precision),a=((C=e.metadata)==null?void 0:C.localCurrencyPrecision)||(o==null?void 0:o.precision),u=s===6||a===6,y=((S=e.metadata)==null?void 0:S.isForThirdPartyPaymentMethod)===!1,l=((P=r==null?void 0:r.categoryId)==null?void 0:P.toLowerCase())||"",i=((j=(x=(R=r==null?void 0:r.version)==null?void 0:R.category)==null?void 0:x.translationTag)==null?void 0:j.toLowerCase())||"",d=l.includes("gift-card")||i.includes("gift_card"),g=l.includes("bank")||i.includes("bank"),f=l.includes("card")||i.includes("card"),h=l.includes("transfer")||i.includes("transfer"),m=d||g||f||h;let p=null,E=null;return e.operationType==="BUY"&&m?(p="withdrawal",E="wallet",{isUSDC:!1,usdcField:E,operationType:p}):n||u||y?(e.operationType==="SELL"&&s===6?(p="withdrawal",E="wallet"):e.operationType==="BUY"&&a===6?(p="deposit",E="local"):(s===6||a===6)&&(p="exchange",E=s===6?"wallet":"local"),{isUSDC:!0,usdcField:E,operationType:p}):{isUSDC:!1,usdcField:null,operationType:null}}function v(e){var t,o,r,n,s,a,u,y,l;try{const i=X(e);let d=((t=e.walletCurrency)==null?void 0:t.symbol)||"$",g=((o=e.currency)==null?void 0:o.symbol)||((r=e.walletCurrency)==null?void 0:r.symbol)||"Unknown";if(i.isUSDC&&(i.usdcField==="wallet"?d="USDC":i.usdcField==="local"&&(g="USDC")),(d===g||!e.currency)&&!i.isUSDC){const w=parseFloat(e.grossAmount||"0");return{amount:O(w),currency:d}}const f=e.rateInfo;if(f&&f.fundsToSendTaker&&f.fundsToReceiveTaker){let w,C,S="";return e.operationType==="BUY"?(w=parseFloat(f.fundsToReceiveTaker),C=parseFloat(f.fundsToSendTaker),i.operationType==="deposit"&&(S="(USDC deposit)")):(w=parseFloat(f.fundsToSendTaker),C=parseFloat(f.fundsToReceiveTaker),i.operationType==="withdrawal"&&(S=`(withdrawal, fees: $${(C-w).toFixed(2)})`)),console.log(`Using rateInfo amounts for ${e.operationType}: ${C} ${d} ↔ ${w} ${g} (from rateInfo)${i.isUSDC?" [USDC operation]":""}`),i.operationType==="withdrawal"?{amount:O(C),currency:d,originalAmount:O(w),originalCurrency:g,exchangeRate:(w/C).toFixed(4),conversionNote:S,operationType:"withdrawal"}:{amount:O(w),currency:g,originalAmount:O(C),originalCurrency:d,exchangeRate:(w/C).toFixed(4),conversionNote:S,operationType:i.operationType}}let h,m=null,p="";e.netAmount&&parseFloat(e.netAmount)>0?h=parseFloat(e.netAmount):h=parseFloat(e.grossAmount||"0");const E=e.metadata,b=e.takerPaymentMethod;if(e.rate?(m=parseFloat(e.rate),p="operation.rate"):(n=e.displayRate)!=null&&n.rate?(m=parseFloat(e.displayRate.rate),p="displayRate.rate"):f&&f.exchangeRate?(m=parseFloat(f.exchangeRate),p="rateInfo.exchangeRate"):(s=E==null?void 0:E.displayRateInfo)!=null&&s.exchangeRate?(m=parseFloat(E.displayRateInfo.exchangeRate),p="metadata.displayRateInfo.exchangeRate"):(a=b==null?void 0:b.rateInfo)!=null&&a.exchangeRate&&(m=parseFloat(b.rateInfo.exchangeRate),p="takerPaymentMethod.rateInfo.exchangeRate"),(!m||m<=0)&&i.isUSDC&&(m=1,p="usdc_parity_default"),!m||m<=0)return{amount:O(h),currency:d,conversionNote:"(rate pending)"};let _;const N=((u=e.displayRate)==null?void 0:u.direction)||"TO_LOCAL_CURRENCY";N==="TO_LOCAL_CURRENCY"?_=h*m:N==="FROM_LOCAL_CURRENCY"?_=h/m:(e.operationType,_=h*m),console.log(`Currency conversion: ${h} ${d} → ${_} ${g} (rate: ${m}, source: ${p}, direction: ${N})${i.isUSDC?" [USDC operation]":""}`);const A={amount:O(_),currency:g,originalAmount:O(h),originalCurrency:d,exchangeRate:m.toString()};return i.isUSDC&&(A.operationType=i.operationType,i.operationType==="withdrawal"?(A.amount=O(h),A.currency=d,A.originalAmount=O(_),A.originalCurrency=g,A.conversionNote=`(withdrawal to ${g})`):i.operationType==="deposit"?A.conversionNote=`(deposit from ${d})`:i.operationType==="exchange"&&(A.conversionNote="(USDC exchange)")),A}catch(i){console.error("Error in currency conversion:",i);const d=parseFloat(e.grossAmount||"0"),g=((y=e.currency)==null?void 0:y.symbol)||((l=e.walletCurrency)==null?void 0:l.symbol)||"Unknown";return{amount:O(d),currency:g,conversionNote:"(conversion error)"}}}function O(e){return e>=1e3?e.toLocaleString("en-US",{minimumFractionDigits:0,maximumFractionDigits:2}):e.toFixed(2).replace(/\.?0+$/,"")}function Ce(){var m,p;console.log("🧪 Testing currency conversion with afteraccepting.txt example...");const e={id:"0d77a20b-f2ab-4dd6-8924-9fd102a37652",hash:"F2ABQP4DD6DK8924",operationType:"SELL",status:"ACCEPTED",isMine:!1,createdAt:"2025-06-12T16:23:51.645Z",updatedAt:"2025-06-12T16:24:00.386Z",grossAmount:"11",netAmount:"10.38",metadata:{isForThirdPartyPaymentMethod:!0,localCurrencyPrecision:2,walletCurrencyPrecision:2},walletCurrency:{symbol:"$",id:"USD",name:"US Dollar",precision:2,__typename:"Catalogs__Currency"},currency:{symbol:"£",id:"EGP",name:"Egyptian Pound",precision:2,__typename:"Catalogs__Currency"},peer:{id:"90e670af-de7f-45ba-8de2-e2698558271d",firstName:"احمد",lastName:"محمد السيد عبدالحافظ",createdAt:"2022-04-23T18:23:27.468Z",country:"EGY",countryInfo:{id:"EGY",__typename:"Catalogs__Country"},numbers:{id:"90e670af-de7f-45ba-8de2-e2698558271d",score:4.98,completedOperations:162,__typename:"Numbers__User"},__typename:"Auth__OperationUser"},rate:"49.57",displayRate:{rate:"47.42396313364055299539",direction:"TO_LOCAL_CURRENCY",__typename:"Operations__DisplayRate"},rateInfo:{fundsToSendTaker:"514.55",fundsToReceiveTaker:"10.85",grossAmount:"545.28",netAmount:"514.55",exchangeRate:"49.5709"},__typename:"Operations__Sell"},t=v(e);console.log("📊 Currency Conversion Test Results:"),console.log(`   Input: ${e.grossAmount} ${(m=e.walletCurrency)==null?void 0:m.symbol} (gross)`),console.log(`   Input: ${e.netAmount} ${(p=e.walletCurrency)==null?void 0:p.symbol} (net)`),console.log("   Expected Local: 514.55 £"),console.log("   Expected Wallet: 10.85 $"),console.log(`   Actual Result: ${t.amount} ${t.currency}`),console.log(`   Original Amount: ${t.originalAmount} ${t.originalCurrency}`),console.log(`   Exchange Rate: ${t.exchangeRate}`),console.log(`   Conversion Note: ${t.conversionNote||"None"}`);const o=514.55,r=parseFloat(t.amount.replace(/,/g,"")),n=Math.abs(r-o)<.01;console.log(`✅ Test Result: ${n?"PASSED":"FAILED"}`),n||console.log(`   Expected: ${o}, Got: ${r}`);const s=F(e);console.log("📱 Telegram Message:"),console.log(s),console.log(`
🧪 Testing same-currency scenario (USD to USD)...`);const a={id:"055c2bee-d063-4ced-acac-27b623954fa5",hash:"D063YU4CEDVGACAC",operationType:"SELL",status:"CREATED",isMine:!1,createdAt:"2025-06-14T13:19:58.291Z",updatedAt:null,grossAmount:"62.16",netAmount:"58.87",metadata:{isForThirdPartyPaymentMethod:!0,localCurrencyPrecision:2,walletCurrencyPrecision:2},walletCurrency:{symbol:"$",id:"USD",name:"US Dollar",precision:2,__typename:"Catalogs__Currency"},currency:{symbol:"$",id:"USD",name:"US Dollar",precision:2,__typename:"Catalogs__Currency"},peer:{id:"8021567e-1d57-446e-ae65-ffe04f622241",firstName:"IRIS DANIELA",lastName:"S.",createdAt:"2025-03-17T00:20:06.523Z",country:"PAN",countryInfo:{id:"PAN",__typename:"Catalogs__Country"},numbers:{id:"8021567e-1d57-446e-ae65-ffe04f622241",score:4.47,completedOperations:32,__typename:"Numbers__User"},__typename:"Auth__OperationUser"},rate:"1",rateInfo:{fundsToReceiveTaker:"61.23",fundsToSendTaker:"58.86"},displayRate:{direction:"TO_WALLET_CURRENCY",rate:"1.04026503567787971458",__typename:"Operations__DisplayRate"},makerPaymentMethod:{version:{category:{translationTag:"CATEGORY_TREE:AIRTM_MOBILE_YAPPY"}}},__typename:"Operations__Sell"},u=v(a);console.log("📊 Same Currency Test Results:"),console.log("   Expected: 62.16 $ (no conversion)"),console.log(`   Actual Result: ${u.amount} ${u.currency}`),console.log(`   Original Amount: ${u.originalAmount||"None"}`),console.log(`   Conversion Note: ${u.conversionNote||"None"}`);const y=F(a);console.log("📱 Same Currency Telegram Message:"),console.log(y),console.log(`
🧪 Testing BUY operation scenario (CNY to USD)...`);const l={id:"8d0a8483-3ea0-43b9-9a93-c48f22abe919",hash:"3EA0YE43B9EC9A93",operationType:"BUY",status:"CREATED",isMine:!1,createdAt:"2025-06-14T13:49:43.184Z",updatedAt:null,grossAmount:"278.97",netAmount:"269.84",metadata:{isForThirdPartyPaymentMethod:null,localCurrencyPrecision:2,walletCurrencyPrecision:2},walletCurrency:{symbol:"$",id:"USD",name:"US Dollar",precision:2,__typename:"Catalogs__Currency"},currency:{symbol:"¥",id:"CNY",name:"Chinese Yuan",precision:2,__typename:"Catalogs__Currency"},peer:{id:"d37923bd-23c8-4f2c-99fc-15c665e090ea",firstName:"ALY ADRIANO",lastName:"S.",createdAt:"2025-02-11T17:11:19.413Z",country:"MOZ",countryInfo:{id:"MOZ",__typename:"Catalogs__Country"},numbers:{id:"d37923bd-23c8-4f2c-99fc-15c665e090ea",score:4.28,completedOperations:82,__typename:"Numbers__User"},__typename:"Auth__OperationUser"},rate:"7.1692",rateInfo:{fundsToReceiveTaker:"2000",fundsToSendTaker:"273.75",netAmount:"1934.55",grossAmount:"2000"},displayRate:{direction:"TO_LOCAL_CURRENCY",rate:"7.30593607305936073059",__typename:"Operations__DisplayRate"},makerPaymentMethod:{version:{category:{translationTag:"CATEGORY_TREE:AIRTM_E_TRANSFER_ALIPAY"}}},__typename:"Operations__Buy"},i=v(l);console.log("📊 BUY Operation Test Results:"),console.log("   Expected: 2,000 ¥ (273.75 $)"),console.log(`   Actual Result: ${i.amount} ${i.currency}`),console.log(`   Original Amount: ${i.originalAmount} ${i.originalCurrency}`),console.log(`   Exchange Rate: ${i.exchangeRate}`);const d=F(l);console.log("📱 BUY Operation Telegram Message:"),console.log(d),console.log(`
🧪 Testing USDC withdrawal scenario...`);const g={id:"withdrawal-001",hash:"WITHDRAWAL001",operationType:"SELL",status:"CREATED",isMine:!0,createdAt:"2025-06-14T15:30:00.000Z",updatedAt:null,grossAmount:"14.23",netAmount:"13.85",metadata:{isForThirdPartyPaymentMethod:!1,walletCurrencyPrecision:6,localCurrencyPrecision:2},walletCurrency:{symbol:"$",id:"USD",name:"US Dollar",precision:6,__typename:"Catalogs__Currency"},currency:{symbol:"$",id:"USD",name:"US Dollar",precision:2,__typename:"Catalogs__Currency"},peer:{id:"withdrawal-peer",firstName:"Bank",lastName:"Withdrawal",createdAt:"2025-01-01T00:00:00.000Z",country:"USA",countryInfo:{id:"USA",__typename:"Catalogs__Country"},numbers:{id:"withdrawal-peer",score:5,completedOperations:1e3,__typename:"Numbers__User"},__typename:"Auth__OperationUser"},rate:"1.0",rateInfo:{fundsToReceiveTaker:"14.23",fundsToSendTaker:"13.50",grossAmount:"14.23",netAmount:"13.85"},displayRate:{direction:"TO_LOCAL_CURRENCY",rate:"1.0",__typename:"Operations__DisplayRate"},makerPaymentMethod:{categoryId:"airtm:bank:withdrawal",version:{category:{translationTag:"CATEGORY_TREE:AIRTM_BANK_WITHDRAWAL"}}},__typename:"Operations__Sell"},f=v(g);console.log("📊 USDC Withdrawal Test Results:"),console.log("   Expected: -14.23 USDC → 13.50 USD (fees: $0.73)"),console.log(`   Actual Result: ${f.amount} ${f.currency}`),console.log(`   Original Amount: ${f.originalAmount} ${f.originalCurrency}`),console.log(`   Operation Type: ${f.operationType}`),console.log(`   Conversion Note: ${f.conversionNote||"None"}`);const h=F(g);console.log("📱 USDC Withdrawal Telegram Message:"),console.log(h),console.log(`
🧪 Currency conversion tests completed.`)}function F(e){var h,m,p,E,b,_,N,A,w,C,S;const t=v(e),o=X(e),r=((((h=e.peer)==null?void 0:h.firstName)||"")+" "+(((m=e.peer)==null?void 0:m.lastName)||"")).trim(),n=((E=(p=e.peer)==null?void 0:p.numbers)==null?void 0:E.score)||0,s=((_=(b=e.peer)==null?void 0:b.numbers)==null?void 0:_.completedOperations)||0,a=((N=e.peer)==null?void 0:N.country)||"Unknown",u=Ee(a);let l=((C=(w=(A=e.makerPaymentMethod)==null?void 0:A.version)==null?void 0:w.category)==null?void 0:C.translationTag)||"Unknown";l.startsWith("CATEGORY_TREE:AIRTM_")&&(l=l.replace("CATEGORY_TREE:AIRTM_","")),l.startsWith("E_TRANSFER_")&&(l=l.replace("E_TRANSFER_","")),l.startsWith("GIFT_CARD_")&&(l=l.replace("GIFT_CARD_","")),l=l.replace(/_/g," ").toLowerCase().replace(/\b\w/g,P=>P.toUpperCase());const i=((S=e.makerPaymentMethod)==null?void 0:S.categoryId)||"";if(i.includes("gift-card")){const P=i.split(":");if(P.length>2){let R=P[P.length-1].replace(/[-_]/g," ").replace(/\b\w/g,x=>x.toUpperCase());R.toLowerCase()==="ebay"&&(R="eBay"),R.toLowerCase()==="paypal"&&(R="PayPal"),R.toLowerCase()==="amazon"&&(R="Amazon"),l=`${R} Gift Card`}}let d="";o.operationType==="withdrawal"?(d=`-${t.amount} ${t.currency}`,t.originalAmount&&t.originalCurrency&&(d+=` → ${t.originalAmount} ${t.originalCurrency}`)):(d=`${t.amount} ${t.currency}`,t.originalAmount&&t.originalCurrency&&t.originalCurrency!==t.currency&&(d+=` (${t.originalAmount} ${t.originalCurrency})`)),t.conversionNote&&(d+=` ${t.conversionNote}`);let g="";const f=i.includes("gift-card");return o.operationType==="withdrawal"?f?g="🎁 ":g="💸 ":o.operationType==="deposit"?g="💰 ":o.isUSDC&&(g="🪙 "),`${g}${d} : ${l} (${u})
👤 User: ${r} ${n}⭐(${s} trades)`}let D=!1,k=null;async function ee(){return k||(k=(async()=>{var e;try{if(!D)try{await chrome.offscreen.createDocument({url:"sounds/beep.html",reasons:[chrome.offscreen.Reason.AUDIO_PLAYBACK],justification:"Play notification sound for new offers"}),D=!0}catch(t){if((e=t.message)!=null&&e.includes("Only a single offscreen document may be created"))D=!0;else throw t}try{await chrome.runtime.sendMessage({type:"PLAY_SOUND"})}catch(t){throw console.log("Message to offscreen document failed:",t),D=!1,t}}catch(t){console.error("Error playing notification sound:",t),D=!1}finally{try{D&&(await chrome.offscreen.closeDocument(),D=!1)}catch{D=!1}k=null}})(),k)}async function W(e){try{if(!J())throw console.warn("⚠️ Extension context invalid during offer acceptance, cannot proceed"),new Error("Extension context is invalid. Please reload the extension.");const t=await chrome.tabs.query({url:"*://app.airtm.com/peer-transfers/available*"});if(t.length===0)throw new Error("Airtm tab not found");const o=await chrome.tabs.sendMessage(t[0].id,{type:"ACCEPT_OFFER",data:{offerId:e}});if(o!=null&&o.success)return T.acceptedOffers++,console.log("Offer "+e+" accepted successfully"),Te(e,t[0].id),!0;throw new Error((o==null?void 0:o.error)||"Failed to accept offer")}catch(t){if(console.error("Error accepting offer "+e+":",t),V(t)&&(console.warn("⚠️ Connection error detected, likely due to extension context invalidation"),c.telegramBotToken&&c.telegramChatId))try{await U(`⚠️ Extension Context Issue

Failed to accept offer ${e}
Reason: Extension context lost

Please reload the extension or refresh the page.`)}catch(o){console.error("Failed to send Telegram notification about context issue:",o)}throw t}}async function _e(e){try{console.log("📋 Processing operation details update:",e);const{operation:t,timestamp:o,source:r}=e;if(!t||!t.id){console.warn("Invalid operation data received");return}await chrome.storage.local.set({[`operation_${t.id}`]:{...t,lastUpdated:o,source:r}}),c.telegramBotToken&&c.telegramChatId&&await Oe(t),console.log(`✅ Operation ${t.id} details updated - Status: ${t.status}`)}catch(t){console.error("❌ Error handling operation details update:",t)}}function Te(e,t){console.log(`🔍 Starting URL monitoring for accepted offer: ${e}`);let o=0;const r=30,n=setInterval(async()=>{try{o++;const a=(await chrome.tabs.get(t)).url;if(console.log(`🔍 URL check ${o}/${r}: ${a}`),a&&/https:\/\/app\.airtm\.com\/operations\/[A-Z0-9]+/.test(a)){console.log("✅ Operation URL detected:",a);const y=a.split("/operations/")[1];c.telegramBotToken&&c.telegramChatId&&await U(`🎯 Offer ${e} accepted successfully!
📋 Operation ID: ${y}
🔗 URL: ${a}
⏰ Status: Accepted`),await chrome.storage.local.set({[`accepted_operation_${e}`]:{operationId:y,url:a,timestamp:new Date().toISOString(),status:"accepted"}}),clearInterval(n);return}o>=r&&(console.log("⚠️ Operation URL not detected within timeout"),c.telegramBotToken&&c.telegramChatId&&await U(`❌ Offer ${e} acceptance failed or timed out
⏰ Status: Not Available`),await chrome.storage.local.set({[`failed_operation_${e}`]:{timestamp:new Date().toISOString(),status:"not_available",reason:"url_not_detected"}}),clearInterval(n))}catch(s){console.error("❌ Error during URL monitoring:",s),clearInterval(n)}},100)}async function Oe(e){var t;try{const o=(t=e.metadata)==null?void 0:t.displayRateInfo,r={id:e.id||"",hash:e.hash||"",operationType:e.operationType||"UNKNOWN",status:e.status||"UNKNOWN",isMine:e.isMine||!1,createdAt:e.createdAt||"",updatedAt:e.updatedAt||null,grossAmount:e.grossAmount||"0",netAmount:e.netAmount||"0",metadata:{...e.metadata,displayRateInfo:o},walletCurrency:e.walletCurrency||{symbol:"$"},peer:e.secondParty||e.peer||{firstName:"Unknown",lastName:""},rate:e.rate,displayRate:e.displayRate,rateInfo:{...e.rateInfo},currency:e.currency,makerPaymentMethod:e.makerPaymentMethod,__typename:e.__typename||"Operations__Unknown"},n=v(r),s=e.secondParty?`${e.secondParty.firstName||""} ${e.secondParty.lastName||""}`.trim():e.peer?`${e.peer.firstName||""} ${e.peer.lastName||""}`.trim():"Unknown";let a=`${n.amount} ${n.currency}`;n.conversionNote&&(a+=` ${n.conversionNote}`),n.originalAmount&&n.originalCurrency&&n.originalCurrency!==n.currency&&(a+=` (${n.originalAmount} ${n.originalCurrency})`);const u=`📋 Operation Update
🆔 ID: ${e.hash||e.id}
📊 Status: ${e.status}
💰 Amount: ${a}
🔄 Type: ${e.operationType}
👤 Partner: ${s}
⏰ Updated: ${new Date().toLocaleString()}`;await U(u)}catch(o){console.error("❌ Error sending Telegram operation update:",o)}}async function be(e){try{console.log("✅ Processing operation acceptance:",e),await chrome.storage.local.set({[`accepted_operation_${e.operationId}`]:{...e,processedAt:new Date().toISOString()}}),T.acceptedOffers++,console.log("✅ Operation acceptance processed successfully")}catch(t){console.error("❌ Error handling operation accepted:",t)}}async function Se(e){try{console.log("🚫 Processing operation not available:",e),await chrome.storage.local.set({[`unavailable_operation_${e.operationId||"unknown"}`]:{...e,processedAt:new Date().toISOString()}}),console.log("🚫 Operation not available processed successfully")}catch(t){console.error("❌ Error handling operation not available:",t)}}async function Re(e){try{console.log("⚠️ Processing operation accept error:",e),await chrome.storage.local.set({[`error_operation_${Date.now()}`]:{...e,processedAt:new Date().toISOString()}}),console.log("⚠️ Operation accept error processed successfully")}catch(t){console.error("❌ Error handling operation accept error:",t)}}async function Ne(e){try{console.log("🚫 Processing operation decline:",e),await chrome.storage.local.set({[`declined_operation_${Date.now()}`]:{...e,processedAt:new Date().toISOString()}}),T.rejectedOffers++,console.log("🚫 Operation decline processed successfully")}catch(t){console.error("❌ Error handling operation declined:",t)}}async function Pe(e){try{console.log("⚠️ Processing operation decline error:",e),await chrome.storage.local.set({[`decline_error_operation_${Date.now()}`]:{...e,processedAt:new Date().toISOString()}}),console.log("⚠️ Operation decline error processed successfully")}catch(t){console.error("❌ Error handling operation decline error:",t)}}async function Ie(e){try{console.log("📱 Sending Telegram operation status update:",e);const{operationId:t,status:o}=e;let r;o==="accepted"?r=`✅ *Operation Accepted*

🆔 Operation ID: ${t}
📊 Status: Successfully accepted
⏰ Time: ${new Date().toLocaleString()}

🎉 Great! The operation was accepted successfully.`:o==="Not Available"?r=`🚫 *Operation Not Available*

🆔 Operation ID: ${t}
📊 Status: No longer available
⏰ Time: ${new Date().toLocaleString()}

😔 The operation was taken by someone else or expired.`:r=`📋 *Operation Update*

🆔 Operation ID: ${t}
📊 Status: ${o}
⏰ Time: ${new Date().toLocaleString()}`,await U(r),console.log("📱 Telegram operation status update sent successfully")}catch(t){console.error("❌ Error sending Telegram operation status update:",t)}}async function te(e){try{if(!J())throw console.warn("⚠️ Extension context invalid during offer rejection, cannot proceed"),new Error("Extension context is invalid. Please reload the extension.");const t=await chrome.tabs.query({url:"*://app.airtm.com/peer-transfers/available*"});if(t.length===0)throw new Error("Airtm tab not found");const o=await chrome.tabs.sendMessage(t[0].id,{type:"REJECT_OFFER",data:{offerId:e}});if(o!=null&&o.success)return T.rejectedOffers++,console.log("Offer "+e+" rejected successfully"),!0;throw new Error((o==null?void 0:o.error)||"Failed to reject offer")}catch(t){if(console.error("Error rejecting offer "+e+":",t),V(t)&&(console.warn("⚠️ Connection error detected, likely due to extension context invalidation"),c.telegramBotToken&&c.telegramChatId))try{await U(`⚠️ Extension Context Issue

Failed to reject offer ${e}
Reason: Extension context lost

Please reload the extension or refresh the page.`)}catch(o){console.error("Failed to send Telegram notification about context issue:",o)}throw t}}function H(e){T.totalOffers=e.length,T.newOffers=e.filter(t=>{const o=new Date(t.createdAt),r=new Date(Date.now()-5*60*1e3);return o>r}).length,T.averageRate=0}async function De(){try{const e=await chrome.storage.sync.get("settings");c={...L,...e.settings},c.monitoring=!0,z(),await chrome.storage.sync.set({settings:c}),console.log("Settings loaded and monitoring enabled:",c)}catch(e){console.error("Error loading settings:",e),c={...L,monitoring:!0},z()}}async function $e(e){try{c={...c,...e},e.fuzzyMatching&&z(),await chrome.storage.sync.set({settings:c}),console.log("Settings updated:",c),oe()}catch(t){throw console.error("Error updating settings:",t),t}}function oe(){c.monitoring?(chrome.action.setBadgeText({text:"ON"}),chrome.action.setBadgeBackgroundColor({color:"#10b981"})):(chrome.action.setBadgeText({text:"OFF"}),chrome.action.setBadgeBackgroundColor({color:"#6b7280"}))}function z(){try{const e=c.fuzzyMatching||{enabled:!1,threshold:.8,enableAliases:!0,customAliases:{}};q.updateConfig({threshold:e.threshold,enableAliases:e.enableAliases,customAliases:e.customAliases}),console.log("Fuzzy matcher configuration updated:",e)}catch(e){console.error("Error updating fuzzy matcher configuration:",e)}}function ve(){chrome.alarms.onAlarm.addListener(e=>{switch(console.log("Alarm triggered:",e.name),e.name){case"monitoring-check":break;case"cleanup-storage":Me();break}}),chrome.alarms.create("cleanup-storage",{periodInMinutes:60})}function Ue(){chrome.notifications.onButtonClicked.addListener(async(e,t)=>{try{const o=e.match(/offer_(.+)_\d+/),r=o?o[1]:e;t===0?await W(r):t===1&&await te(r),chrome.notifications.clear(e)}catch(o){console.error("Error handling notification button click:",o)}}),chrome.runtime.onMessage.addListener((e,t,o)=>{(e.type==="ACCEPT_OFFER"||e.type==="REJECT_OFFER"||e.type==="IGNORE_OFFER")&&Y.handleMessage(e,t)})}async function Me(){try{console.log("Cleaning up storage...");const t=(await chrome.storage.local.get(["airtm_offers_timestamp"])).airtm_offers_timestamp;if(t){const o=Date.now()-new Date(t).getTime(),r=24*60*60*1e3;o>r&&(await chrome.storage.local.remove(["airtm_offers","airtm_offers_timestamp"]),console.log("Old offers cleaned up"))}}catch(e){console.error("Error cleaning up storage:",e)}}async function ke(){try{console.log("🔍 Checking for existing Airtm tabs...");const e=await chrome.tabs.query({url:"*://app.airtm.com/peer-transfers/available*"});if(e.length===0){console.log("📭 No existing Airtm tabs found");return}console.log(`🎯 Found ${e.length} existing Airtm tab(s)`),console.log("ℹ️ Content scripts will be automatically injected by Chrome based on manifest.json");for(const t of e)t.id&&t.url&&console.log(`📋 Found Airtm tab ${t.id}: ${t.url}`)}catch(e){console.error("❌ Error checking existing tabs:",e)}}function Fe(){const e=()=>{var t;try{(t=chrome==null?void 0:chrome.runtime)!=null&&t.getPlatformInfo&&chrome.runtime.getPlatformInfo(()=>{chrome.runtime.lastError&&console.warn("Keep alive error:",chrome.runtime.lastError)})}catch(o){console.warn("Keep alive failed:",o)}};e(),setInterval(e,2e4)}G().then(()=>{Fe()});chrome.runtime.onStartup.addListener(()=>{console.log("Service worker starting up..."),G()});chrome.runtime.onInstalled.addListener(e=>{console.log("Extension installed/updated:",e.reason),G()});
