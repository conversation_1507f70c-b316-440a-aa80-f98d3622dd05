/**
 * Options Page Debug Helper
 * Run this in the browser console to diagnose issues
 */

function debugOptionsPage() {
  console.log('🔍 AIRTM OPTIONS PAGE DIAGNOSTICS');
  console.log('=================================');
  
  // Check basic environment
  console.log('\n📋 ENVIRONMENT CHECK:');
  console.log('- URL:', window.location.href);
  console.log('- User Agent:', navigator.userAgent);
  console.log('- Viewport:', `${window.innerWidth}x${window.innerHeight}`);
  console.log('- Document Ready State:', document.readyState);
  
  // Check Chrome Extension APIs
  console.log('\n🔌 CHROME EXTENSION APIS:');
  console.log('- chrome object:', typeof chrome);
  console.log('- chrome.storage:', typeof chrome?.storage);
  console.log('- chrome.runtime:', typeof chrome?.runtime);
  console.log('- Extension ID:', chrome?.runtime?.id);
  
  // Check DOM elements
  console.log('\n🏗️ DOM STRUCTURE:');
  const root = document.getElementById('root');
  console.log('- Root element:', !!root);
  console.log('- Root dimensions:', root ? `${root.offsetWidth}x${root.offsetHeight}` : 'N/A');
  console.log('- Body classes:', document.body.className);
  console.log('- HTML classes:', document.documentElement.className);
  
  // Check CSS loading
  console.log('\n🎨 CSS STATUS:');
  const stylesheets = Array.from(document.styleSheets);
  console.log('- Stylesheets loaded:', stylesheets.length);
  stylesheets.forEach((sheet, i) => {
    try {
      console.log(`  ${i + 1}. ${sheet.href || 'inline'} (${sheet.cssRules?.length || 0} rules)`);
    } catch (e) {
      console.log(`  ${i + 1}. ${sheet.href || 'inline'} (access denied)`);
    }
  });
  
  // Check computed styles
  const bodyStyles = window.getComputedStyle(document.body);
  console.log('- Body background:', bodyStyles.background);
  console.log('- Body dimensions:', `${bodyStyles.width} x ${bodyStyles.height}`);
  
  // Check for React
  console.log('\n⚛️ REACT STATUS:');
  const reactRoot = root?._reactInternalInstance || root?._reactInternalFiber;
  console.log('- React mounted:', !!reactRoot);
  console.log('- React version:', window.React?.version || 'Unknown');
  
  // Check for errors
  console.log('\n❌ ERROR CHECK:');
  const errors = window.optionsPageErrors || [];
  console.log('- Recorded errors:', errors.length);
  errors.forEach((error, i) => {
    console.log(`  ${i + 1}. ${error.message} (${error.timestamp})`);
  });
  
  // Performance metrics
  console.log('\n⚡ PERFORMANCE:');
  const navigation = performance.getEntriesByType('navigation')[0];
  if (navigation) {
    console.log('- DOM Content Loaded:', `${navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart}ms`);
    console.log('- Load Complete:', `${navigation.loadEventEnd - navigation.loadEventStart}ms`);
  }
  
  // Storage check
  console.log('\n💾 STORAGE CHECK:');
  if (chrome?.storage?.sync) {
    chrome.storage.sync.get('settings', (result) => {
      console.log('- Settings in storage:', !!result.settings);
      console.log('- Settings keys:', result.settings ? Object.keys(result.settings) : 'None');
    });
  } else {
    console.log('- Chrome storage not available');
  }
  
  console.log('\n✅ DIAGNOSTICS COMPLETE');
  console.log('Copy this output when reporting issues');
}

// Error tracking
window.optionsPageErrors = window.optionsPageErrors || [];
window.addEventListener('error', (e) => {
  window.optionsPageErrors.push({
    message: e.message,
    filename: e.filename,
    lineno: e.lineno,
    colno: e.colno,
    timestamp: new Date().toISOString()
  });
});

// Auto-run diagnostics if in development
if (window.location.href.includes('localhost') || window.location.href.includes('chrome-extension://')) {
  setTimeout(debugOptionsPage, 1000);
}

// Export for manual use
window.debugOptionsPage = debugOptionsPage;

console.log('🔧 Options page debug helper loaded. Run debugOptionsPage() to diagnose issues.');
