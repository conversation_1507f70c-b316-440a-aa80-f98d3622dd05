/**
 * Options Page Debug Helper
 * Run this in the browser console to diagnose issues
 */

function debugOptionsPage() {
  console.log('🔍 AIRTM OPTIONS PAGE DIAGNOSTICS');
  console.log('=================================');
  
  // Check basic environment
  console.log('\n📋 ENVIRONMENT CHECK:');
  console.log('- URL:', window.location.href);
  console.log('- User Agent:', navigator.userAgent);
  console.log('- Viewport:', `${window.innerWidth}x${window.innerHeight}`);
  console.log('- Document Ready State:', document.readyState);
  
  // Check Chrome Extension APIs
  console.log('\n🔌 CHROME EXTENSION APIS:');
  console.log('- chrome object:', typeof chrome);
  console.log('- chrome.storage:', typeof chrome?.storage);
  console.log('- chrome.runtime:', typeof chrome?.runtime);
  console.log('- Extension ID:', chrome?.runtime?.id);
  
  // Check DOM elements
  console.log('\n🏗️ DOM STRUCTURE:');
  const root = document.getElementById('root');
  console.log('- Root element:', !!root);
  console.log('- Root dimensions:', root ? `${root.offsetWidth}x${root.offsetHeight}` : 'N/A');
  console.log('- Body classes:', document.body.className);
  console.log('- HTML classes:', document.documentElement.className);
  
  // Check CSS loading
  console.log('\n🎨 CSS STATUS:');
  const stylesheets = Array.from(document.styleSheets);
  console.log('- Stylesheets loaded:', stylesheets.length);
  stylesheets.forEach((sheet, i) => {
    try {
      console.log(`  ${i + 1}. ${sheet.href || 'inline'} (${sheet.cssRules?.length || 0} rules)`);
    } catch (e) {
      console.log(`  ${i + 1}. ${sheet.href || 'inline'} (access denied)`);
    }
  });
  
  // Check computed styles
  const bodyStyles = window.getComputedStyle(document.body);
  console.log('- Body background:', bodyStyles.background);
  console.log('- Body dimensions:', `${bodyStyles.width} x ${bodyStyles.height}`);
  
  // Check for React
  console.log('\n⚛️ REACT STATUS:');
  const reactRoot = root?._reactInternalInstance || root?._reactInternalFiber;
  console.log('- React mounted:', !!reactRoot);
  console.log('- React version:', window.React?.version || 'Unknown');
  
  // Check for errors
  console.log('\n❌ ERROR CHECK:');
  const errors = window.optionsPageErrors || [];
  console.log('- Recorded errors:', errors.length);
  errors.forEach((error, i) => {
    console.log(`  ${i + 1}. ${error.message} (${error.timestamp})`);
  });
  
  // Performance metrics
  console.log('\n⚡ PERFORMANCE:');
  const navigation = performance.getEntriesByType('navigation')[0];
  if (navigation) {
    console.log('- DOM Content Loaded:', `${navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart}ms`);
    console.log('- Load Complete:', `${navigation.loadEventEnd - navigation.loadEventStart}ms`);
  }
  
  // Storage check
  console.log('\n💾 STORAGE CHECK:');
  if (chrome?.storage?.sync) {
    chrome.storage.sync.get('settings', (result) => {
      console.log('- Settings in storage:', !!result.settings);
      console.log('- Settings keys:', result.settings ? Object.keys(result.settings) : 'None');
    });
  } else {
    console.log('- Chrome storage not available');
  }
  
  console.log('\n✅ DIAGNOSTICS COMPLETE');
  console.log('Copy this output when reporting issues');
}

// Error tracking and handling
window.optionsPageErrors = window.optionsPageErrors || [];

// Global error handler
window.addEventListener('error', function(e) {
  console.error('Options page error:', e.error);

  // Track error
  window.optionsPageErrors.push({
    message: e.message,
    filename: e.filename,
    lineno: e.lineno,
    colno: e.colno,
    timestamp: new Date().toISOString()
  });

  // Show error UI if React fails to load
  setTimeout(() => {
    const root = document.getElementById('root');
    if (root && root.innerHTML.includes('initial-loading')) {
      showErrorUI(root, e.message);
    }
  }, 5000); // Wait 5 seconds for React to load
});

// Error UI function
function showErrorUI(root, errorMessage) {
  root.innerHTML = `
    <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; min-height: 100vh; color: white; text-align: center; padding: 2rem; background: linear-gradient(135deg, #1e1b4b, #0f172a, #312e81);">
      <div style="max-width: 600px;">
        <h1 style="color: #ef4444; font-size: 2rem; margin-bottom: 1rem; font-weight: bold;">Configuration Error</h1>
        <p style="color: #94a3b8; font-size: 1.125rem; margin-bottom: 1rem; line-height: 1.6;">Failed to load the options page. This may be due to Content Security Policy restrictions or missing resources.</p>
        <div style="background: rgba(239, 68, 68, 0.1); border: 1px solid rgba(239, 68, 68, 0.3); border-radius: 0.5rem; padding: 1rem; margin-bottom: 2rem; text-align: left;">
          <p style="color: #fca5a5; font-size: 0.875rem; font-family: monospace;">${errorMessage}</p>
        </div>
        <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
          <button onclick="window.location.reload()" style="background: linear-gradient(to right, #8b5cf6, #7c3aed); color: white; padding: 0.75rem 1.5rem; border-radius: 0.5rem; border: none; cursor: pointer; font-weight: 600; transition: transform 0.2s;">
            Reload Page
          </button>
          <button onclick="debugOptionsPage()" style="background: linear-gradient(to right, #059669, #047857); color: white; padding: 0.75rem 1.5rem; border-radius: 0.5rem; border: none; cursor: pointer; font-weight: 600; transition: transform 0.2s;">
            Run Diagnostics
          </button>
        </div>
      </div>
    </div>
  `;
}

// Auto-run diagnostics if in development
if (window.location.href.includes('localhost') || window.location.href.includes('chrome-extension://')) {
  setTimeout(debugOptionsPage, 1000);
}

// Export for manual use
window.debugOptionsPage = debugOptionsPage;

console.log('🔧 Options page debug helper loaded. Run debugOptionsPage() to diagnose issues.');
