/**
 * Data Parser for Airtm GraphQL Responses
 * Handles parsing and validation of AvailableOperations responses
 */

import type {
  CurrencyData,
  UserData,
  RateInfo,
  DisplayRate,
  PaymentMethodInstance,
  AirtmOperation
} from '../types/airtm-schema'
import { isExtensionContextValid } from '../utils/extension-context'

/**
 * Parses the GraphQL AvailableOperations payload into typed AirtmOperation[]
 * @param payload - Raw GraphQL response payload
 * @returns Array of parsed AirtmOperation objects
 * @throws Error if payload is invalid
 */
export function parseAvailableOperations(payload: any): AirtmOperation[] {
  console.log('Validating AvailableOperations query format and payload structure')

  // Check extension context before processing
  if (!isExtensionContextValid()) {
    console.warn('⚠️ Extension context invalidated, skipping AvailableOperations parsing')
    return []
  }

  if (!payload?.availableOperations || !Array.isArray(payload.availableOperations)) {
    console.warn('Invalid AvailableOperations GraphQL query payload structure:', payload)
    throw new Error('Invalid AvailableOperations query format: missing or invalid availableOperations array')
  }
  console.log('AvailableOperations query format validation successful')

  const offers: AirtmOperation[] = []

  for (let i = 0; i < payload.availableOperations.length; i++) {
    try {
      // Check extension context for each offer to handle mid-processing invalidation
      if (!isExtensionContextValid()) {
        console.warn('⚠️ Extension context invalidated during offer parsing, stopping')
        break
      }

      const rawOffer = payload.availableOperations[i]
      console.log(`🔍 Raw offer ${i} structure:`, JSON.stringify(rawOffer, null, 2))
      const parsedOffer = parseGraphQLOffer(rawOffer)
      offers.push(parsedOffer)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)

      // Handle extension context invalidation gracefully
      if (errorMessage.includes('Extension context invalidated') ||
          errorMessage.includes('context invalidated')) {
        console.warn('⚠️ Extension context invalidated during offer parsing')
        break
      }

      console.error(`Failed to parse offer at index ${i}:`, error)
      console.error(`Raw offer data:`, JSON.stringify(payload.availableOperations[i], null, 2))
      // Continue parsing other offers instead of failing completely
      continue
    }
  }

  console.log(`Successfully parsed ${offers.length} offers from ${payload.availableOperations.length} raw offers using AvailableOperations query format`)
  return offers
}



/**
 * Parses a single raw offer from GraphQL response
 * @param raw - Raw offer object from GraphQL
 * @returns Parsed and validated AirtmOperation
 * @throws Error if required fields are missing or invalid
 */
export function parseGraphQLOffer(raw: any): AirtmOperation {
  if (!raw || typeof raw !== 'object') {
    throw new Error('Invalid offer: not an object')
  }

  // Extract and validate required fields - Updated to match current schema
  const {
    id,
    hash,
    operationType,
    status,
    isMine,
    createdAt,
    updatedAt,
    grossAmount,
    netAmount,
    metadata,
    walletCurrency,
    peer,
    rate,  // Added direct rate field
    rateInfo,
    displayRate,
    currency,
    makerPaymentMethod,
    __typename
  } = raw

  // Validate required fields
  if (typeof id !== 'string' || !id.trim()) {
    throw new Error('Invalid offer: missing or invalid id')
  }

  if (typeof hash !== 'string' || !hash.trim()) {
    throw new Error(`Invalid offer ${id}: missing or invalid hash`)
  }

  if (operationType !== 'BUY' && operationType !== 'SELL') {
    throw new Error(`Invalid offer ${id}: operationType must be BUY or SELL, got ${operationType}`)
  }

  if (typeof status !== 'string' || !status.trim()) {
    throw new Error(`Invalid offer ${id}: missing or invalid status`)
  }

  const validStatuses = ['CREATED', 'ACCEPTED', 'REJECTED', 'COMPLETED', 'CANCELLED']
  if (!validStatuses.includes(status)) {
    throw new Error(`Invalid offer ${id}: status must be one of ${validStatuses.join(', ')}, got ${status}`)
  }

  if (typeof isMine !== 'boolean') {
    throw new Error(`Invalid offer ${id}: isMine must be boolean, got ${typeof isMine}`)
  }

  if (typeof createdAt !== 'string' || !createdAt.trim()) {
    throw new Error(`Invalid offer ${id}: missing or invalid createdAt`)
  }

  if (updatedAt !== null && (typeof updatedAt !== 'string' || !updatedAt.trim())) {
    throw new Error(`Invalid offer ${id}: missing or invalid updatedAt`)
  }

  // Validate string-based amount fields
  if (typeof grossAmount !== 'string' || !grossAmount.trim()) {
    throw new Error(`Invalid offer ${id}: grossAmount must be a non-empty string`)
  }
  
  if (typeof netAmount !== 'string' || !netAmount.trim()) {
    throw new Error(`Invalid offer ${id}: netAmount must be a non-empty string`)
  }

  // Validate required nested objects
  if (!walletCurrency || typeof walletCurrency !== 'object') {
    throw new Error(`Invalid offer ${id}: missing or invalid walletCurrency`)
  }

  if (!peer || typeof peer !== 'object') {
    throw new Error(`Invalid offer ${id}: missing or invalid peer`)
  }

  // Build the parsed offer - Updated to include rate field
  const offer: AirtmOperation = {
    id,
    hash,
    operationType,
    status: status as 'CREATED' | 'ACCEPTED' | 'REJECTED' | 'COMPLETED' | 'CANCELLED',
    isMine,
    createdAt,
    updatedAt,
    grossAmount,
    netAmount,
    metadata: metadata || {},

    // Nested objects - assume they're well-formed by GraphQL
    walletCurrency: validateCurrencyData(walletCurrency, `${id}.walletCurrency`),
    peer: validateUserData(peer, `${id}.peer`),

    // Optional fields for BUY/SELL operations - Updated per AvailableOperations.txt
    rate: rate ? String(rate) : undefined,  // Added direct rate field
    rateInfo: rateInfo ? parseRateInfo(rateInfo, id) : undefined,
    displayRate: displayRate ? parseDisplayRate(displayRate, id) : undefined,
    currency: currency ? validateCurrencyData(currency, `${id}.currency`) : undefined,
    makerPaymentMethod: makerPaymentMethod ? validatePaymentMethod(makerPaymentMethod, `${id}.makerPaymentMethod`) : undefined,
    __typename: __typename || 'Operations__Unknown'
  }

  return offer
}

/**
 * Helper function to validate string-based amount fields
 */
function validateStringAmount(value: any, fieldName: string, offerId: string): string {
  if (typeof value !== 'string' || !value.trim()) {
    throw new Error(`Invalid offer ${offerId}: ${fieldName} must be a non-empty string`)
  }
  return value.trim()
}

/**
 * Validates CurrencyData structure - matches corrected schema
 */
function validateCurrencyData(currency: any, context: string): CurrencyData {
  if (!currency || typeof currency !== 'object') {
    throw new Error(`Invalid ${context}: not an object`)
  }

  const { id, symbol, precision, __typename } = currency

  if (typeof id !== 'string' || !id.trim()) {
    throw new Error(`Invalid ${context}: missing or invalid id`)
  }

  if (typeof symbol !== 'string' || !symbol.trim()) {
    throw new Error(`Invalid ${context}: missing or invalid symbol`)
  }

  if (typeof precision !== 'number' || precision < 0 || !Number.isInteger(precision)) {
    throw new Error(`Invalid ${context}: precision must be a non-negative integer, got ${precision}`)
  }

  // Validate __typename if present
  if (__typename && __typename !== 'Catalogs__Currency') {
    console.warn(`${context}: unexpected __typename '${__typename}', expected 'Catalogs__Currency'`)
  }

  return {
    id: id.trim(),
    symbol: symbol.trim(),
    precision,
    __typename: __typename || 'Catalogs__Currency'
  }
}

/**
 * Validates UserData structure - matches corrected schema
 */
function validateUserData(user: any, context: string): UserData {
  if (!user || typeof user !== 'object') {
    throw new Error(`Invalid ${context}: not an object`)
  }

  const { id, firstName, lastName, createdAt, country, countryInfo, securityHub, numbers, preferences, __typename } = user

  // Validate required string fields
  if (typeof id !== 'string' || !id.trim()) {
    throw new Error(`Invalid ${context}: missing or invalid id`)
  }

  if (typeof firstName !== 'string') {
    throw new Error(`Invalid ${context}: firstName must be a string, got ${typeof firstName}`)
  }

  if (typeof lastName !== 'string') {
    throw new Error(`Invalid ${context}: lastName must be a string, got ${typeof lastName}`)
  }

  if (typeof createdAt !== 'string' || !createdAt.trim()) {
    throw new Error(`Invalid ${context}: createdAt must be a non-empty string`)
  }

  // Validate country field - must be simple string, not union type
  if (typeof country !== 'string') {
    throw new Error(`Invalid ${context}: country must be a string, got ${typeof country}`)
  }

  // Validate required nested objects exist
  if (!countryInfo || typeof countryInfo !== 'object') {
    throw new Error(`Invalid ${context}: countryInfo is required and must be an object`)
  }

  if (!securityHub || typeof securityHub !== 'object') {
    throw new Error(`Invalid ${context}: securityHub is required and must be an object`)
  }

  // Validate SecurityHub fields
  if (typeof securityHub.verificationStatusName !== 'string') {
    console.warn(`${context}: securityHub.verificationStatusName should be a string, got ${typeof securityHub.verificationStatusName}`)
  }

  if (!numbers || typeof numbers !== 'object') {
    throw new Error(`Invalid ${context}: numbers is required and must be an object`)
  }

  if (!preferences || typeof preferences !== 'object') {
    throw new Error(`Invalid ${context}: preferences is required and must be an object`)
  }

  // Validate __typename
  if (__typename && !['Auth__OperationUser', 'Auth__User'].includes(__typename)) {
    console.warn(`${context}: unexpected __typename '${__typename}', expected 'Auth__OperationUser' or 'Auth__User'`)
  }

  return user as UserData
}

/**
 * Parses RateInfo structure
 */
function parseRateInfo(rateInfo: any, offerId: string): RateInfo {
  if (!rateInfo || typeof rateInfo !== 'object') {
    throw new Error(`Invalid offer ${offerId}: rateInfo is not an object`)
  }

  const {
    id,
    netAmount,
    grossAmount,
    fundsToReceiveMaker,
    fundsToReceiveTaker,
    fundsToSendMaker,
    fundsToSendTaker,
    __typename
  } = rateInfo

  return {
    id: id || offerId,
    netAmount: validateStringAmount(netAmount, 'rateInfo.netAmount', offerId),
    grossAmount: validateStringAmount(grossAmount, 'rateInfo.grossAmount', offerId),
    fundsToReceiveMaker: fundsToReceiveMaker !== null ? (typeof fundsToReceiveMaker === 'string' ? fundsToReceiveMaker : String(fundsToReceiveMaker)) : null,
    fundsToReceiveTaker: fundsToReceiveTaker !== null ? (typeof fundsToReceiveTaker === 'string' ? fundsToReceiveTaker : String(fundsToReceiveTaker)) : null,
    fundsToSendMaker: fundsToSendMaker !== null ? (typeof fundsToSendMaker === 'string' ? fundsToSendMaker : String(fundsToSendMaker)) : null,
    fundsToSendTaker: fundsToSendTaker !== null ? (typeof fundsToSendTaker === 'string' ? fundsToSendTaker : String(fundsToSendTaker)) : null,
    __typename: __typename || 'Operations__RateInfo'
  }
}

/**
 * Parses DisplayRate structure - Updated to match current schema
 */
function parseDisplayRate(displayRate: any, offerId: string): DisplayRate {
  if (!displayRate || typeof displayRate !== 'object') {
    throw new Error(`Invalid offer ${offerId}: displayRate is not an object`)
  }

  const { direction, rate, __typename } = displayRate

  // Validate direction is one of the expected values
  const validDirections = ['TO_LOCAL_CURRENCY', 'FROM_LOCAL_CURRENCY', 'TO_WALLET_CURRENCY']
  if (!validDirections.includes(direction)) {
    console.warn(`Invalid direction '${direction}', defaulting to 'TO_LOCAL_CURRENCY'`)
  }

  // Validate rate field (added per AvailableOperations.txt)
  if (rate !== undefined && rate !== null && typeof rate !== 'string' && typeof rate !== 'number') {
    console.warn(`Invalid rate type '${typeof rate}', converting to string`)
  }

  return {
    direction: validDirections.includes(direction) ? direction : 'TO_LOCAL_CURRENCY',
    rate: rate ? String(rate) : '0',  // Added rate field with fallback
    __typename: __typename || 'Operations__DisplayRate'
  }
}

/**
 * Validates PaymentMethodInstance structure
 */
function validatePaymentMethod(paymentMethod: any, context: string): PaymentMethodInstance {
  if (!paymentMethod || typeof paymentMethod !== 'object') {
    throw new Error(`Invalid ${context}: not an object`)
  }

  const { id, categoryId, isThirdPartyInstance, version } = paymentMethod

  // Validate required fields
  if (typeof id !== 'string' || !id.trim()) {
    throw new Error(`Invalid ${context}: missing or invalid id`)
  }

  if (typeof categoryId !== 'string' || !categoryId.trim()) {
    throw new Error(`Invalid ${context}: missing or invalid categoryId`)
  }

  if (typeof isThirdPartyInstance !== 'boolean') {
    throw new Error(`Invalid ${context}: isThirdPartyInstance must be boolean, got ${typeof isThirdPartyInstance}`)
  }

  // Validate nested version object exists
  if (!version || typeof version !== 'object') {
    throw new Error(`Invalid ${context}: version is required and must be an object`)
  }

  // data and __typename can be any type, so no validation needed
  return paymentMethod as PaymentMethodInstance
}

/**
 * Utility function to extract bearer token from page
 */
export function extractBearerToken(): string | null {
  try {
    // Try to find token in localStorage with more comprehensive key patterns
    const localStorageKeys = [
      'airtm_token', 'auth_token', 'access_token', 'bearer_token',
      'authToken', 'accessToken', 'bearerToken', 'jwt', 'JWT',
      'token', 'authorization', 'auth', 'session_token'
    ]
    
    for (const key of localStorageKeys) {
      const token = localStorage.getItem(key)
      if (token && token.length > 10) { // Basic validation
        console.log(`Bearer token found in localStorage: ${key}`)
        return token
      }
    }

    // Try sessionStorage as well
    for (const key of localStorageKeys) {
      const token = sessionStorage.getItem(key)
      if (token && token.length > 10) {
        console.log(`Bearer token found in sessionStorage: ${key}`)
        return token
      }
    }

    // Try to find token in cookies with better pattern matching
    const cookies = document.cookie.split(';')
    for (const cookie of cookies) {
      const [name, value] = cookie.trim().split('=')
      if ((name.toLowerCase().includes('token') || name.toLowerCase().includes('auth')) && value && value.length > 10) {
        console.log(`Bearer token found in cookie: ${name}`)
        return value
      }
    }

    // Try to find token in page scripts with improved regex patterns
    const scripts = document.querySelectorAll('script')
    for (const script of scripts) {
      const content = script.textContent || ''
      
      // Look for various token patterns
      const patterns = [
        /(?:token|bearer|authorization)["']?\s*:\s*["']([^"'\s]{20,})["']/i,
        /Authorization["']?\s*:\s*["']Bearer\s+([^"'\s]+)["']/i,
        /["']token["']\s*:\s*["']([^"'\s]{20,})["']/i,
        /window\.__INITIAL_STATE__.*["']token["']\s*:\s*["']([^"'\s]+)["']/i
      ]
      
      for (const pattern of patterns) {
        const tokenMatch = content.match(pattern)
        if (tokenMatch && tokenMatch[1]) {
          console.log('Bearer token found in script content')
          return tokenMatch[1]
        }
      }
    }

    // Try to extract from Authorization headers if available in global context
    if (typeof window !== 'undefined' && (window as any).__auth_token) {
      console.log('Bearer token found in window.__auth_token')
      return (window as any).__auth_token
    }

    console.log('No bearer token found in any location')
    return null
  } catch (error) {
    console.error('Error extracting bearer token:', error)
    return null
  }
}
