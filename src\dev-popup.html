<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Development Popup Preview</title>
    <script type="module" crossorigin src="/src/popup/popup.tsx"></script>
    <link rel="stylesheet" href="/src/styles/globals.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            width: 600px;
            height: 800px;
            overflow: hidden;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        }
        
        /* Mock Chrome extension APIs for development */
        .dev-notice {
            position: fixed;
            top: 10px;
            right: 10px;
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 6px;
            padding: 8px 12px;
            font-size: 12px;
            color: #92400e;
            z-index: 1000;
            max-width: 200px;
        }
    </style>
</head>
<body class="popup">
    <div class="dev-notice">
        Development Mode
        <br>
        <small>Some features may not work outside extension context</small>
    </div>
    
    <div id="popup-root"></div>
    
    <script>
        // Mock Chrome extension APIs for development
        if (typeof chrome === 'undefined') {
            window.chrome = {
                storage: {
                    local: {
                        get: (keys, callback) => {
                            // Mock settings data
                            const mockData = {
                                settings: {
                                    monitoring: true,
                                    autoAccept: false,
                                    notificationsEnabled: true,
                                    soundEnabled: true,
                                    blacklistKeywords: ['paypal', 'zelle', 'cashapp'],
                                    fuzzyMatching: {
                                        enabled: true,
                                        threshold: 0.8,
                                        enableAliases: true
                                    }
                                },
                                offers: [
                                    {
                                        id: 'offer1',
                                        hash: 'hash123',
                                        operationType: 'BUY',
                                        grossAmount: '100.50',
                                        netAmount: '95.25',
                                        currency: { symbol: '$', precision: 2 },
                                        peer: {
                                            firstName: 'John',
                                            lastName: 'Doe',
                                            country: 'US',
                                            numbers: { score: 4.8, completedOperations: 150 }
                                        },
                                        makerPaymentMethod: {
                                            version: {
                                                category: { translationTag: 'Bank Transfer' }
                                            }
                                        },
                                        createdAt: new Date().toISOString()
                                    },
                                    {
                                        id: 'offer2',
                                        hash: 'hash456',
                                        operationType: 'SELL',
                                        grossAmount: '200.00',
                                        netAmount: '190.50',
                                        currency: { symbol: '€', precision: 2 },
                                        peer: {
                                            firstName: 'Jane',
                                            lastName: 'Smith',
                                            country: 'DE',
                                            numbers: { score: 4.9, completedOperations: 89 }
                                        },
                                        makerPaymentMethod: {
                                            version: {
                                                category: { translationTag: 'PayPal' }
                                            }
                                        },
                                        createdAt: new Date().toISOString()
                                    }
                                ]
                            };
                            
                            if (typeof keys === 'string') {
                                callback({ [keys]: mockData[keys] });
                            } else if (Array.isArray(keys)) {
                                const result = {};
                                keys.forEach(key => {
                                    result[key] = mockData[key];
                                });
                                callback(result);
                            } else {
                                callback(mockData);
                            }
                        },
                        set: (data, callback) => {
                            console.log('Mock chrome.storage.local.set:', data);
                            if (callback) callback();
                        }
                    }
                },
                runtime: {
                    sendMessage: (message, callback) => {
                        console.log('Mock chrome.runtime.sendMessage:', message);
                        if (callback) {
                            // Mock response based on message type
                            if (message.type === 'GET_OFFERS') {
                                callback({ offers: [] });
                            } else {
                                callback({ success: true });
                            }
                        }
                    },
                    onMessage: {
                        addListener: (callback) => {
                            console.log('Mock chrome.runtime.onMessage.addListener');
                        }
                    }
                },
                tabs: {
                    create: (options) => {
                        console.log('Mock chrome.tabs.create:', options);
                        window.open(options.url, '_blank');
                    }
                }
            };
        }
    </script>
</body>
</html>
