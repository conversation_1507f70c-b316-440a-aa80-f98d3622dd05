@tailwind base;
@tailwind components;
@tailwind utilities;

/* ===== MODERN POPUP DESIGN SYSTEM ===== */

:root {
  /* Brand Colors */
  --brand-primary: #2563eb;
  --brand-secondary: #10b981;
  --brand-accent: #8b5cf6;

  /* UI Colors */
  --ui-white: #ffffff;
  --ui-gray-50: #f8fafc;
  --ui-gray-100: #f1f5f9;
  --ui-gray-200: #e2e8f0;
  --ui-gray-300: #cbd5e1;
  --ui-gray-400: #94a3b8;
  --ui-gray-500: #64748b;
  --ui-gray-600: #475569;
  --ui-gray-700: #334155;
  --ui-gray-800: #1e293b;
  --ui-gray-900: #0f172a;

  /* Status Colors */
  --status-success: #10b981;
  --status-warning: #f59e0b;
  --status-error: #ef4444;
  --status-info: #3b82f6;

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  --gradient-secondary: linear-gradient(135deg, #10b981 0%, #059669 100%);
  --gradient-surface: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  --gradient-background: linear-gradient(135deg, #f8fafc 0%, #eff6ff 50%, #ecfdf5 100%);

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;

  /* Typography */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;

  /* Animation */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.2s ease;
  --transition-slow: 0.3s ease;
}

/* ===== BASE STYLES ===== */

* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  background: var(--ui-white);
  color: var(--ui-gray-800);
  line-height: 1.5;
}

/* ===== POPUP CONTAINER ===== */

.popup-container {
  width: 600px;
  height: 850px;
  background: var(--gradient-background);
  position: relative;
  overflow: hidden;
  font-family: 'Inter', sans-serif;
}

.popup-background {
  position: absolute;
  inset: 0;
  z-index: 0;
}

.popup-pattern {
  position: absolute;
  inset: 0;
  background:
    radial-gradient(circle at 30% 20%, rgba(37, 99, 235, 0.06) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(16, 185, 129, 0.06) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(139, 92, 246, 0.04) 0%, transparent 50%);
}

.popup-content {
  position: relative;
  z-index: 10;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* ===== LOADING STATE ===== */

.loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: var(--space-8);
}

.logo-container {
  position: relative;
  margin-bottom: var(--space-8);
}

.logo-icon {
  width: 64px;
  height: 64px;
  background: var(--gradient-primary);
  border-radius: var(--radius-2xl);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-xl);
  position: relative;
  z-index: 2;
}

.logo-text {
  color: var(--ui-white);
  font-size: var(--font-size-2xl);
  font-weight: 700;
}

.logo-pulse {
  position: absolute;
  inset: -4px;
  background: var(--gradient-primary);
  border-radius: var(--radius-2xl);
  opacity: 0.3;
  animation: pulse-ring 2s ease-in-out infinite;
}

.spinner-container {
  position: relative;
  width: 80px;
  height: 80px;
  margin-bottom: var(--space-8);
}

.spinner-ring {
  position: absolute;
  inset: 0;
  border-radius: 50%;
  border: 3px solid transparent;
}

.spinner-ring-1 {
  border-top-color: var(--brand-primary);
  animation: spin 1s linear infinite;
}

.spinner-ring-2 {
  inset: 8px;
  border-top-color: var(--brand-secondary);
  animation: spin 1.5s linear infinite reverse;
}

.spinner-ring-3 {
  inset: 16px;
  border-top-color: var(--brand-accent);
  animation: spin 2s linear infinite;
}

.loading-text {
  text-align: center;
}

.app-title {
  font-size: var(--font-size-xl);
  font-weight: 700;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--space-2);
}

.loading-message {
  color: var(--ui-gray-600);
  font-size: var(--font-size-sm);
  font-weight: 500;
  margin-bottom: var(--space-4);
}

.loading-dots {
  display: flex;
  gap: var(--space-2);
  justify-content: center;
}

.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: bounce-dot 1.4s ease-in-out infinite both;
}

.dot-1 {
  background: var(--brand-primary);
  animation-delay: -0.32s;
}

.dot-2 {
  background: var(--brand-secondary);
  animation-delay: -0.16s;
}

.dot-3 {
  background: var(--brand-accent);
}

/* ===== HEADER STYLES ===== */

.popup-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(12px);
  border-bottom: 1px solid var(--ui-gray-200);
  flex-shrink: 0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4) var(--space-6);
}

.header-brand {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.brand-logo {
  position: relative;
}

.logo-icon {
  width: 48px;
  height: 48px;
  background: var(--gradient-primary);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-lg);
}

.logo-text {
  color: var(--ui-white);
  font-size: var(--font-size-lg);
  font-weight: 700;
}

.status-indicator {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid var(--ui-white);
  box-shadow: var(--shadow-sm);
}

.status-active {
  background: var(--status-success);
  animation: pulse-glow 2s ease-in-out infinite;
}

.status-inactive {
  background: var(--ui-gray-400);
}

.brand-info {
  display: flex;
  flex-direction: column;
}

.app-title {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--ui-gray-800);
  margin: 0;
  line-height: 1.2;
}

.app-subtitle {
  font-size: var(--font-size-sm);
  color: var(--ui-gray-500);
  font-weight: 500;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: var(--space-3);
}

.action-btn {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-weight: 600;
  transition: all var(--transition-normal);
  cursor: pointer;
  border: none;
  box-shadow: var(--shadow-sm);
}

.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.action-btn-primary {
  background: var(--gradient-primary);
  color: var(--ui-white);
}

.action-btn-primary:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
}

.action-btn-secondary {
  background: var(--ui-white);
  color: var(--ui-gray-700);
  border: 1px solid var(--ui-gray-200);
}

.action-btn-secondary:hover {
  background: var(--ui-gray-50);
  border-color: var(--ui-gray-300);
}

/* ===== ERROR ALERT ===== */

.error-container {
  padding: var(--space-4) var(--space-6) 0;
  animation: slideDown 0.3s ease-out;
}

.error-alert {
  background: rgba(254, 242, 242, 0.95);
  backdrop-filter: blur(8px);
  border: 1px solid #fecaca;
  border-radius: var(--radius-xl);
  padding: var(--space-4);
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  box-shadow: var(--shadow-sm);
}

.error-content {
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
  flex: 1;
}

.error-icon {
  width: 32px;
  height: 32px;
  background: var(--status-error);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--ui-white);
  flex-shrink: 0;
}

.error-text {
  flex: 1;
}

.error-title {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: #991b1b;
  margin: 0 0 var(--space-1) 0;
}

.error-message {
  font-size: var(--font-size-sm);
  color: #7f1d1d;
  margin: 0;
  line-height: 1.4;
}

.error-close {
  background: none;
  border: none;
  color: #dc2626;
  cursor: pointer;
  padding: var(--space-1);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.error-close:hover {
  background: rgba(220, 38, 38, 0.1);
  color: #991b1b;
}

/* ===== STATS SECTION ===== */

.stats-section {
  padding: var(--space-4) var(--space-6);
  flex-shrink: 0;
}

.stats-container {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(12px);
  border: 1px solid var(--ui-gray-200);
  border-radius: var(--radius-2xl);
  padding: var(--space-4);
  box-shadow: var(--shadow-sm);
}

/* ===== MAIN CONTENT ===== */

.main-content {
  flex: 1;
  padding: 0 var(--space-6) var(--space-2);
  min-height: 0;
}

.content-container {
  height: 380px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(12px);
  border: 1px solid var(--ui-gray-200);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-sm);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  /* Ensure proper height calculation for nested flex containers */
  min-height: 380px;
  max-height: 380px;
}

.offers-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4) var(--space-6);
  border-bottom: 1px solid var(--ui-gray-200);
  background: var(--gradient-surface);
  flex-shrink: 0;
  /* Fixed height to ensure consistent layout */
  height: 72px;
  min-height: 72px;
}

.offers-title {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.title-icon {
  width: 32px;
  height: 32px;
  background: var(--gradient-secondary);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-sm);
}

.icon-shape {
  width: 16px;
  height: 16px;
  background: var(--ui-white);
  border-radius: var(--radius-sm);
}

.section-title {
  font-size: var(--font-size-lg);
  font-weight: 700;
  color: var(--ui-gray-800);
  margin: 0;
}

.offers-meta {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.live-indicator {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: var(--radius-xl);
  backdrop-filter: blur(8px);
}

.pulse-dot {
  width: 8px;
  height: 8px;
  background: var(--status-success);
  border-radius: 50%;
  animation: pulse-glow 2s ease-in-out infinite;
}

.live-text {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--ui-gray-700);
}

.offers-count {
  padding: var(--space-2) var(--space-3);
  background: var(--brand-primary);
  color: var(--ui-white);
  border-radius: var(--radius-xl);
  font-size: var(--font-size-sm);
  font-weight: 600;
}

.offers-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* ===== CONTROL SECTION ===== */

.control-section {
  padding: var(--space-2) var(--space-6) var(--space-4);
  flex-shrink: 0;
}

.control-container {
  /* Control panel styles will be handled by the ControlPanel component */
}

/* ===== OFFERS LIST COMPONENT ===== */

.offers-list-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(8px);
  border-radius: var(--radius-xl);
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
}

.offers-list-header {
  background: rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: var(--space-4) var(--space-6);
  flex-shrink: 0;
}

.offers-list-scroll {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.offers-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  padding: var(--space-8);
}

.offers-list-items {
  padding: var(--space-6);
}

.offer-item-wrapper {
  margin-bottom: var(--space-4);
  animation: fadeIn 0.3s ease-out forwards;
}

.offer-item-wrapper:last-child {
  margin-bottom: 0;
}

.offers-list-footer {
  background: rgba(255, 255, 255, 0.1);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: var(--space-3) var(--space-6);
  backdrop-filter: blur(8px);
  flex-shrink: 0;
}

/* ===== MODERN SCROLLBARS ===== */

.offers-list-scroll::-webkit-scrollbar {
  width: 6px;
}

.offers-list-scroll::-webkit-scrollbar-track {
  background: var(--ui-gray-100);
  border-radius: var(--radius-lg);
  margin: var(--space-2);
}

.offers-list-scroll::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, var(--brand-primary), #1d4ed8);
  border-radius: var(--radius-lg);
  border: 1px solid var(--ui-white);
  transition: all var(--transition-normal);
}

.offers-list-scroll::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #1d4ed8, #1e40af);
}

.offers-list-scroll::-webkit-scrollbar-corner {
  background: transparent;
}

/* Thin scrollbar for compact areas */
.scrollbar-thin::-webkit-scrollbar {
  width: 4px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: var(--ui-gray-300);
  border-radius: var(--radius-md);
  transition: background var(--transition-normal);
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: var(--ui-gray-400);
}

/* ===== ANIMATIONS ===== */

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse-ring {
  0% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.1;
  }
  100% {
    transform: scale(1);
    opacity: 0.3;
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 8px rgba(16, 185, 129, 0.4);
  }
  50% {
    box-shadow: 0 0 16px rgba(16, 185, 129, 0.8);
  }
}

@keyframes bounce-dot {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-16px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== POPUP DIMENSION ENFORCEMENT ===== */

/* Popup Specific Styles */
body.popup {
  width: 600px !important;
  height: 850px !important;
  min-width: 600px !important;
  min-height: 850px !important;
  max-width: 600px !important;
  max-height: 850px !important;
  overflow: hidden;
  position: relative;
}

/* Options Page Styles - Full Browser Window */
html.options-page,
body.options {
  width: 100vw !important;
  height: 100vh !important;
  min-width: 100vw !important;
  min-height: 100vh !important;
  max-width: 100vw !important;
  max-height: none !important;
  overflow-x: hidden;
  overflow-y: auto;
  margin: 0;
  padding: 0;
  background: var(--gradient-background);
}

/* Root element styles */
#root {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* Popup-specific root styling */
body.popup #root {
  width: 100% !important;
  height: 100% !important;
  min-width: 600px !important;
  min-height: 850px !important;
}

/* Options page specific root styling */
body.options #root {
  width: 100%;
  height: 100%;
  min-height: 100vh;
}

/* Ensure popup container takes full dimensions */
body.popup #root > div {
  width: 600px !important;
  height: 850px !important;
  min-width: 600px !important;
  min-height: 850px !important;
}



