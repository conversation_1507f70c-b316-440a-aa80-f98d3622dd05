@tailwind base;
@tailwind components;
@tailwind utilities;

html, body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', '<PERSON>ra Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: #ffffff;
  color: #1f2937;
}

/* Popup specific styles */
body.popup {
  width: 600px;
  height: 800px;
  overflow: hidden;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
}

/* Options page specific styles */
body.options {
  width: 100vw;
  height: 100vh;
  overflow-x: hidden;
  overflow-y: auto;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #f1f5f9 100%);
}

#root {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Custom Scrollbar - Light Theme */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(241, 245, 249, 0.6);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, rgba(99, 102, 241, 0.3), rgba(168, 85, 247, 0.3));
  border-radius: 4px;
  transition: background 0.2s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, rgba(99, 102, 241, 0.6), rgba(168, 85, 247, 0.6));
}

/* Modern scrollbar styles */
.custom-scrollbar-modern::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar-modern::-webkit-scrollbar-track {
  background: rgba(241, 245, 249, 0.5);
  border-radius: 8px;
  margin: 4px;
}

.custom-scrollbar-modern::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, rgba(99, 102, 241, 0.4), rgba(139, 92, 246, 0.6));
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.2s ease;
}

.custom-scrollbar-modern::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, rgba(99, 102, 241, 0.7), rgba(139, 92, 246, 0.9));
  transform: scaleX(1.2);
}

.custom-scrollbar-modern::-webkit-scrollbar-corner {
  background: transparent;
}

/* Enhanced Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(16, 185, 129, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.8);
  }
}

/* Animation Classes */
.animate-fade-in {
  animation: fadeIn 0.5s ease-out forwards;
}

.animate-slide-in {
  animation: slideIn 0.4s ease-out forwards;
}

.animate-slide-in-right {
  animation: slideInFromRight 0.4s ease-out forwards;
}

.animate-scale-in {
  animation: scaleIn 0.3s ease-out forwards;
}

.animate-bounce-in {
  animation: bounceIn 0.6s ease-out forwards;
}

.animate-shimmer {
  animation: shimmer 2s infinite linear;
  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  background-size: 200px 100%;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

/* Elegant White Theme Card Styles */
.card-elegant {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08), 0 4px 16px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
  border-radius: 16px;
}

.card-elegant:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12), 0 8px 24px rgba(0, 0, 0, 0.08);
  border-color: rgba(99, 102, 241, 0.4);
  background: rgba(255, 255, 255, 0.9);
}

/* Dark Theme Card Styles */
.card-dark {
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
  border: 1px solid #374151;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.card-dark:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
  border-color: #10b981;
}

/* Button Styles - Elegant White Theme */
.btn-primary-elegant {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.9), rgba(168, 85, 247, 0.9));
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(99, 102, 241, 0.2);
  border-radius: 12px;
}

.btn-primary-elegant:hover {
  background: linear-gradient(135deg, rgba(99, 102, 241, 1), rgba(168, 85, 247, 1));
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(99, 102, 241, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

.btn-secondary-elegant {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(156, 163, 175, 0.3);
  color: #4b5563;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border-radius: 12px;
}

.btn-secondary-elegant:hover {
  background: rgba(255, 255, 255, 0.9);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border-color: rgba(99, 102, 241, 0.3);
  color: #374151;
}

/* Button Styles - Dark Theme */
.btn-primary-dark {
  background: linear-gradient(135deg, #10b981, #059669);
  border: none;
  color: white;
  font-weight: 600;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
}

.btn-primary-dark:hover {
  background: linear-gradient(135deg, #059669, #047857);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.5);
}

.btn-secondary-dark {
  background: linear-gradient(135deg, #6b7280, #4b5563);
  border: none;
  color: white;
  font-weight: 600;
  transition: all 0.2s ease;
}

.btn-secondary-dark:hover {
  background: linear-gradient(135deg, #4b5563, #374151);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(107, 114, 128, 0.4);
}

.btn-danger-dark {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  border: none;
  color: white;
  font-weight: 600;
  transition: all 0.2s ease;
}

.btn-danger-dark:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
}

/* Status Indicators - Elegant White Theme */
.status-online-elegant {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(21, 128, 61, 0.1));
  color: #15803d;
  border: 1px solid rgba(34, 197, 94, 0.3);
  backdrop-filter: blur(8px);
  box-shadow: 0 2px 8px rgba(34, 197, 94, 0.1);
  border-radius: 8px;
}

.status-offline-elegant {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 38, 0.1));
  color: #dc2626;
  border: 1px solid rgba(239, 68, 68, 0.3);
  backdrop-filter: blur(8px);
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.1);
  border-radius: 8px;
}

.status-warning-elegant {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(217, 119, 6, 0.1));
  color: #d97706;
  border: 1px solid rgba(245, 158, 11, 0.3);
  backdrop-filter: blur(8px);
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.1);
  border-radius: 8px;
}

/* Status Indicators - Dark Theme */
.status-online-dark {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border: 1px solid #065f46;
  box-shadow: 0 0 10px rgba(16, 185, 129, 0.3);
}

.status-offline-dark {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  border: 1px solid #991b1b;
  box-shadow: 0 0 10px rgba(239, 68, 68, 0.3);
}

.status-warning-dark {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  border: 1px solid #92400e;
  box-shadow: 0 0 10px rgba(245, 158, 11, 0.3);
}

/* Input Styles - Elegant White Theme */
.input-elegant {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(156, 163, 175, 0.3);
  color: #374151;
  transition: all 0.3s ease;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.input-elegant:focus {
  border-color: rgba(99, 102, 241, 0.5);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1), 0 4px 16px rgba(0, 0, 0, 0.1);
  outline: none;
  background: rgba(255, 255, 255, 0.95);
}

.input-elegant::placeholder {
  color: #9ca3af;
}

/* Input Styles - Dark Theme */
.input-dark {
  background: #374151;
  border: 1px solid #4b5563;
  color: #f9fafb;
  border-radius: 8px;
  padding: 8px 12px;
  transition: all 0.2s ease;
}

.input-dark:focus {
  outline: none;
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
  background: #1f2937;
}

.input-dark::placeholder {
  color: #9ca3af;
}

/* Offer Card Styles - Elegant White Theme */
.offer-card-elegant {
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
}

.offer-card-elegant::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);
  transition: left 0.6s ease;
}

.offer-card-elegant:hover::before {
  left: 100%;
}

.offer-card-elegant:hover {
  transform: translateY(-6px);
  border-color: rgba(99, 102, 241, 0.4);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12), 0 8px 24px rgba(99, 102, 241, 0.1);
  background: rgba(255, 255, 255, 0.95);
}

/* Offer Card Styles - Dark Theme */
.offer-card-dark {
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
  border: 1px solid #374151;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.offer-card-dark::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(16, 185, 129, 0.1), transparent);
  transition: left 0.5s ease;
}

.offer-card-dark:hover::before {
  left: 100%;
}

.offer-card-dark:hover {
  transform: translateY(-4px);
  border-color: #10b981;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.offer-card-dark.selected {
  border-color: #10b981;
  background: linear-gradient(135deg, #064e3b 0%, #1f2937 100%);
  box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
}

/* Notification Styles - Elegant White Theme */
.notification-elegant {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: #374151;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12), 0 4px 16px rgba(0, 0, 0, 0.08);
  border-radius: 16px;
}

.notification-elegant.success {
  border-color: rgba(34, 197, 94, 0.3);
  background: rgba(240, 253, 244, 0.9);
}

.notification-elegant.error {
  border-color: rgba(239, 68, 68, 0.3);
  background: rgba(254, 242, 242, 0.9);
}

.notification-elegant.warning {
  border-color: rgba(245, 158, 11, 0.3);
  background: rgba(255, 251, 235, 0.9);
}

/* Loading Spinner - Elegant White Theme */
.spinner-elegant {
  border: 3px solid rgba(156, 163, 175, 0.3);
  border-top: 3px solid rgba(99, 102, 241, 0.8);
  animation: spin 1s linear infinite;
  backdrop-filter: blur(4px);
}

/* Notification Styles - Dark Theme */
.notification-dark {
  background: #1f2937;
  border: 1px solid #374151;
  border-radius: 12px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.4);
  color: #f9fafb;
}

.notification-success-dark {
  border-color: #10b981;
  background: linear-gradient(135deg, #064e3b, #1f2937);
}

.notification-error-dark {
  border-color: #ef4444;
  background: linear-gradient(135deg, #7f1d1d, #1f2937);
}

.notification-warning-dark {
  border-color: #f59e0b;
  background: linear-gradient(135deg, #78350f, #1f2937);
}

/* Loading Spinner - Dark Theme */
.spinner-dark {
  border: 3px solid #374151;
  border-top: 3px solid #10b981;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Sidebar Styles */
.sidebar-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: #374151;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  cursor: pointer;
}

.sidebar-icon:hover {
  background: #10b981;
  transform: scale(1.05);
}

.sidebar-icon.active {
  background: #10b981;
  box-shadow: 0 0 15px rgba(16, 185, 129, 0.5);
}

/* Stats Panel - Dark Theme */
.stats-item-dark {
  background: #374151;
  border-radius: 8px;
  padding: 12px;
  text-align: center;
  transition: all 0.2s ease;
}

.stats-item-dark:hover {
  background: #4b5563;
  transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 480px) {
  .mobile-hidden {
    display: none;
  }
  
  .mobile-full {
    width: 100%;
  }
}

/* Focus States - Dark Theme */
.focus-ring-dark {
  transition: box-shadow 0.2s ease;
}

.focus-ring-dark:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.2);
}

/* Utility Classes */
.text-shadow-dark {
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

.border-gradient-dark {
  border: 2px solid;
  border-image: linear-gradient(135deg, #10b981, #059669) 1;
}

/* Glass Effects - Elegant White Theme */
.glass-elegant {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
}

.glass-elegant-strong {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
}

.glass-elegant-subtle {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.06);
}

/* Glass Effect - Dark Theme */
.glass-dark {
  background: rgba(31, 41, 55, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(75, 85, 99, 0.3);
}

/* Glass Effect - Light Theme */
.glass {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Modern Card Component */
.card-modern {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.card-modern:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* Hover Effects */
.hover-lift-dark {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.hover-lift-dark:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
}

.hover-glow-dark {
  transition: box-shadow 0.3s ease;
}

.hover-glow-dark:hover {
  box-shadow: 0 0 20px rgba(16, 185, 129, 0.5);
}

.btn-primary {
  @apply bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded transition-colors;
}

.btn-secondary {
  @apply bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded transition-colors;
}

.btn-success {
  @apply bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded transition-colors;
}

.btn-danger {
  @apply bg-red-500 hover:bg-red-600 text-white font-medium py-2 px-4 rounded transition-colors;
}

/* Input styles */
.input-field {
  @apply border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

/* Status indicators */
.status-active {
  @apply bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium;
}

.status-inactive {
  @apply bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs font-medium;
}

.status-pending {
  @apply bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium;
}

/* Loading spinner */
.spinner {
  @apply animate-spin rounded-full border-2 border-gray-300 border-t-blue-500;
}

/* Notification styles */
.notification {
  @apply fixed top-4 right-4 bg-white border border-gray-200 rounded-lg shadow-lg p-4 max-w-sm z-50;
}

.notification-success {
  @apply border-green-200 bg-green-50;
}

.notification-error {
  @apply border-red-200 bg-red-50;
}

.notification-warning {
  @apply border-yellow-200 bg-yellow-50;
}

/* Range slider styles */
input[type="range"].slider {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
}

input[type="range"].slider::-webkit-slider-track {
  background: #e2e8f0;
  height: 6px;
  border-radius: 3px;
}

input[type="range"].slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  background: #10b981;
  height: 18px;
  width: 18px;
  border-radius: 50%;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

input[type="range"].slider::-webkit-slider-thumb:hover {
  background: #059669;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

input[type="range"].slider::-moz-range-track {
  background: #e2e8f0;
  height: 6px;
  border-radius: 3px;
  border: none;
}

input[type="range"].slider::-moz-range-thumb {
  background: #10b981;
  height: 18px;
  width: 18px;
  border-radius: 50%;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

/* Dark mode support (for future implementation) */
@media (prefers-color-scheme: dark) {
  .dark-mode {
    @apply bg-gray-900 text-white;
  }

  .dark-mode .offer-card {
    @apply bg-gray-800 border-gray-700;
  }

  .dark-mode .input-field {
    @apply bg-gray-800 border-gray-600 text-white;
  }
}
