@tailwind base;
@tailwind components;
@tailwind utilities;

/* Modern Design System Variables */
:root {
  /* Primary Brand Colors - Deep Ocean Blue */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;

  /* Secondary Colors - Emerald Green */
  --secondary-50: #ecfdf5;
  --secondary-100: #d1fae5;
  --secondary-200: #a7f3d0;
  --secondary-300: #6ee7b7;
  --secondary-400: #34d399;
  --secondary-500: #10b981;
  --secondary-600: #059669;
  --secondary-700: #047857;
  --secondary-800: #065f46;
  --secondary-900: #064e3b;

  /* Neutral Colors - Modern Gray Scale */
  --neutral-0: #ffffff;
  --neutral-50: #f8fafc;
  --neutral-100: #f1f5f9;
  --neutral-200: #e2e8f0;
  --neutral-300: #cbd5e1;
  --neutral-400: #94a3b8;
  --neutral-500: #64748b;
  --neutral-600: #475569;
  --neutral-700: #334155;
  --neutral-800: #1e293b;
  --neutral-900: #0f172a;

  /* Accent Colors */
  --accent-amber: #f59e0b;
  --accent-red: #ef4444;
  --accent-purple: #8b5cf6;
  --accent-pink: #ec4899;

  /* Semantic Colors */
  --success: var(--secondary-500);
  --warning: var(--accent-amber);
  --error: var(--accent-red);
  --info: var(--primary-500);

  /* Shadows */
  --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);

  /* Border Radius */
  --radius-xs: 0.25rem;
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;

  /* Typography Scale */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;

  /* Spacing Scale */
  --space-0: 0;
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
}

/* Base Styles */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  background: var(--neutral-0);
  color: var(--neutral-800);
  line-height: 1.5;
}

/* Popup Specific Styles */
body.popup {
  width: 600px !important;
  height: 850px !important;
  min-width: 600px !important;
  min-height: 850px !important;
  max-width: 600px !important;
  max-height: 850px !important;
  overflow: hidden;
  background: linear-gradient(135deg, var(--neutral-50) 0%, var(--primary-50) 50%, var(--secondary-50) 100%);
  position: relative;
}

/* Options Page Styles */
body.options {
  width: 100vw;
  height: 100vh;
  overflow-x: hidden;
  overflow-y: auto;
  background: linear-gradient(135deg, var(--neutral-0) 0%, var(--neutral-50) 50%, var(--primary-50) 100%);
}

#root {
  width: 100% !important;
  height: 100% !important;
  min-width: 600px !important;
  min-height: 850px !important;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* Ensure popup container takes full dimensions */
body.popup #root > div {
  width: 600px !important;
  height: 850px !important;
  min-width: 600px !important;
  min-height: 850px !important;
}

/* Browser Extension Popup Window Sizing */
html {
  width: 600px !important;
  height: 850px !important;
  min-width: 600px !important;
  min-height: 850px !important;
}

/* Force popup window dimensions for Chrome extensions */
@media all {
  html, body {
    width: 600px !important;
    height: 850px !important;
    min-width: 600px !important;
    min-height: 850px !important;
    max-width: 600px !important;
    max-height: 850px !important;
  }
}

/* Modern Component Styles */
@layer components {
  /* Card Components */
  .card {
    @apply bg-white rounded-xl border border-slate-200 shadow-sm;
  }

  .card-elevated {
    @apply bg-white rounded-xl border border-slate-200/60 shadow-lg backdrop-blur-sm;
  }

  .card-glass {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: var(--shadow-lg);
    border-radius: var(--radius-xl);
  }

  /* Button Components */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .btn-primary {
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
    color: white;
    border: none;
    box-shadow: var(--shadow-sm);
  }

  .btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }

  .btn-secondary {
    background: var(--neutral-0);
    color: var(--neutral-700);
    border: 1px solid var(--neutral-200);
    box-shadow: var(--shadow-xs);
  }

  .btn-secondary:hover {
    background: var(--neutral-50);
    border-color: var(--neutral-300);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
  }

  .btn-success {
    background: linear-gradient(135deg, var(--secondary-500), var(--secondary-600));
    color: white;
    border: none;
    box-shadow: var(--shadow-sm);
  }

  .btn-success:hover {
    background: linear-gradient(135deg, var(--secondary-600), var(--secondary-700));
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }

  .btn-danger {
    background: linear-gradient(135deg, var(--error), #dc2626);
    color: white;
    border: none;
    box-shadow: var(--shadow-sm);
  }

  .btn-danger:hover {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }

  /* Input Components */
  .input {
    @apply w-full px-3 py-2 text-sm border border-slate-200 rounded-lg bg-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200;
  }

  .input-glass {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(226, 232, 240, 0.6);
    border-radius: var(--radius-lg);
    transition: all 0.2s ease;
  }

  .input-glass:focus {
    background: rgba(255, 255, 255, 0.95);
    border-color: var(--primary-400);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  /* Badge Components */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-primary {
    @apply bg-blue-100 text-blue-800;
  }

  .badge-success {
    @apply bg-green-100 text-green-800;
  }

  .badge-warning {
    @apply bg-amber-100 text-amber-800;
  }

  .badge-error {
    @apply bg-red-100 text-red-800;
  }
}

/* Modern Scrollbar Styles */
.scrollbar-modern::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-modern::-webkit-scrollbar-track {
  background: var(--neutral-100);
  border-radius: var(--radius-lg);
  margin: var(--space-2);
}

.scrollbar-modern::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, var(--primary-400), var(--primary-500));
  border-radius: var(--radius-lg);
  border: 1px solid var(--neutral-0);
  transition: all 0.2s ease;
}

.scrollbar-modern::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, var(--primary-500), var(--primary-600));
  transform: scaleX(1.1);
}

.scrollbar-modern::-webkit-scrollbar-corner {
  background: transparent;
}

/* Thin Scrollbar for Compact Areas */
.scrollbar-thin::-webkit-scrollbar {
  width: 4px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: var(--neutral-300);
  border-radius: var(--radius-md);
  transition: background 0.2s ease;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: var(--neutral-400);
}

/* Modern Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(16px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-16px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideLeft {
  from {
    opacity: 0;
    transform: translateX(16px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideRight {
  from {
    opacity: 0;
    transform: translateX(-16px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.96);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-4px);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 8px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 16px rgba(59, 130, 246, 0.6);
  }
}

/* Animation Utility Classes */
.animate-fade-in {
  animation: fadeIn 0.3s ease-out forwards;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out forwards;
}

.animate-slide-down {
  animation: slideDown 0.3s ease-out forwards;
}

.animate-slide-left {
  animation: slideLeft 0.3s ease-out forwards;
}

.animate-slide-right {
  animation: slideRight 0.3s ease-out forwards;
}

.animate-scale-in {
  animation: scaleIn 0.2s ease-out forwards;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-bounce {
  animation: bounce 1s ease-in-out infinite;
}

.animate-shimmer {
  animation: shimmer 2s infinite linear;
  background: linear-gradient(90deg, var(--neutral-200) 25%, var(--neutral-300) 50%, var(--neutral-200) 75%);
  background-size: 200px 100%;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

/* Utility Classes */
.glass-effect {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.glass-dark {
  background: rgba(15, 23, 42, 0.8);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.gradient-primary {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
}

.gradient-secondary {
  background: linear-gradient(135deg, var(--secondary-500), var(--secondary-600));
}

.gradient-surface {
  background: linear-gradient(135deg, var(--neutral-0), var(--neutral-50));
}

.text-gradient {
  background: linear-gradient(135deg, var(--primary-600), var(--secondary-600));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Focus States */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
}

.focus-ring-inset {
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset;
}

/* Hover Effects */
.hover-lift {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.hover-scale {
  transition: transform 0.2s ease;
}

.hover-scale:hover {
  transform: scale(1.02);
}

/* Status Indicators */
.status-dot {
  @apply w-2 h-2 rounded-full;
}

.status-online {
  @apply bg-green-500;
  box-shadow: 0 0 8px rgba(34, 197, 94, 0.4);
}

.status-offline {
  @apply bg-red-500;
  box-shadow: 0 0 8px rgba(239, 68, 68, 0.4);
}

.status-warning {
  @apply bg-amber-500;
  box-shadow: 0 0 8px rgba(245, 158, 11, 0.4);
}

/* Range Slider Styles */
input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
  width: 100%;
  height: 6px;
}

input[type="range"]::-webkit-slider-track {
  background: var(--neutral-200);
  height: 6px;
  border-radius: var(--radius-lg);
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  background: var(--primary-500);
  height: 20px;
  width: 20px;
  border-radius: 50%;
  border: 2px solid var(--neutral-0);
  box-shadow: var(--shadow-sm);
  cursor: pointer;
  transition: all 0.2s ease;
}

input[type="range"]::-webkit-slider-thumb:hover {
  background: var(--primary-600);
  transform: scale(1.1);
  box-shadow: var(--shadow-md);
}

input[type="range"]::-moz-range-track {
  background: var(--neutral-200);
  height: 6px;
  border-radius: var(--radius-lg);
  border: none;
}

input[type="range"]::-moz-range-thumb {
  background: var(--primary-500);
  height: 20px;
  width: 20px;
  border-radius: 50%;
  border: 2px solid var(--neutral-0);
  box-shadow: var(--shadow-sm);
  cursor: pointer;
  transition: all 0.2s ease;
}

/* Toggle Switch Styles */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--neutral-300);
  transition: 0.3s ease;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background: var(--neutral-0);
  transition: 0.3s ease;
  border-radius: 50%;
  box-shadow: var(--shadow-sm);
}

input:checked + .toggle-slider {
  background: var(--primary-500);
}

input:checked + .toggle-slider:before {
  transform: translateX(20px);
}

/* Modern Offer Card Styles */
.offer-card {
  background: var(--neutral-0);
  border: 1px solid var(--neutral-200);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.offer-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-300);
}

.offer-card.selected {
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.offer-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.05), transparent);
  transition: left 0.5s ease;
}

.offer-card:hover::before {
  left: 100%;
}

/* Modern Control Panel Styles */
.control-panel {
  background: var(--neutral-0);
  border: 1px solid var(--neutral-200);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  backdrop-filter: blur(8px);
}

.control-section {
  padding: var(--space-4);
  border-bottom: 1px solid var(--neutral-100);
}

.control-section:last-child {
  border-bottom: none;
}

.control-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--space-3);
}

.control-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-lg);
  font-weight: 500;
  font-size: var(--text-sm);
  transition: all 0.2s ease;
  cursor: pointer;
  border: 1px solid;
}

.control-button.active {
  background: var(--primary-500);
  color: var(--neutral-0);
  border-color: var(--primary-600);
  box-shadow: var(--shadow-sm);
}

.control-button.inactive {
  background: var(--neutral-100);
  color: var(--neutral-600);
  border-color: var(--neutral-200);
}

.control-button:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Modern Notification Styles */
.notification-panel {
  background: var(--neutral-0);
  border: 1px solid var(--neutral-200);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  backdrop-filter: blur(8px);
}

.notification-success {
  background: var(--secondary-50);
  border-color: var(--secondary-200);
  color: var(--secondary-800);
}

.notification-error {
  background: #fef2f2;
  border-color: #fecaca;
  color: #991b1b;
}

.notification-warning {
  background: #fffbeb;
  border-color: #fed7aa;
  color: #92400e;
}

.notification-info {
  background: var(--primary-50);
  border-color: var(--primary-200);
  color: var(--primary-800);
}

/* Modern Loading Spinner */
.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--neutral-200);
  border-top: 2px solid var(--primary-500);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Responsive Design */
@media (max-width: 480px) {
  .mobile-hidden {
    display: none;
  }

  .mobile-full {
    width: 100%;
  }

  body.popup {
    width: 100vw;
    height: 100vh;
  }
}
