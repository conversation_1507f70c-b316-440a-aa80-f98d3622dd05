var ne=Object.defineProperty;var ae=(e,t,o)=>t in e?ne(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o;var M=(e,t,o)=>(ae(e,typeof t!="symbol"?t+"":t,o),o);import{D as B}from"./index-357a2da0.js";import{i as J,a as V}from"./extension-context-5094bf00.js";const $=class ${constructor(){M(this,"activeNotifications",new Map);M(this,"settings",{autoCloseDelay:3e4,playSound:!0})}static getInstance(){return $.instance||($.instance=new $),$.instance}updateSettings(t){this.settings={...this.settings,...t}}async showNotification(t,o){const r={...this.settings,...o};try{await this.showChromeNotification(t,r),await this.highlightOfferOnPage(t),r.playSound&&await this.playNotificationSound()}catch(n){console.error("Error showing notification:",n)}}async showChromeNotification(t,o){var a,u;if(typeof chrome>"u"||!chrome.notifications)throw new Error("Chrome notifications not available");const r=`offer_${t.id}_${Date.now()}`,n=`New ${t.operationType} Offer`,s=`${t.grossAmount} ${((a=t.currency)==null?void 0:a.symbol)||((u=t.walletCurrency)==null?void 0:u.symbol)}`;await chrome.notifications.create(r,{type:"basic",iconUrl:"icons/icon48.png",title:n,message:s,contextMessage:t.peer?`From: ${t.peer.firstName} ${t.peer.lastName}`:void 0,buttons:[{title:"Accept"},{title:"Ignore"}],requireInteraction:!0}),this.activeNotifications.set(t.id,r),setTimeout(()=>{this.closeNotification(t.id)},o.autoCloseDelay)}async highlightOfferOnPage(t){var o;try{const r=await chrome.tabs.query({active:!0,currentWindow:!0});(o=r[0])!=null&&o.id&&await chrome.tabs.sendMessage(r[0].id,{type:"HIGHLIGHT_OFFER",data:{offer:t}})}catch(r){console.log("Could not highlight offer on page:",r)}}closeNotification(t){const o=this.activeNotifications.get(t);if(o){try{typeof o=="string"&&o.startsWith("offer_")&&chrome.notifications.clear(o)}catch(r){console.error("Error closing notification:",r)}this.activeNotifications.delete(t)}}closeAllNotifications(){for(const[t]of this.activeNotifications)this.closeNotification(t)}async playNotificationSound(){var t;try{if(typeof chrome<"u"&&chrome.offscreen){try{await chrome.offscreen.createDocument({url:"sounds/beep.html",reasons:["AUDIO_PLAYBACK"],justification:"Play notification sound"})}catch(o){if(!((t=o.message)!=null&&t.includes("Only a single offscreen document")))throw o}chrome.runtime.sendMessage({type:"PLAY_SOUND"})}}catch(o){console.error("Error playing notification sound:",o)}}handleMessage(t,o){var r,n;switch(t.type){case"ACCEPT_OFFER":this.handleOfferAction("accept",(r=t.data)==null?void 0:r.offerId);break;case"IGNORE_OFFER":this.handleOfferAction("ignore",(n=t.data)==null?void 0:n.offerId);break}}async handleOfferAction(t,o){if(o)try{if(this.closeNotification(o),typeof chrome<"u"&&chrome.runtime){const r=t==="accept"?"ACCEPT_OFFER":"REJECT_OFFER";await chrome.runtime.sendMessage({type:r,data:{offerId:o}})}}catch(r){console.error(`Error handling ${t} action:`,r)}}static isSupported(){return typeof chrome<"u"&&!!chrome.notifications}getAvailableMethods(){const t=[];return typeof chrome<"u"&&chrome.notifications&&t.push("chrome"),t}};M($,"instance");let Y=$;const z=Y.getInstance();class se{constructor(t={}){M(this,"config");M(this,"defaultAliases",{paypal:["paypal","pp","pay pal"],zelle:["zelle","zell","zele"],venmo:["venmo","venm","vemo"],cashapp:["cashapp","cash app","cash-app","$cashtag"],wise:["wise","transferwise","transfer wise"],revolut:["revolut","revolt","revol"],skrill:["skrill","skril","skrilla"],neteller:["neteller","neteler","netelr"],perfectmoney:["perfectmoney","perfect money","pm","perfect-money"],webmoney:["webmoney","web money","wm","web-money"],bitcoin:["bitcoin","btc","bit coin"],ethereum:["ethereum","eth","ether"],usdt:["usdt","tether","usd-t"],binance:["binance","bnb","binance pay"],bank:["bank","banking","bank transfer","wire"],credit:["credit","credit card","cc","visa","mastercard"],debit:["debit","debit card","dc"]});this.config={threshold:.7,enableAliases:!0,customAliases:{},...t}}normalizePaymentMethod(t){return t?t.replace(/^CATEGORY_TREE:AIRTM_/i,"").replace(/^E_TRANSFER_/i,"").replace(/^AIRTM_/i,"").replace(/[_-]/g," ").toLowerCase().trim().replace(/\s+/g," "):""}levenshteinDistance(t,o){const r=Array(o.length+1).fill(null).map(()=>Array(t.length+1).fill(null));for(let n=0;n<=t.length;n++)r[0][n]=n;for(let n=0;n<=o.length;n++)r[n][0]=n;for(let n=1;n<=o.length;n++)for(let s=1;s<=t.length;s++){const a=t[s-1]===o[n-1]?0:1;r[n][s]=Math.min(r[n][s-1]+1,r[n-1][s]+1,r[n-1][s-1]+a)}return r[o.length][t.length]}calculateSimilarity(t,o){if(t===o)return 1;if(!t||!o)return 0;const r=Math.max(t.length,o.length);return 1-this.levenshteinDistance(t,o)/r}wordBasedMatch(t,o){const r=t.split(" ").filter(a=>a.length>0),n=o.split(" ").filter(a=>a.length>0);if(n.length===0)return 0;let s=0;for(const a of n)r.some(u=>u.includes(a)||a.includes(u)||this.calculateSimilarity(u,a)>.8)&&s++;return s/n.length}getAliases(t){const o=this.normalizePaymentMethod(t),r=[o];if(this.config.enableAliases){for(const[,n]of Object.entries(this.config.customAliases))if(n.some(s=>this.normalizePaymentMethod(s)===o)){r.push(...n.map(s=>this.normalizePaymentMethod(s)));break}for(const[,n]of Object.entries(this.defaultAliases))if(n.some(s=>this.normalizePaymentMethod(s)===o)){r.push(...n.map(s=>this.normalizePaymentMethod(s)));break}}return[...new Set(r)]}matchPaymentMethod(t,o){const r=this.normalizePaymentMethod(t),n=this.getAliases(o);let s=0,a="";for(const u of n){if(r===u)return{isMatch:!0,score:1,matchedAlias:u};if(r.includes(u)||u.includes(r)){const i=Math.max(u.length/r.length,r.length/u.length)*.9;i>s&&(s=i,a=u)}const h=this.wordBasedMatch(r,u)*.85;h>s&&(s=h,a=u);const l=this.calculateSimilarity(r,u)*.8;l>s&&(s=l,a=u)}return{isMatch:s>=this.config.threshold,score:s,matchedAlias:s>=this.config.threshold?a:void 0}}matchAnyPaymentMethod(t,o){let r={isMatch:!1,score:0};for(const n of o){const s=this.matchPaymentMethod(t,n);if(s.score>r.score&&(r=s),s.score===1)break}return r}updateConfig(t){this.config={...this.config,...t}}getConfig(){return{...this.config}}}let c=B;const q=new se,_={totalOffers:0,newOffers:0,acceptedOffers:0,rejectedOffers:0,averageRate:0,lastUpdate:new Date().toISOString()};let P=new Map,K=Date.now();const ie=2*60*1e3,ce=60*60*1e3;async function le(){try{const e=await chrome.storage.local.get("notified_offers");e.notified_offers?(P=new Map(Object.entries(e.notified_offers)),console.log(`Loaded ${P.size} notified offers from storage`)):(P=new Map,console.log("No previously notified offers found in storage"))}catch(e){console.error("Error loading notified offers:",e),P=new Map}}async function Q(){try{const e=Object.fromEntries(P);await chrome.storage.local.set({notified_offers:e})}catch(e){console.error("Error saving notified offers:",e)}}let Z=!1;async function W(){var e,t;if(Z){console.log("Background service worker already initialized, skipping...");return}console.log("Airtm Monitor Pro: Background service worker initializing...");try{if(typeof chrome>"u"||!chrome.runtime)throw new Error("Chrome runtime APIs not available");await le(),await $e(),ue(),Ue(),ke(),await Fe(),oe(),Z=!0,console.log("Background service worker initialized successfully"),Te()}catch(o){console.error("Error initializing background service worker:",o);try{(e=chrome.action)==null||e.setBadgeText({text:"!"}),(t=chrome.action)==null||t.setBadgeBackgroundColor({color:"#ff0000"})}catch(r){console.error("Could not set error badge:",r)}}}function ue(){chrome.runtime.onMessage.hasListeners()&&chrome.runtime.onMessage.removeListener,chrome.runtime.onMessage.addListener((e,t,o)=>{var r,n,s;if(console.log("Background received message:",e.type,"from:",((r=t.tab)==null?void 0:r.url)||"popup/options"),!e||typeof e.type!="string"){console.error("Invalid message received:",e),o({success:!1,error:"Invalid message format"});return}try{switch(e.type){case"OFFERS_UPDATE":return pe(e.data).then(()=>o({success:!0})).catch(a=>{console.error("Error handling offers update:",a),o({success:!1,error:a.message})}),!0;case"SETTINGS_UPDATE":return ve(e.data).then(()=>o({success:!0})).catch(a=>{console.error("Error handling settings update:",a),o({success:!1,error:a.message})}),!0;case"ACCEPT_OFFER":return j((n=e.data)==null?void 0:n.offerId).then(a=>o({success:!0,result:a})).catch(a=>{console.error("Error handling accept offer:",a),o({success:!1,error:a.message})}),!0;case"REJECT_OFFER":return te((s=e.data)==null?void 0:s.offerId).then(a=>o({success:!0,result:a})).catch(a=>{console.error("Error handling reject offer:",a),o({success:!1,error:a.message})}),!0;case"OPERATION_DETAILS_UPDATE":return _e(e.data).then(()=>o({success:!0})).catch(a=>{console.error("Error handling operation details update:",a),o({success:!1,error:a.message})}),!0;case"OPERATION_ACCEPTED":return Se(e.data).then(()=>o({success:!0})).catch(a=>{console.error("Error handling operation accepted:",a),o({success:!1,error:a.message})}),!0;case"OPERATION_NOT_AVAILABLE":return Re(e.data).then(()=>o({success:!0})).catch(a=>{console.error("Error handling operation not available:",a),o({success:!1,error:a.message})}),!0;case"OPERATION_ACCEPT_ERROR":return Ne(e.data).then(()=>o({success:!0})).catch(a=>{console.error("Error handling operation accept error:",a),o({success:!1,error:a.message})}),!0;case"SEND_TELEGRAM_OPERATION_UPDATE":return De(e.data).then(()=>o({success:!0})).catch(a=>{console.error("Error sending Telegram operation update:",a),o({success:!1,error:a.message})}),!0;case"OPERATION_DECLINED":return Pe(e.data).then(()=>o({success:!0})).catch(a=>{console.error("Error handling operation declined:",a),o({success:!1,error:a.message})}),!0;case"OPERATION_DECLINE_ERROR":return Ie(e.data).then(()=>o({success:!0})).catch(a=>{console.error("Error handling operation decline error:",a),o({success:!1,error:a.message})}),!0;case"GET_POPUP_DATA":return de().then(a=>o({success:!0,data:a})).catch(a=>{console.error("Error handling get popup data:",a),o({success:!1,error:a.message})}),!0;case"PING":console.log("Ping received, responding with alive status"),o({success:!0,message:"Background script is alive",timestamp:new Date().toISOString()});break;default:console.warn("Unknown message type:",e.type),o({success:!1,error:"Unknown message type: "+e.type})}}catch(a){console.error("Error in message listener:",a),o({success:!1,error:"Internal error: "+(a instanceof Error?a.message:"Unknown error")})}}),chrome.runtime.onConnect.addListener(e=>{console.log("Port connected:",e.name),e.onDisconnect.addListener(()=>{console.log("Port disconnected:",e.name),chrome.runtime.lastError&&console.error("Port disconnect error:",chrome.runtime.lastError)})}),chrome.commands&&chrome.commands.onCommand&&chrome.commands.onCommand.addListener(e=>{console.log("Chrome command received:",e),chrome.tabs.query({url:"*://app.airtm.com/peer-transfers/available*"},t=>{t.length>0&&t[0].id?chrome.tabs.sendMessage(t[0].id,{type:"EXECUTE_COMMAND",command:e}).catch(o=>{console.log("Could not send command to content script:",o)}):console.log("No active Airtm tab found for command:",e)})})}async function de(){try{const t=(await chrome.storage.local.get("current_offers")).current_offers||[];return console.log("Sending popup data:",{offersCount:t.length,settings:c,stats:_}),{offers:t,settings:c,stats:_}}catch(e){throw console.error("Error getting popup data:",e),e}}function me(){const e=Date.now();if(e-K<ie)return;const t=e-ce;let o=0;for(const[r,n]of P.entries())n<t&&(P.delete(r),o++);K=e,o>0&&(console.log(`🧹 Cleaned up ${o} old notified offers (older than 1 hour)`),Q())}function ge(e){return P.has(e)}function fe(e){P.set(e,Date.now()),Q()}async function pe(e){try{const{offers:t,timestamp:o}=e;if(!Array.isArray(t))throw new Error("Invalid offers data");if(console.log("Processing "+t.length+" offers"),await chrome.storage.local.set({current_offers:t}),me(),!c.monitoring){console.log("Monitoring disabled, skipping offer processing"),H(t),_.lastUpdate=o||new Date().toISOString();try{chrome.runtime.sendMessage({type:"OFFERS_PROCESSED",data:{offers:t,stats:_}}).catch(s=>{console.log("Could not notify popup (popup may be closed):",s.message)})}catch(s){console.log("Error sending OFFERS_PROCESSED message:",s)}return}H(t);const r=he(t);console.log(r.length+" offers passed filters");const n=r.filter(s=>!ge(s.id));console.log(n.length+" new offers (not previously notified)"),n.length>0&&c.soundEnabled&&c.notificationsEnabled&&(console.log("Playing sound notification once for "+n.length+" new offers"),await ee());for(const s of n)fe(s.id),await we(s,!1);_.lastUpdate=o||new Date().toISOString();try{chrome.runtime.sendMessage({type:"OFFERS_PROCESSED",data:{offers:t,stats:_}}).catch(s=>{console.log("Could not notify popup (popup may be closed):",s.message)})}catch(s){console.log("Error sending OFFERS_PROCESSED message:",s)}}catch(t){console.error("Error handling offers update:",t)}}function he(e){return e.filter(t=>{var r,n,s,a,u,h;const o=typeof t.grossAmount=="string"?parseFloat(t.grossAmount):t.grossAmount;if(o<c.minAmount||o>c.maxAmount)return!1;if(c.preferredCurrencies.length>0){const l=((r=t.currency)==null?void 0:r.symbol)||((n=t.walletCurrency)==null?void 0:n.symbol)||"";if(!c.preferredCurrencies.includes(l))return!1}if(c.paymentMethods.length>0){const l=((u=(a=(s=t.makerPaymentMethod)==null?void 0:s.version)==null?void 0:a.category)==null?void 0:u.translationTag)||"";if(c.fuzzyMatching.enabled){const i=q.matchAnyPaymentMethod(l,c.paymentMethods);if(i.isMatch)console.log(`Offer ${t.id} matched payment method "${l}" with alias "${i.matchedAlias}" (score: ${i.score.toFixed(2)})`);else return console.log(`Offer ${t.id} filtered out: payment method "${l}" doesn't match any configured methods (best score: ${i.score.toFixed(2)})`),!1}else{let i=l;if(i.startsWith("CATEGORY_TREE:AIRTM_")&&(i=i.replace("CATEGORY_TREE:AIRTM_","")),i.startsWith("E_TRANSFER_")&&(i=i.replace("E_TRANSFER_","")),!c.paymentMethods.some(d=>i.includes(d)))return console.log(`Offer ${t.id} filtered out: payment method "${i}" doesn't match any configured methods (legacy matching)`),!1}}if(c.countries.length>0){const l=((h=t.peer)==null?void 0:h.country)||"";if(!c.countries.includes(l))return!1}if(c.keywords.length>0){const l=JSON.stringify(t).toLowerCase();if(!c.keywords.some(i=>l.includes(i.toLowerCase())))return!1}if(c.blacklistKeywords.length>0){const l=JSON.stringify(t).toLowerCase();if(c.blacklistKeywords.some(i=>l.includes(i.toLowerCase())))return console.log(`Offer ${t.id} filtered out by blacklist keyword`),!1}return!0})}async function ye(e){try{const t=await chrome.tabs.query({url:"*://app.airtm.com/peer-transfers/available*"});if(t.length===0){console.log("No Airtm tab found, opening new tab");const r=await chrome.tabs.create({url:"https://app.airtm.com/peer-transfers/available",active:!0});if(await new Promise(n=>setTimeout(n,3e3)),r.windowId)try{await chrome.windows.update(r.windowId,{focused:!0,state:"maximized"}),console.log("✅ New window maximized and focused")}catch(n){console.warn("⚠️ Failed to maximize new window:",n);try{await chrome.windows.update(r.windowId,{focused:!0}),console.log("✅ New window focused (without maximizing)")}catch(s){console.error("❌ Failed to focus new window:",s)}}return}const o=t[0];if(o.windowId){try{await chrome.windows.update(o.windowId,{focused:!0,state:"maximized"}),console.log("✅ Window maximized and focused for offer:",e.id)}catch(n){console.warn("⚠️ Failed to maximize window:",n);try{await chrome.windows.update(o.windowId,{focused:!0}),console.log("✅ Window focused (without maximizing) for offer:",e.id)}catch(s){console.error("❌ Failed to focus window:",s)}}let r=!1;for(let n=1;n<=3;n++)try{await chrome.tabs.update(o.id,{active:!0}),console.log(`✅ Tab activated (attempt ${n}) for offer:`,e.id),r=!0;break}catch(s){console.warn(`⚠️ Tab activation attempt ${n} failed:`,s),n<3&&await new Promise(a=>setTimeout(a,500))}r||console.error("❌ Failed to activate tab after 3 attempts"),await new Promise(n=>setTimeout(n,1e3));try{await chrome.tabs.sendMessage(o.id,{type:"FOCUS_OFFER",data:{offerId:e.id}}),console.log("✅ Focus message sent to content script for offer:",e.id)}catch(n){console.log("⚠️ Could not send focus message to content script:",n)}}else console.error("❌ No window ID found for Airtm tab")}catch(t){console.error("❌ Error maximizing window and focusing offer:",t)}}async function we(e,t=!0){try{console.log("Processing offer "+e.id),await ye(e),c.notificationsEnabled&&await Ae(e,t),console.log("Checking Telegram settings:",{botToken:c.telegramBotToken?"***SET***":"NOT SET",chatId:c.telegramChatId?"***SET***":"NOT SET"}),c.telegramBotToken&&c.telegramChatId?(console.log("Sending Telegram message for offer:",e.id),await U(e)):console.log("Telegram not configured - skipping message"),c.webhookUrl?(console.log("Sending webhook message for offer:",e.id),await Ee(e)):console.log("Webhook not configured - skipping webhook"),c.autoAccept&&await j(e.id)}catch(o){console.error("Error processing offer "+e.id+":",o)}}async function Ae(e,t=!0){var o,r;try{z.updateSettings({autoCloseDelay:3e4,playSound:c.soundEnabled&&t}),await z.showNotification(e)}catch(n){console.error("Error sending notification:",n);try{const s=v(e),a="New "+e.operationType+" Offer";let u=`${s.amount} ${s.currency}`;s.conversionNote&&(u+=` ${s.conversionNote}`),await chrome.notifications.create({type:"basic",iconUrl:"icons/icon48.png",title:a,message:u,contextMessage:"From: "+(((o=e.peer)==null?void 0:o.firstName)||"")+" "+(((r=e.peer)==null?void 0:r.lastName)||""),buttons:[{title:"Accept"},{title:"Reject"}]}),c.soundEnabled&&t&&await ee()}catch(s){console.error("Fallback notification also failed:",s)}}}async function U(e){try{const t=typeof e=="string"?e:L(e),o="https://api.telegram.org/bot"+c.telegramBotToken+"/sendMessage";console.log("Sending Telegram message to:",c.telegramChatId),console.log("Message content:",t);const r=await fetch(o,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({chat_id:c.telegramChatId,text:t,parse_mode:"Markdown"})}),n=await r.json();r.ok?console.log("✅ Telegram message sent successfully:",n):console.error("❌ Telegram API error:",n)}catch(t){console.error("❌ Error sending Telegram message:",t)}}async function Ee(e){try{const t={timestamp:new Date().toISOString(),offer:e,event:"new_offer",metadata:{extensionVersion:"1.0.0",source:"airtm-monitor-pro"}};console.log("Sending webhook to:",c.webhookUrl),console.log("Webhook payload:",t);const o=await fetch(c.webhookUrl,{method:"POST",headers:{"Content-Type":"application/json","User-Agent":"Airtm-Monitor-Pro/1.0.0"},body:JSON.stringify(t)});o.ok?console.log("✅ Webhook sent successfully:",o.status):console.error("❌ Webhook failed with status:",o.status,await o.text())}catch(t){console.error("❌ Error sending webhook:",t)}}function Ce(e){return{USA:"🇺🇸",US:"🇺🇸",IND:"🇮🇳",IN:"🇮🇳",GBR:"🇬🇧",GB:"🇬🇧",UK:"🇬🇧",CAN:"🇨🇦",CA:"🇨🇦",AUS:"🇦🇺",AU:"🇦🇺",DEU:"🇩🇪",DE:"🇩🇪",GER:"🇩🇪",FRA:"🇫🇷",FR:"🇫🇷",ESP:"🇪🇸",ES:"🇪🇸",ITA:"🇮🇹",IT:"🇮🇹",BRA:"🇧🇷",BR:"🇧🇷",MEX:"🇲🇽",MX:"🇲🇽",ARG:"🇦🇷",AR:"🇦🇷",COL:"🇨🇴",CO:"🇨🇴",VEN:"🇻🇪",VE:"🇻🇪",PER:"🇵🇪",PE:"🇵🇪",CHL:"🇨🇱",CL:"🇨🇱",URY:"🇺🇾",UY:"🇺🇾",ECU:"🇪🇨",EC:"🇪🇨",BOL:"🇧🇴",BO:"🇧🇴",PRY:"🇵🇾",PY:"🇵🇾",CHN:"🇨🇳",CN:"🇨🇳",JPN:"🇯🇵",JP:"🇯🇵",KOR:"🇰🇷",KR:"🇰🇷",RUS:"🇷🇺",RU:"🇷🇺",TUR:"🇹🇷",TR:"🇹🇷",EGY:"🇪🇬",EG:"🇪🇬",ZAF:"🇿🇦",ZA:"🇿🇦",NGA:"🇳🇬",NG:"🇳🇬",KEN:"🇰🇪",KE:"🇰🇪",GHA:"🇬🇭",GH:"🇬🇭",MAR:"🇲🇦",MA:"🇲🇦",TUN:"🇹🇳",TN:"🇹🇳",DZA:"🇩🇿",DZ:"🇩🇿",MOZ:"🇲🇿",MZ:"🇲🇿",PAN:"🇵🇦",PA:"🇵🇦",IDN:"🇮🇩",ID:"🇮🇩",THA:"🇹🇭",TH:"🇹🇭",VNM:"🇻🇳",VN:"🇻🇳",PHL:"🇵🇭",PH:"🇵🇭",MYS:"🇲🇾",MY:"🇲🇾",SGP:"🇸🇬",SG:"🇸🇬",PAK:"🇵🇰",PK:"🇵🇰",BGD:"🇧🇩",BD:"🇧🇩",LKA:"🇱🇰",LK:"🇱🇰",NPL:"🇳🇵",NP:"🇳🇵",AFG:"🇦🇫",AF:"🇦🇫",IRN:"🇮🇷",IR:"🇮🇷",IRQ:"🇮🇶",IQ:"🇮🇶",SAU:"🇸🇦",SA:"🇸🇦",ARE:"🇦🇪",AE:"🇦🇪",QAT:"🇶🇦",QA:"🇶🇦",KWT:"🇰🇼",KW:"🇰🇼",BHR:"🇧🇭",BH:"🇧🇭",OMN:"🇴🇲",OM:"🇴🇲",JOR:"🇯🇴",JO:"🇯🇴",LBN:"🇱🇧",LB:"🇱🇧",SYR:"🇸🇾",SY:"🇸🇾",ISR:"🇮🇱",IL:"🇮🇱",PSE:"🇵🇸",PS:"🇵🇸"}[e.toUpperCase()]||e}function X(e){var b,T,N,E,A,C,S,k,F,I,R;const t=e.walletCurrency,o=e.currency,r=e.makerPaymentMethod||e.takerPaymentMethod,n=((b=r==null?void 0:r.categoryId)==null?void 0:b.toLowerCase().includes("usdc"))||((E=(N=(T=r==null?void 0:r.version)==null?void 0:T.category)==null?void 0:N.translationTag)==null?void 0:E.toLowerCase().includes("usdc")),s=((A=e.metadata)==null?void 0:A.walletCurrencyPrecision)||(t==null?void 0:t.precision),a=((C=e.metadata)==null?void 0:C.localCurrencyPrecision)||(o==null?void 0:o.precision),u=s===6||a===6,h=((S=e.metadata)==null?void 0:S.isForThirdPartyPaymentMethod)===!1,l=((k=r==null?void 0:r.categoryId)==null?void 0:k.toLowerCase())||"",i=((R=(I=(F=r==null?void 0:r.version)==null?void 0:F.category)==null?void 0:I.translationTag)==null?void 0:R.toLowerCase())||"",d=l.includes("gift-card")||i.includes("gift_card"),m=l.includes("bank")||i.includes("bank"),p=l.includes("card")||i.includes("card"),y=l.includes("transfer")||i.includes("transfer"),g=d||m||p||y;let f=null,w=null;return e.operationType==="BUY"&&g?(f="withdrawal",w="wallet",{isUSDC:!1,usdcField:w,operationType:f}):e.operationType==="SELL"&&g?(f="deposit",w="wallet",{isUSDC:!1,usdcField:w,operationType:f}):n||u||h?(e.operationType==="SELL"&&s===6?(f="withdrawal",w="wallet"):e.operationType==="BUY"&&a===6?(f="deposit",w="local"):(s===6||a===6)&&(f="exchange",w=s===6?"wallet":"local"),{isUSDC:!0,usdcField:w,operationType:f}):{isUSDC:!1,usdcField:null,operationType:null}}function v(e){var t,o,r,n,s,a,u,h,l;try{const i=X(e);let d=((t=e.walletCurrency)==null?void 0:t.symbol)||"$",m=((o=e.currency)==null?void 0:o.symbol)||((r=e.walletCurrency)==null?void 0:r.symbol)||"Unknown";if(i.isUSDC&&(i.usdcField==="wallet"?d="USDC":i.usdcField==="local"&&(m="USDC")),(d===m||!e.currency)&&!i.isUSDC){const A=parseFloat(e.grossAmount||"0");return{amount:O(A),currency:d}}const p=e.rateInfo;if(p&&p.fundsToSendTaker&&p.fundsToReceiveTaker){let A,C,S="";return e.operationType==="BUY"?(A=parseFloat(p.fundsToReceiveTaker),C=parseFloat(p.fundsToSendTaker),i.operationType==="deposit"&&(S="(USDC deposit)")):(A=parseFloat(p.fundsToSendTaker),C=parseFloat(p.fundsToReceiveTaker),i.operationType==="withdrawal"&&(S=`(withdrawal, fees: $${(C-A).toFixed(2)})`)),console.log(`Using rateInfo amounts for ${e.operationType}: ${C} ${d} ↔ ${A} ${m} (from rateInfo)${i.isUSDC?" [USDC operation]":""}`),i.operationType==="withdrawal"?{amount:O(C),currency:d,originalAmount:O(A),originalCurrency:m,exchangeRate:(A/C).toFixed(4),conversionNote:S,operationType:"withdrawal"}:{amount:O(A),currency:m,originalAmount:O(C),originalCurrency:d,exchangeRate:(A/C).toFixed(4),conversionNote:S,operationType:i.operationType}}let y,g=null,f="";e.netAmount&&parseFloat(e.netAmount)>0?y=parseFloat(e.netAmount):y=parseFloat(e.grossAmount||"0");const w=e.metadata,b=e.takerPaymentMethod;if(e.rate?(g=parseFloat(e.rate),f="operation.rate"):(n=e.displayRate)!=null&&n.rate?(g=parseFloat(e.displayRate.rate),f="displayRate.rate"):p&&p.exchangeRate?(g=parseFloat(p.exchangeRate),f="rateInfo.exchangeRate"):(s=w==null?void 0:w.displayRateInfo)!=null&&s.exchangeRate?(g=parseFloat(w.displayRateInfo.exchangeRate),f="metadata.displayRateInfo.exchangeRate"):(a=b==null?void 0:b.rateInfo)!=null&&a.exchangeRate&&(g=parseFloat(b.rateInfo.exchangeRate),f="takerPaymentMethod.rateInfo.exchangeRate"),(!g||g<=0)&&i.isUSDC&&(g=1,f="usdc_parity_default"),!g||g<=0)return{amount:O(y),currency:d,conversionNote:"(rate pending)"};let T;const N=((u=e.displayRate)==null?void 0:u.direction)||"TO_LOCAL_CURRENCY";N==="TO_LOCAL_CURRENCY"?T=y*g:N==="FROM_LOCAL_CURRENCY"?T=y/g:(e.operationType,T=y*g),console.log(`Currency conversion: ${y} ${d} → ${T} ${m} (rate: ${g}, source: ${f}, direction: ${N})${i.isUSDC?" [USDC operation]":""}`);const E={amount:O(T),currency:m,originalAmount:O(y),originalCurrency:d,exchangeRate:g.toString()};return i.isUSDC&&(E.operationType=i.operationType,i.operationType==="withdrawal"?(E.amount=O(y),E.currency=d,E.originalAmount=O(T),E.originalCurrency=m,E.conversionNote=`(withdrawal to ${m})`):i.operationType==="deposit"?E.conversionNote=`(deposit from ${d})`:i.operationType==="exchange"&&(E.conversionNote="(USDC exchange)")),E}catch(i){console.error("Error in currency conversion:",i);const d=parseFloat(e.grossAmount||"0"),m=((h=e.currency)==null?void 0:h.symbol)||((l=e.walletCurrency)==null?void 0:l.symbol)||"Unknown";return{amount:O(d),currency:m,conversionNote:"(conversion error)"}}}function O(e){return e>=1e3?e.toLocaleString("en-US",{minimumFractionDigits:0,maximumFractionDigits:2}):e.toFixed(2).replace(/\.?0+$/,"")}function Te(){var g,f;console.log("🧪 Testing currency conversion with afteraccepting.txt example...");const e={id:"0d77a20b-f2ab-4dd6-8924-9fd102a37652",hash:"F2ABQP4DD6DK8924",operationType:"SELL",status:"ACCEPTED",isMine:!1,createdAt:"2025-06-12T16:23:51.645Z",updatedAt:"2025-06-12T16:24:00.386Z",grossAmount:"11",netAmount:"10.38",metadata:{isForThirdPartyPaymentMethod:!0,localCurrencyPrecision:2,walletCurrencyPrecision:2},walletCurrency:{symbol:"$",id:"USD",name:"US Dollar",precision:2,__typename:"Catalogs__Currency"},currency:{symbol:"£",id:"EGP",name:"Egyptian Pound",precision:2,__typename:"Catalogs__Currency"},peer:{id:"90e670af-de7f-45ba-8de2-e2698558271d",firstName:"احمد",lastName:"محمد السيد عبدالحافظ",createdAt:"2022-04-23T18:23:27.468Z",country:"EGY",countryInfo:{id:"EGY",__typename:"Catalogs__Country"},numbers:{id:"90e670af-de7f-45ba-8de2-e2698558271d",score:4.98,completedOperations:162,__typename:"Numbers__User"},__typename:"Auth__OperationUser"},rate:"49.57",displayRate:{rate:"47.42396313364055299539",direction:"TO_LOCAL_CURRENCY",__typename:"Operations__DisplayRate"},rateInfo:{fundsToSendTaker:"514.55",fundsToReceiveTaker:"10.85",grossAmount:"545.28",netAmount:"514.55",exchangeRate:"49.5709"},__typename:"Operations__Sell"},t=v(e);console.log("📊 Currency Conversion Test Results:"),console.log(`   Input: ${e.grossAmount} ${(g=e.walletCurrency)==null?void 0:g.symbol} (gross)`),console.log(`   Input: ${e.netAmount} ${(f=e.walletCurrency)==null?void 0:f.symbol} (net)`),console.log("   Expected Local: 514.55 £"),console.log("   Expected Wallet: 10.85 $"),console.log(`   Actual Result: ${t.amount} ${t.currency}`),console.log(`   Original Amount: ${t.originalAmount} ${t.originalCurrency}`),console.log(`   Exchange Rate: ${t.exchangeRate}`),console.log(`   Conversion Note: ${t.conversionNote||"None"}`);const o=514.55,r=parseFloat(t.amount.replace(/,/g,"")),n=Math.abs(r-o)<.01;console.log(`✅ Test Result: ${n?"PASSED":"FAILED"}`),n||console.log(`   Expected: ${o}, Got: ${r}`);const s=L(e);console.log("📱 Telegram Message:"),console.log(s),console.log(`
🧪 Testing same-currency scenario (USD to USD)...`);const a={id:"055c2bee-d063-4ced-acac-27b623954fa5",hash:"D063YU4CEDVGACAC",operationType:"SELL",status:"CREATED",isMine:!1,createdAt:"2025-06-14T13:19:58.291Z",updatedAt:null,grossAmount:"62.16",netAmount:"58.87",metadata:{isForThirdPartyPaymentMethod:!0,localCurrencyPrecision:2,walletCurrencyPrecision:2},walletCurrency:{symbol:"$",id:"USD",name:"US Dollar",precision:2,__typename:"Catalogs__Currency"},currency:{symbol:"$",id:"USD",name:"US Dollar",precision:2,__typename:"Catalogs__Currency"},peer:{id:"8021567e-1d57-446e-ae65-ffe04f622241",firstName:"IRIS DANIELA",lastName:"S.",createdAt:"2025-03-17T00:20:06.523Z",country:"PAN",countryInfo:{id:"PAN",__typename:"Catalogs__Country"},numbers:{id:"8021567e-1d57-446e-ae65-ffe04f622241",score:4.47,completedOperations:32,__typename:"Numbers__User"},__typename:"Auth__OperationUser"},rate:"1",rateInfo:{fundsToReceiveTaker:"61.23",fundsToSendTaker:"58.86"},displayRate:{direction:"TO_WALLET_CURRENCY",rate:"1.04026503567787971458",__typename:"Operations__DisplayRate"},makerPaymentMethod:{version:{category:{translationTag:"CATEGORY_TREE:AIRTM_MOBILE_YAPPY"}}},__typename:"Operations__Sell"},u=v(a);console.log("📊 Same Currency Test Results:"),console.log("   Expected: 62.16 $ (no conversion)"),console.log(`   Actual Result: ${u.amount} ${u.currency}`),console.log(`   Original Amount: ${u.originalAmount||"None"}`),console.log(`   Conversion Note: ${u.conversionNote||"None"}`);const h=L(a);console.log("📱 Same Currency Telegram Message:"),console.log(h),console.log(`
🧪 Testing BUY operation scenario (CNY to USD)...`);const l={id:"8d0a8483-3ea0-43b9-9a93-c48f22abe919",hash:"3EA0YE43B9EC9A93",operationType:"BUY",status:"CREATED",isMine:!1,createdAt:"2025-06-14T13:49:43.184Z",updatedAt:null,grossAmount:"278.97",netAmount:"269.84",metadata:{isForThirdPartyPaymentMethod:null,localCurrencyPrecision:2,walletCurrencyPrecision:2},walletCurrency:{symbol:"$",id:"USD",name:"US Dollar",precision:2,__typename:"Catalogs__Currency"},currency:{symbol:"¥",id:"CNY",name:"Chinese Yuan",precision:2,__typename:"Catalogs__Currency"},peer:{id:"d37923bd-23c8-4f2c-99fc-15c665e090ea",firstName:"ALY ADRIANO",lastName:"S.",createdAt:"2025-02-11T17:11:19.413Z",country:"MOZ",countryInfo:{id:"MOZ",__typename:"Catalogs__Country"},numbers:{id:"d37923bd-23c8-4f2c-99fc-15c665e090ea",score:4.28,completedOperations:82,__typename:"Numbers__User"},__typename:"Auth__OperationUser"},rate:"7.1692",rateInfo:{fundsToReceiveTaker:"2000",fundsToSendTaker:"273.75",netAmount:"1934.55",grossAmount:"2000"},displayRate:{direction:"TO_LOCAL_CURRENCY",rate:"7.30593607305936073059",__typename:"Operations__DisplayRate"},makerPaymentMethod:{version:{category:{translationTag:"CATEGORY_TREE:AIRTM_E_TRANSFER_ALIPAY"}}},__typename:"Operations__Buy"},i=v(l);console.log("📊 BUY Operation Test Results:"),console.log("   Expected: 2,000 ¥ (273.75 $)"),console.log(`   Actual Result: ${i.amount} ${i.currency}`),console.log(`   Original Amount: ${i.originalAmount} ${i.originalCurrency}`),console.log(`   Exchange Rate: ${i.exchangeRate}`);const d=L(l);console.log("📱 BUY Operation Telegram Message:"),console.log(d),console.log(`
🧪 Testing USDC withdrawal scenario...`);const m={id:"withdrawal-001",hash:"WITHDRAWAL001",operationType:"SELL",status:"CREATED",isMine:!0,createdAt:"2025-06-14T15:30:00.000Z",updatedAt:null,grossAmount:"14.23",netAmount:"13.85",metadata:{isForThirdPartyPaymentMethod:!1,walletCurrencyPrecision:6,localCurrencyPrecision:2},walletCurrency:{symbol:"$",id:"USD",name:"US Dollar",precision:6,__typename:"Catalogs__Currency"},currency:{symbol:"$",id:"USD",name:"US Dollar",precision:2,__typename:"Catalogs__Currency"},peer:{id:"withdrawal-peer",firstName:"Bank",lastName:"Withdrawal",createdAt:"2025-01-01T00:00:00.000Z",country:"USA",countryInfo:{id:"USA",__typename:"Catalogs__Country"},numbers:{id:"withdrawal-peer",score:5,completedOperations:1e3,__typename:"Numbers__User"},__typename:"Auth__OperationUser"},rate:"1.0",rateInfo:{fundsToReceiveTaker:"14.23",fundsToSendTaker:"13.50",grossAmount:"14.23",netAmount:"13.85"},displayRate:{direction:"TO_LOCAL_CURRENCY",rate:"1.0",__typename:"Operations__DisplayRate"},makerPaymentMethod:{categoryId:"airtm:bank:withdrawal",version:{category:{translationTag:"CATEGORY_TREE:AIRTM_BANK_WITHDRAWAL"}}},__typename:"Operations__Sell"},p=v(m);console.log("📊 USDC Withdrawal Test Results:"),console.log("   Expected: -14.23 USDC → 13.50 USD (fees: $0.73)"),console.log(`   Actual Result: ${p.amount} ${p.currency}`),console.log(`   Original Amount: ${p.originalAmount} ${p.originalCurrency}`),console.log(`   Operation Type: ${p.operationType}`),console.log(`   Conversion Note: ${p.conversionNote||"None"}`);const y=L(m);console.log("📱 USDC Withdrawal Telegram Message:"),console.log(y),console.log(`
🧪 Currency conversion tests completed.`)}function L(e){var f,w,b,T,N,E,A,C,S,k,F;const t=v(e),o=X(e),r=((((f=e.peer)==null?void 0:f.firstName)||"")+" "+(((w=e.peer)==null?void 0:w.lastName)||"")).trim(),n=((T=(b=e.peer)==null?void 0:b.numbers)==null?void 0:T.score)||0,s=((E=(N=e.peer)==null?void 0:N.numbers)==null?void 0:E.completedOperations)||0,a=((A=e.peer)==null?void 0:A.country)||"Unknown",u=Ce(a);let l=((k=(S=(C=e.makerPaymentMethod)==null?void 0:C.version)==null?void 0:S.category)==null?void 0:k.translationTag)||"Unknown";l.startsWith("CATEGORY_TREE:AIRTM_")&&(l=l.replace("CATEGORY_TREE:AIRTM_","")),l.startsWith("E_TRANSFER_")&&(l=l.replace("E_TRANSFER_","")),l.startsWith("GIFT_CARD_")&&(l=l.replace("GIFT_CARD_","")),l=l.replace(/_/g," ").toLowerCase().replace(/\b\w/g,I=>I.toUpperCase());const i=((F=e.makerPaymentMethod)==null?void 0:F.categoryId)||"";if(i.includes("gift-card")){const I=i.split(":");if(I.length>2){let R=I[I.length-1].replace(/[-_]/g," ").replace(/\b\w/g,re=>re.toUpperCase());R.toLowerCase()==="ebay"&&(R="eBay"),R.toLowerCase()==="paypal"&&(R="PayPal"),R.toLowerCase()==="amazon"&&(R="Amazon"),l=`${R} Gift Card`}}let d="";o.operationType==="withdrawal"?(d=`-${t.amount} ${t.currency}`,t.originalAmount&&t.originalCurrency&&(d+=` → ${t.originalAmount} ${t.originalCurrency}`)):(d=`${t.amount} ${t.currency}`,t.originalAmount&&t.originalCurrency&&t.originalCurrency!==t.currency&&(d+=` (${t.originalAmount} ${t.originalCurrency})`)),t.conversionNote&&(d+=` ${t.conversionNote}`);let m="";const p=i.includes("gift-card"),y=i.includes("bank")||l.includes("bank"),g=i.includes("card")||l.includes("card");return o.operationType==="withdrawal"?p?m="🎁 ":m="💸 ":o.operationType==="deposit"?y?m="🏦 ":g?m="💳 ":m="💰 ":o.isUSDC&&(m="🪙 "),`${m}${d} : ${l} (${u})
👤 User: ${r} ${n}⭐(${s} trades)`}let D=!1,x=null;async function ee(){return x||(x=(async()=>{var e;try{if(!D)try{await chrome.offscreen.createDocument({url:"sounds/beep.html",reasons:[chrome.offscreen.Reason.AUDIO_PLAYBACK],justification:"Play notification sound for new offers"}),D=!0}catch(t){if((e=t.message)!=null&&e.includes("Only a single offscreen document may be created"))D=!0;else throw t}try{await chrome.runtime.sendMessage({type:"PLAY_SOUND"})}catch(t){throw console.log("Message to offscreen document failed:",t),D=!1,t}}catch(t){console.error("Error playing notification sound:",t),D=!1}finally{try{D&&(await chrome.offscreen.closeDocument(),D=!1)}catch{D=!1}x=null}})(),x)}async function j(e){try{if(!J())throw console.warn("⚠️ Extension context invalid during offer acceptance, cannot proceed"),new Error("Extension context is invalid. Please reload the extension.");const t=await chrome.tabs.query({url:"*://app.airtm.com/peer-transfers/available*"});if(t.length===0)throw new Error("Airtm tab not found");const o=await chrome.tabs.sendMessage(t[0].id,{type:"ACCEPT_OFFER",data:{offerId:e}});if(o!=null&&o.success)return _.acceptedOffers++,console.log("Offer "+e+" accepted successfully"),Oe(e,t[0].id),!0;throw new Error((o==null?void 0:o.error)||"Failed to accept offer")}catch(t){if(console.error("Error accepting offer "+e+":",t),V(t)&&(console.warn("⚠️ Connection error detected, likely due to extension context invalidation"),c.telegramBotToken&&c.telegramChatId))try{await U(`⚠️ Extension Context Issue

Failed to accept offer ${e}
Reason: Extension context lost

Please reload the extension or refresh the page.`)}catch(o){console.error("Failed to send Telegram notification about context issue:",o)}throw t}}async function _e(e){try{console.log("📋 Processing operation details update:",e);const{operation:t,timestamp:o,source:r}=e;if(!t||!t.id){console.warn("Invalid operation data received");return}await chrome.storage.local.set({[`operation_${t.id}`]:{...t,lastUpdated:o,source:r}}),c.telegramBotToken&&c.telegramChatId&&await be(t),console.log(`✅ Operation ${t.id} details updated - Status: ${t.status}`)}catch(t){console.error("❌ Error handling operation details update:",t)}}function Oe(e,t){console.log(`🔍 Starting URL monitoring for accepted offer: ${e}`);let o=0;const r=30,n=setInterval(async()=>{try{o++;const a=(await chrome.tabs.get(t)).url;if(console.log(`🔍 URL check ${o}/${r}: ${a}`),a&&/https:\/\/app\.airtm\.com\/operations\/[A-Z0-9]+/.test(a)){console.log("✅ Operation URL detected:",a);const h=a.split("/operations/")[1];c.telegramBotToken&&c.telegramChatId&&await U(`🎯 Offer ${e} accepted successfully!
📋 Operation ID: ${h}
🔗 URL: ${a}
⏰ Status: Accepted`),await chrome.storage.local.set({[`accepted_operation_${e}`]:{operationId:h,url:a,timestamp:new Date().toISOString(),status:"accepted"}}),clearInterval(n);return}o>=r&&(console.log("⚠️ Operation URL not detected within timeout"),c.telegramBotToken&&c.telegramChatId&&await U(`❌ Offer ${e} acceptance failed or timed out
⏰ Status: Not Available`),await chrome.storage.local.set({[`failed_operation_${e}`]:{timestamp:new Date().toISOString(),status:"not_available",reason:"url_not_detected"}}),clearInterval(n))}catch(s){console.error("❌ Error during URL monitoring:",s),clearInterval(n)}},100)}async function be(e){var t;try{const o=(t=e.metadata)==null?void 0:t.displayRateInfo,r={id:e.id||"",hash:e.hash||"",operationType:e.operationType||"UNKNOWN",status:e.status||"UNKNOWN",isMine:e.isMine||!1,createdAt:e.createdAt||"",updatedAt:e.updatedAt||null,grossAmount:e.grossAmount||"0",netAmount:e.netAmount||"0",metadata:{...e.metadata,displayRateInfo:o},walletCurrency:e.walletCurrency||{symbol:"$"},peer:e.secondParty||e.peer||{firstName:"Unknown",lastName:""},rate:e.rate,displayRate:e.displayRate,rateInfo:{...e.rateInfo},currency:e.currency,makerPaymentMethod:e.makerPaymentMethod,__typename:e.__typename||"Operations__Unknown"},n=v(r),s=e.secondParty?`${e.secondParty.firstName||""} ${e.secondParty.lastName||""}`.trim():e.peer?`${e.peer.firstName||""} ${e.peer.lastName||""}`.trim():"Unknown";let a=`${n.amount} ${n.currency}`;n.conversionNote&&(a+=` ${n.conversionNote}`),n.originalAmount&&n.originalCurrency&&n.originalCurrency!==n.currency&&(a+=` (${n.originalAmount} ${n.originalCurrency})`);const u=`📋 Operation Update
🆔 ID: ${e.hash||e.id}
📊 Status: ${e.status}
💰 Amount: ${a}
🔄 Type: ${e.operationType}
👤 Partner: ${s}
⏰ Updated: ${new Date().toLocaleString()}`;await U(u)}catch(o){console.error("❌ Error sending Telegram operation update:",o)}}async function Se(e){try{console.log("✅ Processing operation acceptance:",e),await chrome.storage.local.set({[`accepted_operation_${e.operationId}`]:{...e,processedAt:new Date().toISOString()}}),_.acceptedOffers++,console.log("✅ Operation acceptance processed successfully")}catch(t){console.error("❌ Error handling operation accepted:",t)}}async function Re(e){try{console.log("🚫 Processing operation not available:",e),await chrome.storage.local.set({[`unavailable_operation_${e.operationId||"unknown"}`]:{...e,processedAt:new Date().toISOString()}}),console.log("🚫 Operation not available processed successfully")}catch(t){console.error("❌ Error handling operation not available:",t)}}async function Ne(e){try{console.log("⚠️ Processing operation accept error:",e),await chrome.storage.local.set({[`error_operation_${Date.now()}`]:{...e,processedAt:new Date().toISOString()}}),console.log("⚠️ Operation accept error processed successfully")}catch(t){console.error("❌ Error handling operation accept error:",t)}}async function Pe(e){try{console.log("🚫 Processing operation decline:",e),await chrome.storage.local.set({[`declined_operation_${Date.now()}`]:{...e,processedAt:new Date().toISOString()}}),_.rejectedOffers++,console.log("🚫 Operation decline processed successfully")}catch(t){console.error("❌ Error handling operation declined:",t)}}async function Ie(e){try{console.log("⚠️ Processing operation decline error:",e),await chrome.storage.local.set({[`decline_error_operation_${Date.now()}`]:{...e,processedAt:new Date().toISOString()}}),console.log("⚠️ Operation decline error processed successfully")}catch(t){console.error("❌ Error handling operation decline error:",t)}}async function De(e){try{console.log("📱 Sending Telegram operation status update:",e);const{operationId:t,status:o}=e;let r;o==="accepted"?r=`✅ *Operation Accepted*

🆔 Operation ID: ${t}
📊 Status: Successfully accepted
⏰ Time: ${new Date().toLocaleString()}

🎉 Great! The operation was accepted successfully.`:o==="Not Available"?r=`🚫 *Operation Not Available*

🆔 Operation ID: ${t}
📊 Status: No longer available
⏰ Time: ${new Date().toLocaleString()}

😔 The operation was taken by someone else or expired.`:r=`📋 *Operation Update*

🆔 Operation ID: ${t}
📊 Status: ${o}
⏰ Time: ${new Date().toLocaleString()}`,await U(r),console.log("📱 Telegram operation status update sent successfully")}catch(t){console.error("❌ Error sending Telegram operation status update:",t)}}async function te(e){try{if(!J())throw console.warn("⚠️ Extension context invalid during offer rejection, cannot proceed"),new Error("Extension context is invalid. Please reload the extension.");const t=await chrome.tabs.query({url:"*://app.airtm.com/peer-transfers/available*"});if(t.length===0)throw new Error("Airtm tab not found");const o=await chrome.tabs.sendMessage(t[0].id,{type:"REJECT_OFFER",data:{offerId:e}});if(o!=null&&o.success)return _.rejectedOffers++,console.log("Offer "+e+" rejected successfully"),!0;throw new Error((o==null?void 0:o.error)||"Failed to reject offer")}catch(t){if(console.error("Error rejecting offer "+e+":",t),V(t)&&(console.warn("⚠️ Connection error detected, likely due to extension context invalidation"),c.telegramBotToken&&c.telegramChatId))try{await U(`⚠️ Extension Context Issue

Failed to reject offer ${e}
Reason: Extension context lost

Please reload the extension or refresh the page.`)}catch(o){console.error("Failed to send Telegram notification about context issue:",o)}throw t}}function H(e){_.totalOffers=e.length,_.newOffers=e.filter(t=>{const o=new Date(t.createdAt),r=new Date(Date.now()-5*60*1e3);return o>r}).length,_.averageRate=0}async function $e(){try{const e=await chrome.storage.sync.get("settings");c={...B,...e.settings},c.monitoring=!0,G(),await chrome.storage.sync.set({settings:c}),console.log("Settings loaded and monitoring enabled:",c)}catch(e){console.error("Error loading settings:",e),c={...B,monitoring:!0},G()}}async function ve(e){try{c={...c,...e},e.fuzzyMatching&&G(),await chrome.storage.sync.set({settings:c}),console.log("Settings updated:",c),oe()}catch(t){throw console.error("Error updating settings:",t),t}}function oe(){c.monitoring?(chrome.action.setBadgeText({text:"ON"}),chrome.action.setBadgeBackgroundColor({color:"#10b981"})):(chrome.action.setBadgeText({text:"OFF"}),chrome.action.setBadgeBackgroundColor({color:"#6b7280"}))}function G(){try{const e=c.fuzzyMatching||{enabled:!1,threshold:.8,enableAliases:!0,customAliases:{}};q.updateConfig({threshold:e.threshold,enableAliases:e.enableAliases,customAliases:e.customAliases}),console.log("Fuzzy matcher configuration updated:",e)}catch(e){console.error("Error updating fuzzy matcher configuration:",e)}}function Ue(){chrome.alarms.onAlarm.addListener(e=>{switch(console.log("Alarm triggered:",e.name),e.name){case"monitoring-check":break;case"cleanup-storage":Me();break}}),chrome.alarms.create("cleanup-storage",{periodInMinutes:60})}function ke(){chrome.notifications.onButtonClicked.addListener(async(e,t)=>{try{const o=e.match(/offer_(.+)_\d+/),r=o?o[1]:e;t===0?await j(r):t===1&&await te(r),chrome.notifications.clear(e)}catch(o){console.error("Error handling notification button click:",o)}}),chrome.runtime.onMessage.addListener((e,t,o)=>{(e.type==="ACCEPT_OFFER"||e.type==="REJECT_OFFER"||e.type==="IGNORE_OFFER")&&z.handleMessage(e,t)})}async function Me(){try{console.log("Cleaning up storage...");const t=(await chrome.storage.local.get(["airtm_offers_timestamp"])).airtm_offers_timestamp;if(t){const o=Date.now()-new Date(t).getTime(),r=24*60*60*1e3;o>r&&(await chrome.storage.local.remove(["airtm_offers","airtm_offers_timestamp"]),console.log("Old offers cleaned up"))}}catch(e){console.error("Error cleaning up storage:",e)}}async function Fe(){try{console.log("🔍 Checking for existing Airtm tabs...");const e=await chrome.tabs.query({url:"*://app.airtm.com/peer-transfers/available*"});if(e.length===0){console.log("📭 No existing Airtm tabs found");return}console.log(`🎯 Found ${e.length} existing Airtm tab(s)`),console.log("ℹ️ Content scripts will be automatically injected by Chrome based on manifest.json");for(const t of e)t.id&&t.url&&console.log(`📋 Found Airtm tab ${t.id}: ${t.url}`)}catch(e){console.error("❌ Error checking existing tabs:",e)}}function xe(){const e=()=>{var t;try{(t=chrome==null?void 0:chrome.runtime)!=null&&t.getPlatformInfo&&chrome.runtime.getPlatformInfo(()=>{chrome.runtime.lastError&&console.warn("Keep alive error:",chrome.runtime.lastError)})}catch(o){console.warn("Keep alive failed:",o)}};e(),setInterval(e,2e4)}W().then(()=>{xe()});chrome.runtime.onStartup.addListener(()=>{console.log("Service worker starting up..."),W()});chrome.runtime.onInstalled.addListener(e=>{console.log("Extension installed/updated:",e.reason),W()});
