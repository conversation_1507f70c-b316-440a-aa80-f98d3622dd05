(function(){function k(e){if(console.log("Validating AvailableOperations query format and payload structure"),!F())return console.warn("⚠️ Extension context invalidated, skipping AvailableOperations parsing"),[];if(!(e!=null&&e.availableOperations)||!Array.isArray(e.availableOperations))throw console.warn("Invalid AvailableOperations GraphQL query payload structure:",e),new Error("Invalid AvailableOperations query format: missing or invalid availableOperations array");console.log("AvailableOperations query format validation successful");const t=[];for(let o=0;o<e.availableOperations.length;o++)try{if(!F()){console.warn("⚠️ Extension context invalidated during offer parsing, stopping");break}const r=e.availableOperations[o];console.log(`🔍 Raw offer ${o} structure:`,JSON.stringify(r,null,2));const n=re(r);t.push(n)}catch(r){const n=r instanceof Error?r.message:String(r);if(n.includes("Extension context invalidated")||n.includes("context invalidated")){console.warn("⚠️ Extension context invalidated during offer parsing");break}console.error(`Failed to parse offer at index ${o}:`,r),console.error("Raw offer data:",JSON.stringify(e.availableOperations[o],null,2));continue}return console.log(`Successfully parsed ${t.length} offers from ${e.availableOperations.length} raw offers using AvailableOperations query format`),t}function F(){var e;try{return!!((e=chrome==null?void 0:chrome.runtime)!=null&&e.id)}catch{return!1}}function re(e){if(!e||typeof e!="object")throw new Error("Invalid offer: not an object");const{id:t,hash:o,operationType:r,status:n,isMine:a,createdAt:s,updatedAt:i,grossAmount:c,netAmount:d,metadata:p,walletCurrency:f,peer:h,rate:v,rateInfo:D,displayRate:N,currency:$,makerPaymentMethod:M,__typename:oe}=e;if(typeof t!="string"||!t.trim())throw new Error("Invalid offer: missing or invalid id");if(typeof o!="string"||!o.trim())throw new Error(`Invalid offer ${t}: missing or invalid hash`);if(r!=="BUY"&&r!=="SELL")throw new Error(`Invalid offer ${t}: operationType must be BUY or SELL, got ${r}`);if(typeof n!="string"||!n.trim())throw new Error(`Invalid offer ${t}: missing or invalid status`);const P=["CREATED","ACCEPTED","REJECTED","COMPLETED","CANCELLED"];if(!P.includes(n))throw new Error(`Invalid offer ${t}: status must be one of ${P.join(", ")}, got ${n}`);if(typeof a!="boolean")throw new Error(`Invalid offer ${t}: isMine must be boolean, got ${typeof a}`);if(typeof s!="string"||!s.trim())throw new Error(`Invalid offer ${t}: missing or invalid createdAt`);if(i!==null&&(typeof i!="string"||!i.trim()))throw new Error(`Invalid offer ${t}: missing or invalid updatedAt`);if(typeof c!="string"||!c.trim())throw new Error(`Invalid offer ${t}: grossAmount must be a non-empty string`);if(typeof d!="string"||!d.trim())throw new Error(`Invalid offer ${t}: netAmount must be a non-empty string`);if(!f||typeof f!="object")throw new Error(`Invalid offer ${t}: missing or invalid walletCurrency`);if(!h||typeof h!="object")throw new Error(`Invalid offer ${t}: missing or invalid peer`);return{id:t,hash:o,operationType:r,status:n,isMine:a,createdAt:s,updatedAt:i,grossAmount:c,netAmount:d,metadata:p||{},walletCurrency:U(f,`${t}.walletCurrency`),peer:ne(h,`${t}.peer`),rate:v?String(v):void 0,rateInfo:D?ae(D,t):void 0,displayRate:N?ie(N,t):void 0,currency:$?U($,`${t}.currency`):void 0,makerPaymentMethod:M?se(M,`${t}.makerPaymentMethod`):void 0,__typename:oe||"Operations__Unknown"}}function q(e,t,o){if(typeof e!="string"||!e.trim())throw new Error(`Invalid offer ${o}: ${t} must be a non-empty string`);return e.trim()}function U(e,t){if(!e||typeof e!="object")throw new Error(`Invalid ${t}: not an object`);const{id:o,symbol:r,precision:n,__typename:a}=e;if(typeof o!="string"||!o.trim())throw new Error(`Invalid ${t}: missing or invalid id`);if(typeof r!="string"||!r.trim())throw new Error(`Invalid ${t}: missing or invalid symbol`);if(typeof n!="number"||n<0||!Number.isInteger(n))throw new Error(`Invalid ${t}: precision must be a non-negative integer, got ${n}`);return a&&a!=="Catalogs__Currency"&&console.warn(`${t}: unexpected __typename '${a}', expected 'Catalogs__Currency'`),{id:o.trim(),symbol:r.trim(),precision:n,__typename:a||"Catalogs__Currency"}}function ne(e,t){if(!e||typeof e!="object")throw new Error(`Invalid ${t}: not an object`);const{id:o,firstName:r,lastName:n,createdAt:a,country:s,countryInfo:i,securityHub:c,numbers:d,preferences:p,__typename:f}=e;if(typeof o!="string"||!o.trim())throw new Error(`Invalid ${t}: missing or invalid id`);if(typeof r!="string")throw new Error(`Invalid ${t}: firstName must be a string, got ${typeof r}`);if(typeof n!="string")throw new Error(`Invalid ${t}: lastName must be a string, got ${typeof n}`);if(typeof a!="string"||!a.trim())throw new Error(`Invalid ${t}: createdAt must be a non-empty string`);if(typeof s!="string")throw new Error(`Invalid ${t}: country must be a string, got ${typeof s}`);if(!i||typeof i!="object")throw new Error(`Invalid ${t}: countryInfo is required and must be an object`);if(!c||typeof c!="object")throw new Error(`Invalid ${t}: securityHub is required and must be an object`);if(typeof c.verificationStatusName!="string"&&console.warn(`${t}: securityHub.verificationStatusName should be a string, got ${typeof c.verificationStatusName}`),!d||typeof d!="object")throw new Error(`Invalid ${t}: numbers is required and must be an object`);if(!p||typeof p!="object")throw new Error(`Invalid ${t}: preferences is required and must be an object`);return f&&!["Auth__OperationUser","Auth__User"].includes(f)&&console.warn(`${t}: unexpected __typename '${f}', expected 'Auth__OperationUser' or 'Auth__User'`),e}function ae(e,t){if(!e||typeof e!="object")throw new Error(`Invalid offer ${t}: rateInfo is not an object`);const{id:o,netAmount:r,grossAmount:n,fundsToReceiveMaker:a,fundsToReceiveTaker:s,fundsToSendMaker:i,fundsToSendTaker:c,__typename:d}=e;return{id:o||t,netAmount:q(r,"rateInfo.netAmount",t),grossAmount:q(n,"rateInfo.grossAmount",t),fundsToReceiveMaker:a!==null?typeof a=="string"?a:String(a):null,fundsToReceiveTaker:s!==null?typeof s=="string"?s:String(s):null,fundsToSendMaker:i!==null?typeof i=="string"?i:String(i):null,fundsToSendTaker:c!==null?typeof c=="string"?c:String(c):null,__typename:d||"Operations__RateInfo"}}function ie(e,t){if(!e||typeof e!="object")throw new Error(`Invalid offer ${t}: displayRate is not an object`);const{direction:o,rate:r,__typename:n}=e,a=["TO_LOCAL_CURRENCY","FROM_LOCAL_CURRENCY","TO_WALLET_CURRENCY"];return a.includes(o)||console.warn(`Invalid direction '${o}', defaulting to 'TO_LOCAL_CURRENCY'`),r!=null&&typeof r!="string"&&typeof r!="number"&&console.warn(`Invalid rate type '${typeof r}', converting to string`),{direction:a.includes(o)?o:"TO_LOCAL_CURRENCY",rate:r?String(r):"0",__typename:n||"Operations__DisplayRate"}}function se(e,t){if(!e||typeof e!="object")throw new Error(`Invalid ${t}: not an object`);const{id:o,categoryId:r,isThirdPartyInstance:n,version:a}=e;if(typeof o!="string"||!o.trim())throw new Error(`Invalid ${t}: missing or invalid id`);if(typeof r!="string"||!r.trim())throw new Error(`Invalid ${t}: missing or invalid categoryId`);if(typeof n!="boolean")throw new Error(`Invalid ${t}: isThirdPartyInstance must be boolean, got ${typeof n}`);if(!a||typeof a!="object")throw new Error(`Invalid ${t}: version is required and must be an object`);return e}function ce(){try{const e=["airtm_token","auth_token","access_token","bearer_token","authToken","accessToken","bearerToken","jwt","JWT","token","authorization","auth","session_token"];for(const r of e){const n=localStorage.getItem(r);if(n&&n.length>10)return console.log(`Bearer token found in localStorage: ${r}`),n}for(const r of e){const n=sessionStorage.getItem(r);if(n&&n.length>10)return console.log(`Bearer token found in sessionStorage: ${r}`),n}const t=document.cookie.split(";");for(const r of t){const[n,a]=r.trim().split("=");if((n.toLowerCase().includes("token")||n.toLowerCase().includes("auth"))&&a&&a.length>10)return console.log(`Bearer token found in cookie: ${n}`),a}const o=document.querySelectorAll("script");for(const r of o){const n=r.textContent||"",a=[/(?:token|bearer|authorization)["']?\s*:\s*["']([^"'\s]{20,})["']/i,/Authorization["']?\s*:\s*["']Bearer\s+([^"'\s]+)["']/i,/["']token["']\s*:\s*["']([^"'\s]{20,})["']/i,/window\.__INITIAL_STATE__.*["']token["']\s*:\s*["']([^"'\s]+)["']/i];for(const s of a){const i=n.match(s);if(i&&i[1])return console.log("Bearer token found in script content"),i[1]}}return typeof window<"u"&&window.__auth_token?(console.log("Bearer token found in window.__auth_token"),window.__auth_token):(console.log("No bearer token found in any location"),null)}catch(e){return console.error("Error extracting bearer token:",e),null}}const _=["AvailableOperations","GetBalance","GetTransactions","CreateOperation","AcceptOperation","OperationDetails","UnmatchOperationMutation"],Q="https://app.airtm.com/graphql",b=new Map,H=2e3;function le(){if(console.log("🔧 Initializing XHR interceptor..."),typeof window>"u"||!window.XMLHttpRequest){console.warn("❌ XHR interceptor: XMLHttpRequest not available");return}const e=window.XMLHttpRequest,t=e.prototype.open,o=e.prototype.send;e.prototype.open=function(r,n,a,s,i){const c=typeof n=="string"?n:n.toString();return console.log("🌐 XHR open called for URL:",c),this._interceptedMethod=r,this._interceptedUrl=c,t.call(this,r,n,a??!0,s,i)},e.prototype.send=function(r){const n=this._interceptedMethod,a=this._interceptedUrl;if(console.log("📤 XHR send called:",n,a),B(a,{method:n,body:r})){console.log("🎯 Intercepting Airtm GraphQL XHR request:",a);const s=this.onreadystatechange;this.onreadystatechange=function(i){if(this.readyState===4&&this.status===200){console.log("📥 XHR GraphQL response received, status:",this.status);try{const c=this.responseText;if(c){console.log("📄 XHR Response preview:",c.substring(0,200)+"...");try{l(r||"")}catch(d){console.error("❌ Error processing XHR GraphQL response:",d)}}}catch(c){console.error("❌ Error parsing XHR response:",c)}}if(s)return s.apply(this,[i])}}else console.log("⏭️ Skipping non-GraphQL XHR request:",a);return o.apply(this,[r])},console.log("✅ XHR interceptor initialized")}function W(){if(console.log("🔧 Initializing all interceptors (fetch, XHR, WebSocket, DOM observer)..."),typeof window>"u"){console.warn("❌ Fetch interceptor: window not available");return}if(console.log("✅ Window is available"),console.log("📋 Monitored operations:",_),console.log("🌐 Target GraphQL endpoint:",Q),window.fetch){const e=window.fetch;window.fetch=we(e),console.log("✅ Fetch interceptor initialized")}else console.warn("⚠️ window.fetch not available");le(),de(),fe(),ue(),ge(),ye(),console.log("🎉 All interceptors and monitoring techniques initialized successfully"),setTimeout(()=>{console.log("📊 Extension Status Check:"),console.log("- Fetch interceptor:",typeof window.fetch=="function"?"✅":"❌"),console.log("- XHR interceptor:",typeof XMLHttpRequest<"u"?"✅":"❌"),console.log("- WebSocket interceptor:",typeof WebSocket<"u"?"✅":"❌"),console.log("- DOM observer:",typeof MutationObserver<"u"?"✅":"❌"),console.log("- Performance API:",typeof PerformanceObserver<"u"?"✅":"❌"),console.log("- Service Worker:","serviceWorker"in navigator?"✅":"❌"),console.log("- Global function:",typeof window.airtmExtensionReceiveData=="function"?"✅":"❌"),console.log("🔍 If you still don't see offers, check the Network tab in DevTools for GraphQL requests")},1e3),console.log("🔍 Ready to intercept GraphQL requests!")}function de(){if(console.log("🔧 Initializing WebSocket interceptor..."),typeof WebSocket<"u"){const e=WebSocket;window.WebSocket=function(t,o){console.log("🔌 WebSocket connection intercepted:",t);const r=new e(t,o),n=r.addEventListener;return r.addEventListener=function(a,s,i){if(a==="message"){const c=d=>{const p=d;console.log("📨 WebSocket message received:",p.data);try{const f=JSON.parse(p.data);f&&(f.data||f.errors)&&(console.log("🎯 Potential GraphQL data via WebSocket:",f),l(p.data))}catch{}if(typeof s=="function")return s.call(r,d);if(s&&typeof s.handleEvent=="function")return s.handleEvent(d)};return n.call(this,a,c,i)}return n.call(this,a,s,i)},r},console.log("✅ WebSocket interceptor initialized")}else console.warn("⚠️ WebSocket not available")}function fe(){console.log("🔧 Initializing DOM observer..."),typeof MutationObserver<"u"?(new MutationObserver(t=>{t.forEach(o=>{if(o.type==="childList"&&o.addedNodes.forEach(r=>{if(r.nodeType===Node.ELEMENT_NODE){const n=r;if(n.textContent&&n.textContent.includes("availableOperations")){console.log("🎯 DOM element with availableOperations detected:",n);try{const i=n.textContent.match(/\{.*availableOperations.*\}/);i&&l(i[0])}catch{console.log("❌ Failed to parse DOM content as JSON")}}if(n.tagName==="SCRIPT"&&n.textContent)try{const i=JSON.parse(n.textContent);i&&i.data&&i.data.availableOperations&&(console.log("🎯 Script tag with availableOperations detected"),l(n.textContent||""))}catch{}n.getAttributeNames().filter(i=>i.startsWith("data-")).forEach(i=>{const c=n.getAttribute(i);c&&(c.includes("availableOperations")||c.includes("graphql"))&&(console.log("📄 Found GraphQL data in data attribute:",i),l(c))}),Object.keys(n).filter(i=>i.startsWith("__react")).forEach(i=>{try{const c=n[i];if(c){const d=JSON.stringify(c);d.includes("availableOperations")&&(console.log("🎯 Found GraphQL data in React component:",i),l(d))}}catch{}})}}),o.type==="attributes"){const n=o.target.getAttribute(o.attributeName||"");n&&n.includes("availableOperations")&&(console.log("📄 Found GraphQL data in modified attribute:",o.attributeName),l(n))}})}).observe(document.body,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["data-state","data-props","data-apollo","data-graphql"]}),console.log("✅ DOM observer initialized")):console.warn("⚠️ MutationObserver not available"),setInterval(()=>{document.querySelectorAll("script").forEach((o,r)=>{o.textContent&&o.textContent.includes("availableOperations")&&!o.scanned&&(console.log(`🔍 Periodic scan found GraphQL data in script ${r}`),l(o.textContent),o.scanned=!0)}),document.querySelectorAll("[data-state], [data-props], [data-apollo], [data-graphql]").forEach((o,r)=>{["data-state","data-props","data-apollo","data-graphql"].forEach(a=>{const s=o.getAttribute(a);s&&s.includes("availableOperations")&&!o[a+"_scanned"]&&(console.log(`🔍 Periodic scan found GraphQL data in ${a} of element ${r}`),l(s),o[a+"_scanned"]=!0)})})},3e3)}function ue(){if(console.log("🔧 Initializing network monitor..."),typeof PerformanceObserver<"u")try{new PerformanceObserver(t=>{t.getEntries().forEach(o=>{(o.name.includes("graphql")||o.name.includes("airtm.com"))&&(console.log("🌐 Network request detected via Performance API:",o.name),o.name.includes("graphql")&&(console.log("🎯 GraphQL request detected, attempting to capture response..."),setTimeout(()=>{try{[window.__APOLLO_CLIENT__,window.__NEXT_DATA__,window.__INITIAL_STATE__,window.store,window.app].forEach((n,a)=>{if(n){console.log(`🔍 Checking cache location ${a+1}:`,n);const s=JSON.stringify(n);s.includes("availableOperations")&&(console.log("🎯 Found availableOperations in cache!"),l(s))}})}catch(r){console.warn("⚠️ Error checking caches:",r)}},100)))})}).observe({entryTypes:["resource"]}),console.log("✅ Network monitor initialized")}catch(e){console.warn("⚠️ PerformanceObserver failed to initialize:",e)}else console.warn("⚠️ PerformanceObserver not available");setInterval(()=>{performance.getEntriesByType("resource").forEach(t=>{t.name.includes("graphql")&&!t.logged&&(console.log("📊 Resource timing detected GraphQL call:",t.name),setTimeout(()=>{document.querySelectorAll("script").forEach(r=>{r.textContent&&r.textContent.includes("availableOperations")&&(console.log("🎯 Found GraphQL data in script tag!"),l(r.textContent))}),Object.keys(window).forEach(r=>{try{const n=window[r];if(n&&typeof n=="object"){const a=JSON.stringify(n);a.includes("availableOperations")&&(console.log("🎯 Found GraphQL data in window."+r),l(a))}}catch{}})},200),t.logged=!0)})},2e3)}function ge(){console.log("🔧 Initializing additional event listeners..."),window.addEventListener("airtm-data-update",e=>{console.log("🎯 Custom airtm-data-update event detected:",e.detail),e.detail&&typeof e.detail=="string"&&l(e.detail)}),window.addEventListener("storage",e=>{if(e.key&&e.key.includes("airtm")&&e.newValue){console.log("💾 Storage event with airtm data detected:",e.key,e.newValue);try{const t=JSON.parse(e.newValue);t&&t.availableOperations&&l(e.newValue)}catch{}}}),window.addEventListener("message",e=>{e.data&&typeof e.data=="object"&&(e.data.type==="airtm-graphql-response"||e.data.data&&e.data.data.availableOperations)&&(console.log("📬 PostMessage with GraphQL data detected:",e.data),l(JSON.stringify(e.data)))}),setInterval(()=>{try{for(let e=0;e<localStorage.length;e++){const t=localStorage.key(e);if(t&&t.includes("airtm")){const o=localStorage.getItem(t);o&&o.includes("availableOperations")&&(console.log("🔍 Found airtm data in localStorage:",t),l(o))}}}catch{}try{for(let e=0;e<sessionStorage.length;e++){const t=sessionStorage.key(e);if(t&&t.includes("airtm")){const o=sessionStorage.getItem(t);o&&o.includes("availableOperations")&&(console.log("🔍 Found airtm data in sessionStorage:",t),l(o))}}}catch{}},5e3),console.log("✅ Additional event listeners initialized")}function pe(){console.log("🔧 Initializing Service Worker listener..."),"serviceWorker"in navigator?(navigator.serviceWorker.addEventListener("message",e=>{e.data&&e.data.type==="network-request"&&(console.log("🔧 Service Worker network request:",e.data),e.data.url&&e.data.url.includes("graphql")&&console.log("🎯 GraphQL request detected via Service Worker"))}),navigator.serviceWorker.register("/service-worker.js").catch(()=>{console.log("ℹ️ Service Worker registration failed (expected if not available)")}),console.log("✅ Service Worker listener initialized")):console.warn("⚠️ Service Worker not available")}function me(){if(console.log("🔧 Initializing DevTools monitor..."),window.chrome&&window.chrome.devtools)try{window.chrome.devtools.network.onRequestFinished.addListener(e=>{e.request.url.includes("graphql")&&(console.log("🔧 DevTools detected GraphQL request:",e),e.getContent(t=>{t&&l(t)}))}),console.log("✅ DevTools monitor initialized")}catch{console.log("ℹ️ DevTools API not accessible (expected in content script)")}}function he(){console.log("🔧 Initializing aggressive polling..."),setInterval(()=>{try{Object.keys(window).forEach(o=>{if(o.toLowerCase().includes("airtm")||o.toLowerCase().includes("graphql")){const r=window[o];if(r&&typeof r=="object"){const n=JSON.stringify(r);n.includes("availableOperations")&&(console.log("🔍 Found GraphQL data in window object:",o),l(n))}}});const t=document.querySelector("[data-reactroot]");if(t&&t._reactInternalFiber&&console.log("🔍 React app detected, checking for data..."),window.__APOLLO_CLIENT__){console.log("🔍 Apollo Client detected");const o=window.__APOLLO_CLIENT__;if(o.cache&&o.cache.data){const r=JSON.stringify(o.cache.data);r.includes("availableOperations")&&(console.log("🎯 Found availableOperations in Apollo cache"),l(r))}}}catch{}},3e3),console.log("✅ Aggressive polling initialized")}function ye(){console.log("🚀 Initializing advanced monitoring techniques..."),pe(),me(),he(),be(),window.airtmExtensionReceiveData=function(e){console.log("📞 Data received via global function:",e),l(typeof e=="string"?e:JSON.stringify(e))},console.log("✅ Advanced monitoring initialized")}function be(){console.log("🔧 Injecting page script for main world access...");const e=document.createElement("script");e.src=chrome.runtime.getURL("inject-script.js"),e.type="text/javascript",window.addEventListener("airtm-graphql-detected",t=>{console.log("📡 Received data from main world:",t.detail),t.detail&&t.detail.responseText&&l(t.detail.responseText)}),(document.head||document.documentElement).appendChild(e),e.remove(),console.log("✅ Page script injected successfully")}function we(e){return async function(t,o){const r=typeof t=="string"?t:t instanceof URL?t.href:t.url;if(console.log("🌐 Fetch interceptor called for URL:",r),B(r,o))try{console.log("🎯 Intercepting Airtm GraphQL request:",r);const n=await e(t,o),a=n.clone();return console.log("📥 GraphQL response received, status:",n.status),Ce(a).catch(s=>{console.error("❌ Error processing GraphQL response:",s)}),n}catch(n){return console.error("❌ Error in fetch interceptor:",n),e(t,o)}else console.log("⏭️ Skipping non-GraphQL request:",r);return e(t,o)}}function B(e,t){if(console.log("🔍 Checking URL for GraphQL interception:",e),!e.includes(Q))return console.log("❌ URL does not match Airtm GraphQL endpoint"),!1;if(console.log("✅ URL matches Airtm GraphQL endpoint"),t!=null&&t.method&&t.method.toUpperCase()!=="POST")return console.log("❌ Request method is not POST:",t.method),!1;if(console.log("✅ Request method is POST or undefined (default)"),t!=null&&t.body){const o=typeof t.body=="string"?t.body:JSON.stringify(t.body);console.log("📝 Request body preview:",o.substring(0,200)+"...");const r=_.some(n=>o.includes(n)?(console.log("✅ Found exact match for operation:",n),!0):o.toLowerCase().includes(n.toLowerCase())?(console.log("✅ Found case-insensitive match for operation:",n),!0):!1);return r||(console.log("❌ No monitored operations found in request body"),console.log("🔍 Monitored operations:",_)),r}return console.log("⚠️ No request body found, defaulting to true for Airtm GraphQL endpoint"),!0}function ve(e){try{const t=e.data.acceptOperation;if(console.log("✅ Processing successful AcceptOperation:",t),t&&t.id){console.log("🎉 Operation successfully accepted:",t.id);const o={operationId:t.id,timestamp:new Date().toISOString(),status:"accepted",data:t};g("OPERATION_ACCEPTED",o),chrome.runtime.sendMessage({type:"SEND_TELEGRAM_OPERATION_UPDATE",operationId:t.id,status:"accepted"})}else console.warn("⚠️ AcceptOperation response missing operation ID")}catch(t){console.error("❌ Error handling AcceptOperation response:",t)}}function Ee(e){try{const t=e.data.unmatchOperation;if(console.log("❌ Processing successful UnmatchOperation:",t),t===!0){console.log("🚫 Operation successfully declined");const o={timestamp:new Date().toISOString(),status:"declined",success:!0};g("OPERATION_DECLINED",o),g("SEND_TELEGRAM_OPERATION_UPDATE",{status:"declined",timestamp:new Date().toISOString()})}else console.warn("⚠️ UnmatchOperation response unexpected result:",t)}catch(t){console.error("❌ Error handling UnmatchOperation response:",t)}}function Oe(e){var t;try{const o=e.errors||[];console.log("❌ Processing UnmatchOperation errors:",o);const r=o.filter(n=>n.path&&n.path.includes("unmatchOperation"));for(const n of r){console.log("🔍 UnmatchOperation error details:",n);const a={timestamp:new Date().toISOString(),status:"decline_failed",error:n.message||"Unknown decline error",errorCode:((t=n.extensions)==null?void 0:t.code)||"UNKNOWN"};g("OPERATION_DECLINE_ERROR",a),g("SEND_TELEGRAM_OPERATION_UPDATE",{status:"decline_error",error:n.message,timestamp:new Date().toISOString()})}}catch(o){console.error("❌ Error handling UnmatchOperation error:",o)}}function Se(e){try{const t=e.errors||[];console.log("❌ Processing AcceptOperation errors:",t);const o=t.filter(r=>r.path&&r.path.includes("acceptOperation"));for(const r of o)if(console.log("🔍 AcceptOperation error details:",r),r.message&&(r.message.toLowerCase().includes("no longer available")||r.message.toLowerCase().includes("not available")||r.message.toLowerCase().includes("already taken")||r.message.toLowerCase().includes("expired"))){console.log("🚫 Operation is no longer available");const a=xe(r);g("OPERATION_NOT_AVAILABLE",{error:r.message,operationId:a,timestamp:new Date().toISOString()}),chrome.runtime.sendMessage({type:"SEND_TELEGRAM_OPERATION_UPDATE",operationId:a||"unknown",status:"Not Available"})}else console.log("⚠️ Other AcceptOperation error:",r.message),g("OPERATION_ACCEPT_ERROR",{error:r.message,timestamp:new Date().toISOString(),errorType:"accept_failed"})}catch(t){console.error("❌ Error handling AcceptOperation error:",t)}}function xe(e){try{if(e.path&&Array.isArray(e.path)){for(const t of e.path)if(typeof t=="string"&&/^\d+$/.test(t))return t}if(e.message){const t=e.message.match(/operation[\s\w]*?(\d+)/i);if(t&&t[1])return t[1]}return null}catch(t){return console.error("Error extracting operation ID from error:",t),null}}function l(e){try{if(!O()){console.warn("⚠️ Extension context invalidated, attempting recovery...");try{const n=ke(e);n.length>0&&(localStorage.setItem("airtm_offers_fallback",JSON.stringify({offers:n,timestamp:Date.now(),source:"context_invalidated_fallback"})),console.log(`💾 Stored ${n.length} offers in localStorage fallback`))}catch(n){console.error("❌ Fallback storage failed:",n)}C();return}console.log("🔄 Processing GraphQL response text..."),console.log("📄 Response text length:",e.length),console.log("📄 Response preview:",e.substring(0,500)+"...");const t=$e(e),o=Date.now();if(b.has(t)){const n=b.get(t);if(o-n<H){console.log("⏭️ Skipping duplicate GraphQL response processing");return}}b.set(t,o),Me();let r;try{r=JSON.parse(e),console.log("✅ JSON parsing successful"),console.log("📊 Parsed data keys:",Object.keys(r))}catch(n){console.error("❌ Failed to parse GraphQL response as JSON:",n);return}r.errors&&Array.isArray(r.errors)&&console.warn("⚠️ GraphQL response contains errors:",r.errors),r.data&&r.data.availableOperations?(console.log("🎯 availableOperations detected, length:",r.data.availableOperations.length),Re(r.data)):console.log("ℹ️ No availableOperations found in response"),r.data&&r.data.getBalance&&(console.log("💰 getBalance operation detected"),g("BALANCE_UPDATE",r.data.getBalance)),r.data&&r.data.createOperation&&(console.log("🆕 createOperation detected"),g("OPERATION_CREATED",r.data.createOperation)),r.data&&r.data.acceptOperation?(console.log("✅ acceptOperation detected"),ve(r)):r.errors&&r.errors.some(n=>n.path&&n.path.includes("acceptOperation"))&&(console.log("❌ acceptOperation error detected"),Se(r)),r.data&&r.data.unmatchOperation!==void 0?(console.log("❌ unmatchOperation detected"),Ee(r)):r.errors&&r.errors.some(n=>n.path&&n.path.includes("unmatchOperation"))&&(console.log("❌ unmatchOperation error detected"),Oe(r)),r.data&&r.data.operation&&(console.log("📋 OperationDetails detected"),Te(r.data.operation))}catch(t){const o=t instanceof Error?t.message:String(t);if(o.includes("Extension context invalidated")||o.includes("context invalidated")){console.warn("⚠️ Extension context invalidated during GraphQL response processing");return}console.error("❌ Error processing GraphQL response text:",t)}}function O(){var e;try{return!!((e=chrome==null?void 0:chrome.runtime)!=null&&e.id)}catch{return!1}}function ke(e){var t,o,r;try{const n=JSON.parse(e);return(t=n==null?void 0:n.data)!=null&&t.availableOperations?k(n):(o=n==null?void 0:n.data)!=null&&o.operations?n.data.operations:(r=n==null?void 0:n.data)!=null&&r.offers?n.data.offers:[]}catch(n){return console.error("❌ Error extracting offers from GraphQL response:",n),[]}}let x=!1,m=0;const S=5,_e=2e3;function C(){x||m>=S||(x=!0,m++,console.log(`🔄 Scheduling context recovery attempt ${m}/${S}`),setTimeout(()=>{Ae(),x=!1},_e))}function Ae(){console.log("🔄 Attempting extension context recovery..."),E(`Attempting recovery... (${m}/${S})`);try{O()?(console.log("✅ Extension context recovered successfully!"),m=0,E("✅ Context recovered!","success"),Ie(),console.log("🔄 Re-initializing fetch interceptor..."),setTimeout(()=>{A()},3e3)):(console.log(`❌ Context recovery attempt ${m} failed`),m<S?C():(console.log("❌ Max recovery attempts reached, giving up"),E("❌ Recovery failed","error"),Le(),setTimeout(()=>{A()},5e3)))}catch(e){console.error("❌ Error during context recovery:",e),E("❌ Recovery error","error")}}function E(e,t="info"){try{A();const o=document.createElement("div");if(o.id="airtm-recovery-status",o.style.cssText=`
      position: fixed;
      top: 10px;
      left: 50%;
      transform: translateX(-50%);
      background: ${t==="success"?"#10b981":t==="error"?"#ef4444":"#3b82f6"};
      color: white;
      padding: 8px 16px;
      border-radius: 20px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 12px;
      font-weight: 600;
      z-index: 10001;
      box-shadow: 0 4px 12px rgba(0,0,0,0.2);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255,255,255,0.2);
      animation: slideDown 0.3s ease-out;
    `,o.textContent=e,!document.getElementById("airtm-recovery-animations")){const r=document.createElement("style");r.id="airtm-recovery-animations",r.textContent=`
        @keyframes slideDown {
          from { transform: translateX(-50%) translateY(-100%); opacity: 0; }
          to { transform: translateX(-50%) translateY(0); opacity: 1; }
        }
      `,document.head.appendChild(r)}document.body.appendChild(o)}catch(o){console.error("❌ Error showing recovery status indicator:",o)}}function A(){try{const e=document.getElementById("airtm-recovery-status");e&&e.remove()}catch(e){console.error("❌ Error hiding recovery status indicator:",e)}}function Ie(){try{const e=localStorage.getItem("airtm_offers_fallback");if(e){const t=JSON.parse(e);console.log(`📤 Sending ${t.offers.length} fallback offers to background script`),chrome.runtime.sendMessage({type:"OFFERS_UPDATE",data:{offers:t.offers,timestamp:t.timestamp,source:"context_recovery_fallback"}}).then(()=>{localStorage.removeItem("airtm_offers_fallback"),console.log("✅ Fallback data sent and cleared")}).catch(o=>{console.error("❌ Failed to send fallback data:",o)})}}catch(e){console.error("❌ Error sending fallback data:",e)}}function Le(){try{document.querySelectorAll(".airtm-context-recovery-notification").forEach(r=>r.remove());const t=document.createElement("div");t.className="airtm-context-recovery-notification",t.style.cssText=`
      position: fixed;
      top: 20px;
      right: 20px;
      background: linear-gradient(135deg, #ff6b6b, #ee5a52);
      color: white;
      padding: 20px;
      border-radius: 12px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      font-weight: 500;
      z-index: 10000;
      box-shadow: 0 8px 32px rgba(0,0,0,0.3);
      max-width: 350px;
      border: 1px solid rgba(255,255,255,0.2);
      backdrop-filter: blur(10px);
    `,t.innerHTML=`
      <div style="display: flex; align-items: center; margin-bottom: 12px;">
        <span style="font-size: 20px; margin-right: 10px;">⚠️</span>
        <strong>Airtm Monitor Pro</strong>
      </div>
      <div style="margin-bottom: 15px; line-height: 1.4;">
        Extension context lost. Some features may not work properly.
      </div>
      <div style="display: flex; gap: 10px;">
        <button id="reload-extension-btn" style="
          background: rgba(255,255,255,0.2);
          border: 1px solid rgba(255,255,255,0.3);
          color: white;
          padding: 8px 16px;
          border-radius: 6px;
          font-size: 12px;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.2s;
        ">Reload Extension</button>
        <button id="refresh-page-btn" style="
          background: rgba(255,255,255,0.2);
          border: 1px solid rgba(255,255,255,0.3);
          color: white;
          padding: 8px 16px;
          border-radius: 6px;
          font-size: 12px;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.2s;
        ">Refresh Page</button>
        <button id="dismiss-btn" style="
          background: transparent;
          border: 1px solid rgba(255,255,255,0.3);
          color: white;
          padding: 8px 16px;
          border-radius: 6px;
          font-size: 12px;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.2s;
        ">Dismiss</button>
      </div>
    `,t.addEventListener("click",r=>{const n=r.target;n.id==="reload-extension-btn"?(window.open("chrome://extensions/","_blank"),t.remove()):n.id==="refresh-page-btn"?window.location.reload():n.id==="dismiss-btn"&&t.remove()}),t.querySelectorAll("button").forEach(r=>{r.addEventListener("mouseenter",()=>{r.style.background="rgba(255,255,255,0.3)"}),r.addEventListener("mouseleave",()=>{r.id==="dismiss-btn"?r.style.background="transparent":r.style.background="rgba(255,255,255,0.2)"})}),setTimeout(()=>{t.parentNode&&t.remove()},3e4),document.body.appendChild(t),console.log("🔔 Context recovery notification shown with actionable buttons")}catch(e){console.error("❌ Error showing context recovery notification:",e)}}async function Ce(e){try{const t=await e.text();l(t)}catch(t){console.error("❌ Error processing GraphQL response:",t)}}async function Te(e){try{console.log("🔧 Handling OperationDetails with status tracking"),console.log("📊 Operation data:",JSON.stringify(e,null,2).substring(0,1e3)+"...");const t={id:e.id,hash:e.hash,status:e.status,operationType:e.operationType,grossAmount:e.grossAmount,netAmount:e.netAmount,airtmFee:e.airtmFee,takerCommission:e.takerCommission,createdAt:e.createdAt,updatedAt:e.updatedAt,previousStatus:e.previousStatus,timeToLive:e.timeToLive,makerPaymentMethod:e.makerPaymentMethod,takerPaymentMethod:e.takerPaymentMethod};console.log(`📈 OperationDetails processed: ${t.id} - Status: ${t.status}`),g("OPERATION_DETAILS_UPDATE",{operation:t,timestamp:new Date().toISOString(),source:"graphql-interception"}),await chrome.storage.local.set({[`operation_${t.id}`]:t,lastOperationUpdate:new Date().toISOString()})}catch(t){console.error("❌ Error handling OperationDetails:",t)}}async function Re(e,t){try{if(!O()){console.warn("⚠️ Extension context invalidated, attempting recovery for AvailableOperations...");try{const n=k(e);n.length>0&&(localStorage.setItem("airtm_offers_fallback",JSON.stringify({offers:n,timestamp:Date.now(),source:"available_operations_context_invalidated"})),console.log(`💾 Stored ${n.length} AvailableOperations offers in localStorage fallback`))}catch(n){console.error("❌ AvailableOperations fallback storage failed:",n)}C();return}console.log("🔧 Handling AvailableOperations query format with comprehensive validation"),console.log("📊 Raw data structure:",JSON.stringify(e,null,2).substring(0,1e3)+"...");const o=k(e);if(console.log(`📈 AvailableOperations GraphQL query processed: ${o.length} offers found`),o.length===0){console.log("📭 No offers found - notifying extension with empty array"),g("OFFERS_UPDATE",{offers:[],timestamp:new Date().toISOString(),source:"graphql-interception"});return}console.log(`✅ Successfully parsed ${o.length} offers from GraphQL response`),console.log("🔍 First offer preview:",JSON.stringify(o[0],null,2).substring(0,500)+"..."),await De(o);const r=ce();r?(console.log("🔑 Bearer token extracted and storing..."),await Ne(r)):console.log("⚠️ No bearer token found"),console.log("📢 Notifying extension components of offers update"),g("OFFERS_UPDATE",{offers:o,timestamp:new Date().toISOString(),source:"graphql-interception"}),console.log("📡 Dispatching custom event for content script listeners"),window.dispatchEvent(new CustomEvent("airtm-offers-detected",{detail:{offers:o,timestamp:new Date().toISOString()}})),console.log("🎉 AvailableOperations handling completed successfully")}catch(o){const r=o instanceof Error?o.message:String(o);if(r.includes("Extension context invalidated")||r.includes("context invalidated")){console.warn("⚠️ Extension context invalidated during AvailableOperations handling");return}console.error("❌ Error handling AvailableOperations:",o),console.error("📊 Error details:",o instanceof Error?o.stack:o),O()&&g("PARSING_ERROR",{error:o instanceof Error?o.message:"Unknown parsing error",timestamp:new Date().toISOString()})}}async function De(e){try{if(typeof chrome<"u"&&chrome.storage&&chrome.runtime&&chrome.runtime.id)try{await chrome.storage.local.set({airtm_offers:e,airtm_offers_timestamp:new Date().toISOString(),airtm_offers_count:e.length}),console.log(`✅ Stored ${e.length} offers in Chrome storage`);return}catch(t){const o=t instanceof Error?t.message:String(t);if(o.includes("Extension context invalidated")||o.includes("context invalidated")||o.includes("receiving end does not exist"))console.warn("⚠️ Extension context invalidated, falling back to localStorage");else throw t}try{localStorage.setItem("airtm_offers",JSON.stringify(e)),localStorage.setItem("airtm_offers_timestamp",new Date().toISOString()),localStorage.setItem("airtm_offers_count",e.length.toString()),console.log(`📦 Stored ${e.length} offers in localStorage (fallback)`)}catch(t){throw console.error("❌ Failed to store in localStorage:",t),new Error("Unable to store offers: Both Chrome storage and localStorage failed")}}catch(t){const o=t instanceof Error?t.message:"Unknown error";console.error("❌ Error storing offers:",o)}}async function Ne(e){try{if(typeof chrome<"u"&&chrome.storage&&chrome.runtime&&chrome.runtime.id)try{await chrome.storage.local.set({airtm_bearer_token:e,airtm_token_timestamp:new Date().toISOString()}),console.log("✅ Bearer token stored successfully in Chrome storage");return}catch(t){const o=t instanceof Error?t.message:String(t);if(o.includes("Extension context invalidated")||o.includes("context invalidated")||o.includes("receiving end does not exist"))console.warn("⚠️ Extension context invalidated, falling back to localStorage for bearer token");else throw t}try{localStorage.setItem("airtm_bearer_token",e),localStorage.setItem("airtm_token_timestamp",new Date().toISOString()),console.log("📦 Bearer token stored in localStorage (fallback)")}catch(t){console.error("❌ Failed to store bearer token in localStorage:",t)}}catch(t){const o=t instanceof Error?t.message:"Unknown error";console.error("❌ Error storing bearer token:",o)}}function g(e,t){try{typeof chrome<"u"&&chrome.runtime&&chrome.runtime.id?chrome.runtime.sendMessage({type:e,data:t,metadata:{timestamp:new Date().toISOString(),source:"fetch-interceptor"}}).catch(o=>{const r=o instanceof Error?o.message:String(o);r.includes("Extension context invalidated")||r.includes("context invalidated")||r.includes("receiving end does not exist")?(console.warn(`⚠️ Extension context invalidated while sending message: ${e}`),console.log("💡 Extension may have been reloaded or disabled. Data will be stored locally.")):console.log(`❌ Failed to send message to extension (${e}):`,r)}):console.log(`📭 Chrome extension APIs not available for message: ${e}`)}catch(o){const r=o instanceof Error?o.message:"Unknown error";console.log(`❌ Could not send message to extension (${e}):`,r)}}function $e(e){var o;try{const r=JSON.parse(e);if((o=r.data)!=null&&o.availableOperations&&Array.isArray(r.data.availableOperations)){const n=r.data.availableOperations,a=n.length,s=n.slice(0,3).map((i,c)=>(i==null?void 0:i.id)||(i==null?void 0:i.hash)||`op${c}`).join("-");return`availableOps-${a}-${s}`}}catch(r){console.debug("Cache key generation fallback due to:",r)}let t=0;for(let r=0;r<e.length;r++){const n=e.charCodeAt(r);t=(t<<5)-t+n,t=t&t}return t.toString()}function Me(){const e=Date.now(),t=[];b.forEach((o,r)=>{e-o>H*2&&t.push(r)}),t.forEach(o=>b.delete(o))}async function T(){try{if(typeof chrome<"u"&&chrome.storage&&chrome.runtime&&chrome.runtime.id)try{return(await chrome.storage.local.get("airtm_offers")).airtm_offers||[]}catch(e){const t=e instanceof Error?e.message:String(e);if(t.includes("Extension context invalidated")||t.includes("context invalidated")||t.includes("receiving end does not exist"))console.warn("⚠️ Extension context invalidated, falling back to localStorage for offers retrieval");else throw e}try{const e=localStorage.getItem("airtm_offers");return e?JSON.parse(e):[]}catch(e){return console.error("❌ Failed to retrieve offers from localStorage:",e),[]}}catch(e){return console.error("❌ Error retrieving stored offers:",e),[]}}function Pe(e){console.log("🔧 Updating duplicate detection config:",e)}const I=/https:\/\/app\.airtm\.com\/peer-transfers\/available/;let y=null;async function G(){try{if(console.log("🚀 Airtm Monitor Pro: Content script initializing..."),console.log("📍 Current URL:",window.location.href),console.log("🎯 Target pattern:",I.toString()),!w()){console.log("⚠️ Extension context invalidated, content script will not initialize");return}if(!I.test(window.location.href)){console.log("❌ Not on target page, content script will not activate"),console.log("💡 Expected URL pattern: https://app.airtm.com/peer-transfers/available");return}console.log("✅ On target page, initializing fetch interceptor..."),console.log("🔧 Window.fetch available:",typeof window.fetch<"u"),W(),Fe(),J(),qe(),console.log("🎉 Airtm Monitor Pro: Content script initialized successfully"),console.log("👂 Now listening for GraphQL requests...")}catch(e){console.error("❌ Error initializing content script:",e),(e instanceof Error?e.message:String(e)).includes("Extension context invalidated")&&console.log("🔄 Extension context was invalidated during initialization")}}function w(){try{return!!(chrome.runtime&&chrome.runtime.id)}catch{return!1}}function Fe(){if(!w()){console.log("⚠️ Extension context invalidated, skipping message listener setup");return}chrome.runtime.onMessage.addListener((e,t,o)=>{var r,n,a,s;if(!w())return console.log("⚠️ Extension context invalidated during message handling"),!1;switch(console.log("Content script received message:",e),e.type){case"ACCEPT_OFFER":return X((r=e.data)==null?void 0:r.offerId).then(i=>o({success:!0,result:i})).catch(i=>o({success:!1,error:i.message})),!0;case"REJECT_OFFER":return V((n=e.data)==null?void 0:n.offerId).then(i=>o({success:!0,result:i})).catch(i=>o({success:!1,error:i.message})),!0;case"GET_OFFERS":return T().then(i=>o({success:!0,offers:i})).catch(i=>o({success:!1,error:i.message})),!0;case"STATUS_UPDATE":Ge(e.data),o({success:!0});break;case"HIGHLIGHT_OFFER":je((a=e.data)==null?void 0:a.offer),o({success:!0});break;case"FOCUS_OFFER":return ze((s=e.data)==null?void 0:s.offerId).then(()=>o({success:!0})).catch(i=>o({success:!1,error:i.message})),!0;case"UPDATE_SETTINGS":He(e.data),o({success:!0});break;case"EXECUTE_COMMAND":return e.command?Ue(e.command).then(()=>o({success:!0})).catch(i=>o({success:!1,error:i.message})):o({success:!1,error:"Command is required"}),!0;default:console.log("Unknown message type:",e.type),o({success:!1,error:"Unknown message type"})}})}function J(){y&&(document.removeEventListener("keydown",y),y=null),y=e=>{if(e.ctrlKey&&e.shiftKey)switch(e.key.toLowerCase()){case"a":e.preventDefault(),Y();break;case"r":e.preventDefault(),K();break;case"d":e.preventDefault(),Z();break;case"c":e.preventDefault(),ee();break}},document.addEventListener("keydown",y)}function qe(){window.addEventListener("airtm-offers-detected",o=>{if(console.log("Offers detected event:",o.detail),w())try{chrome.runtime.sendMessage({type:"OFFERS_UPDATE",data:o.detail,metadata:{timestamp:new Date().toISOString(),source:"content-script"}}).catch(r=>{console.log("⚠️ Failed to send message to background script:",r)})}catch(r){console.log("⚠️ Extension context invalidated, cannot send message:",r)}else console.log("⚠️ Extension context invalidated, skipping message send")});let e=window.location.href;new MutationObserver(()=>{window.location.href!==e&&(e=window.location.href,console.log("Page navigation detected:",e),I.test(e)&&setTimeout(()=>W(),1e3))}).observe(document.body,{childList:!0,subtree:!0})}async function X(e){if(!e)throw new Error("Offer ID is required");console.log("Attempting to accept offer:",e);try{const t=R(e);if(!t)throw new Error(`Offer element not found for ID: ${e}`);const o=t.querySelector('[data-testid="accept-button"], .accept-btn, button[aria-label*="accept" i]');if(!o)throw new Error("Accept button not found");return o.click(),console.log("Offer acceptance initiated:",e),!0}catch(t){throw console.error("Error accepting offer:",t),t}}async function V(e){if(!e)throw new Error("Offer ID is required");console.log("Attempting to reject offer:",e);try{const t=R(e);if(!t)throw new Error(`Offer element not found for ID: ${e}`);const o=t.querySelector('[data-testid="reject-button"], .reject-btn, button[aria-label*="reject" i]');if(!o)throw new Error("Reject button not found");return o.click(),console.log("Offer rejection initiated:",e),!0}catch(t){throw console.error("Error rejecting offer:",t),t}}function R(e){var r;const t=[`[data-offer-id="${e}"]`,`[data-id="${e}"]`,`#offer-${e}`,`.offer-${e}`];for(const n of t){const a=document.querySelector(n);if(a)return a}const o=document.querySelectorAll('[class*="offer"], [data-testid*="offer"]');for(const n of o)if((r=n.textContent)!=null&&r.includes(e))return n;return null}async function Ue(e){try{switch(console.log("Executing command:",e),e){case"accept_offer":await Y();break;case"reject_offer":await K();break;case"open_offer_details":Z();break;case"cycle_offers":ee();break;default:console.warn("Unknown command:",e)}}catch(t){throw console.error("Error executing command:",e,t),t}}async function Y(){try{const e=await T();if(e.length===0){console.log("No offers available for quick accept");return}const t=e[0];await X(t.id)}catch(e){console.error("Error in quick accept:",e)}}async function K(){try{const e=await T();if(e.length===0){console.log("No offers available for quick reject");return}const t=e[0];await V(t.id)}catch(e){console.error("Error in quick reject:",e)}}function Z(){console.log("Show details shortcut triggered"),chrome.runtime.sendMessage({type:"SHOW_DETAILS",metadata:{timestamp:new Date().toISOString(),source:"keyboard-shortcut"}})}function ee(){console.log("Cycle offers shortcut triggered"),chrome.runtime.sendMessage({type:"CYCLE_OFFERS",metadata:{timestamp:new Date().toISOString(),source:"keyboard-shortcut"}})}function Ge(e){console.log("Status update received:",e),e.monitoring?console.log("Monitoring is active"):console.log("Monitoring is inactive")}function je(e){e&&(console.log("Highlighting offer:",e.id),te(),Qe(e),Be())}async function ze(e){if(!e)throw new Error("Offer ID is required");console.log("Focusing on offer:",e);try{te();const t=R(e);if(t){t.classList.add("airtm-monitor-highlight","airtm-monitor-focus"),t.scrollIntoView({behavior:"smooth",block:"center",inline:"center"});const o=document.createElement("style");o.id="airtm-focus-styles",o.textContent=`
        .airtm-monitor-focus {
          animation: airtm-focus-pulse 3s infinite !important;
          border: 4px solid #ef4444 !important;
          box-shadow: 0 0 30px rgba(239, 68, 68, 0.8) !important;
        }
        
        .airtm-monitor-focus::before {
          content: '🎯 DETECTED OFFER!' !important;
          background: linear-gradient(135deg, #ef4444, #dc2626) !important;
        }
        
        @keyframes airtm-focus-pulse {
          0% { 
            box-shadow: 0 0 30px rgba(239, 68, 68, 0.8);
            transform: scale(1);
          }
          50% { 
            box-shadow: 0 0 50px rgba(239, 68, 68, 1);
            transform: scale(1.02);
          }
          100% { 
            box-shadow: 0 0 30px rgba(239, 68, 68, 0.8);
            transform: scale(1);
          }
        }
      `;const r=document.getElementById("airtm-focus-styles");r&&r.remove(),document.head.appendChild(o),setTimeout(()=>{t.classList.remove("airtm-monitor-focus");const n=document.getElementById("airtm-focus-styles");n&&n.remove()},15e3),console.log("Successfully focused on offer:",e)}else{console.log("Specific offer not found, highlighting first available offer");const o=document.querySelector('[data-testid*="offer"], [class*="offer"], .MuiCard-root');o&&(o.classList.add("airtm-monitor-highlight"),o.scrollIntoView({behavior:"smooth",block:"center"}),setTimeout(()=>{o.classList.remove("airtm-monitor-highlight")},1e4))}}catch(t){throw console.error("Error focusing on offer:",t),t}}function te(){if(document.getElementById("airtm-monitor-styles"))return;const e=document.createElement("style");e.id="airtm-monitor-styles",e.textContent=`
    .airtm-monitor-highlight {
      animation: airtm-pulse 2s infinite;
      border: 3px solid #10b981 !important;
      border-radius: 8px !important;
      box-shadow: 0 0 20px rgba(16, 185, 129, 0.5) !important;
      background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(34, 197, 94, 0.1)) !important;
      position: relative !important;
    }
    
    .airtm-monitor-highlight::before {
      content: '🔥 NEW OFFER!';
      position: absolute;
      top: -10px;
      right: -10px;
      background: linear-gradient(135deg, #10b981, #22c55e);
      color: white;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: bold;
      z-index: 1000;
      animation: airtm-bounce 1s infinite;
    }
    
    @keyframes airtm-pulse {
      0% { box-shadow: 0 0 20px rgba(16, 185, 129, 0.5); }
      50% { box-shadow: 0 0 30px rgba(16, 185, 129, 0.8); }
      100% { box-shadow: 0 0 20px rgba(16, 185, 129, 0.5); }
    }
    
    @keyframes airtm-bounce {
      0%, 100% { transform: translateY(0); }
      50% { transform: translateY(-5px); }
    }
    
    .airtm-floating-notification {
      position: fixed;
      top: 20px;
      right: 20px;
      background: linear-gradient(135deg, #10b981, #22c55e);
      color: white;
      padding: 16px 20px;
      border-radius: 12px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
      z-index: 10000;
      animation: airtm-slide-in 0.5s ease-out;
      max-width: 300px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
    
    .airtm-floating-notification h3 {
      margin: 0 0 8px 0;
      font-size: 16px;
      font-weight: bold;
    }
    
    .airtm-floating-notification p {
      margin: 0;
      font-size: 14px;
      opacity: 0.9;
    }
    
    @keyframes airtm-slide-in {
      from {
        transform: translateX(100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }
  `,document.head.appendChild(e)}function Qe(e){console.log("Attempting to highlight offer:",e),setTimeout(()=>{let t=!1;const o=['[data-testid*="offer"]','[class*="offer"]','[class*="card"]',".MuiCard-root",".MuiPaper-root",'[data-cy*="offer"]',".offer-card",".peer-transfer-card"];for(const r of o){if(t)break;const n=document.querySelectorAll(r);console.log(`Found ${n.length} elements with selector: ${r}`),n.forEach(a=>{var p,f;if(t)return;const s=a.textContent||"",i=e.grossAmount||e.amount,c=((p=e.currency)==null?void 0:p.symbol)||((f=e.walletCurrency)==null?void 0:f.symbol);let d=!1;if(i){const h=i.toString(),v=parseFloat(h).toLocaleString();(s.includes(h)||s.includes(v))&&(d=!0)}if(!d&&c&&s.includes(c)&&(d=!0),d){a.classList.add("airtm-monitor-highlight"),a.scrollIntoView({behavior:"smooth",block:"center"}),setTimeout(()=>{a.classList.remove("airtm-monitor-highlight")},1e4),console.log("✅ Highlighted offer element:",a),t=!0;return}})}if(!t){console.log("No specific match found, highlighting most recent offer");const r=document.querySelectorAll('[data-testid*="offer"], [class*="offer"], .MuiCard-root');if(r.length>0){const n=r[0];n.classList.add("airtm-monitor-highlight"),n.scrollIntoView({behavior:"smooth",block:"center"}),setTimeout(()=>{n.classList.remove("airtm-monitor-highlight")},1e4),console.log("✅ Highlighted first available offer element:",n),t=!0}}t||console.warn("⚠️ Could not find any offer elements to highlight")},500)}function He(e){console.log("📝 Received settings update:",e),e.duplicateDetection&&Pe(e.duplicateDetection),e.hotkeys&&(console.log("🔄 Hotkeys updated, re-setting up keyboard shortcuts"),J())}let u=null,j=0;const We=0;function Be(){const e=Date.now();if(u){try{u.pause(),u.currentTime=0,console.log("🔇 Previous sound interrupted for new offer")}catch(t){console.log("Error stopping previous sound:",t)}u=null}if(e-j<We){console.log("🔇 Sound skipped - too rapid succession (debounced)");return}j=e;try{u=new Audio(chrome.runtime.getURL("sounds/beep.mp3")),u.volume=.5,u.onended=()=>{u=null,console.log("🔊 Sound playback completed")},u.onerror=()=>{u=null,console.log("🔇 Sound playback error")},u.play().then(()=>{console.log("🔊 New notification sound started")}).catch(t=>{console.log("Could not play sound:",t),u=null})}catch(t){console.log("Sound not available:",t),u=null}}document.readyState==="loading"?document.addEventListener("DOMContentLoaded",G):G();let L=null,z=!0;function Je(){L=setInterval(()=>{const e=w();if(e!==z){if(e){console.log("✅ Extension context restored during health check");try{const t=localStorage.getItem("airtm_offers_fallback");if(t){console.log("📤 Sending fallback data after context restoration...");const o=JSON.parse(t);chrome.runtime.sendMessage({type:"OFFERS_UPDATE",data:{offers:o.offers,timestamp:o.timestamp,source:"health_check_recovery"}}).then(()=>{localStorage.removeItem("airtm_offers_fallback"),console.log("✅ Fallback data sent and cleared during health check")}).catch(r=>{console.error("❌ Failed to send fallback data during health check:",r)})}}catch(t){console.error("❌ Error processing fallback data during health check:",t)}}else console.warn("⚠️ Extension context lost during health check");z=e}},5e3)}Je();window.addEventListener("beforeunload",()=>{L&&clearInterval(L)});
})()
