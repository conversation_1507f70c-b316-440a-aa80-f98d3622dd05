<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#764ba2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f093fb;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#f8fafc;stop-opacity:0.7" />
    </linearGradient>
    <linearGradient id="glowGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fbbf24;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#f59e0b;stop-opacity:0.4" />
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background with modern gradient -->
  <rect width="128" height="128" rx="28" fill="url(#bgGradient)"/>
  
  <!-- Outer glow ring -->
  <circle cx="64" cy="64" r="45" fill="none" stroke="url(#glowGradient)" stroke-width="2" opacity="0.6" filter="url(#glow)">
    <animate attributeName="r" values="45;50;45" dur="4s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.6;0.3;0.6" dur="4s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Central geometric design -->
  <g transform="translate(64, 64)">
    <!-- Main hexagon -->
    <polygon points="-20,0 -10,-17.32 10,-17.32 20,0 10,17.32 -10,17.32" fill="url(#accentGradient)" opacity="0.9">
      <animateTransform attributeName="transform" attributeType="XML" type="rotate" from="0" to="360" dur="20s" repeatCount="indefinite"/>
    </polygon>
    
    <!-- Inner rotating triangles -->
    <polygon points="0,-12 -10.39,6 10.39,6" fill="url(#glowGradient)" opacity="0.8">
      <animateTransform attributeName="transform" attributeType="XML" type="rotate" from="0" to="-360" dur="8s" repeatCount="indefinite"/>
    </polygon>
    
    <!-- Central core -->
    <circle cx="0" cy="0" r="6" fill="url(#accentGradient)" filter="url(#glow)"/>
  </g>
  
  <!-- Floating geometric elements -->
  <g opacity="0.7">
    <!-- Top left -->
    <circle cx="25" cy="25" r="3" fill="url(#accentGradient)">
      <animate attributeName="cy" values="25;20;25" dur="3s" repeatCount="indefinite"/>
    </circle>
    
    <!-- Top right -->
    <rect x="99" y="22" width="6" height="6" rx="1" fill="url(#accentGradient)">
      <animate attributeName="y" values="22;17;22" dur="2.5s" repeatCount="indefinite"/>
    </rect>
    
    <!-- Bottom left -->
    <polygon points="25,103 22,108 28,108" fill="url(#accentGradient)">
      <animate attributeName="opacity" values="0.7;0.3;0.7" dur="2s" repeatCount="indefinite"/>
    </polygon>
    
    <!-- Bottom right -->
    <circle cx="103" cy="103" r="3" fill="url(#glowGradient)">
      <animate attributeName="r" values="3;5;3" dur="3.5s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Connecting lines -->
  <g stroke="url(#accentGradient)" stroke-width="1" opacity="0.4" fill="none">
    <path d="M 35 35 Q 64 50 93 35">
      <animate attributeName="opacity" values="0.4;0.1;0.4" dur="4s" repeatCount="indefinite"/>
    </path>
    <path d="M 35 93 Q 64 78 93 93">
      <animate attributeName="opacity" values="0.4;0.1;0.4" dur="3s" repeatCount="indefinite"/>
    </path>
  </g>
</svg>
