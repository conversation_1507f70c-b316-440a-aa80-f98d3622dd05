import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'

// Development configuration for previewing popup
export default defineConfig({
  plugins: [react()],
  root: 'src',
  build: {
    outDir: '../dev-dist',
    rollupOptions: {
      input: {
        popup: resolve(__dirname, 'src/popup/index.html'),
        'dev-popup': resolve(__dirname, 'src/dev-popup.html')
      }
    }
  },
  server: {
    port: 3000,
    open: '/dev-popup.html'
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  }
})
