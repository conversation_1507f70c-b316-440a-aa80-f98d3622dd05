import{r as D,j as e,c as ee}from"./globals-457874bd.js";import{i as R,s as P}from"./extension-context-5094bf00.js";const u=({d:a,className:r="w-4 h-4"})=>e.jsx("svg",{className:r,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:a})}),p={refresh:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15",settings:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z M15 12a3 3 0 11-6 0 3 3 0 016 0z",close:"M6 18L18 6M6 6l12 12",alert:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z",play:"M14.828 14.828a4 4 0 01-5.656 0M9 10h1.586a1 1 0 01.707.293l2.414 2.414a1 1 0 00.707.293H15",pause:"M10 9v6m4-6v6",check:"M5 13l4 4L19 7",search:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z",withdrawal:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z",crypto:"M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"};function Z(a){var T,E,A,L,M,_,z,W,B,G,H,Y,J,K,V,q,Q,X;const r=a.walletCurrency,c=a.currency,n=a.makerPaymentMethod||a.takerPaymentMethod,d=((T=n==null?void 0:n.categoryId)==null?void 0:T.toLowerCase().includes("usdc"))||((L=(A=(E=n==null?void 0:n.version)==null?void 0:E.category)==null?void 0:A.translationTag)==null?void 0:L.toLowerCase().includes("usdc")),h=((M=a.metadata)==null?void 0:M.walletCurrencyPrecision)||(r==null?void 0:r.precision),m=((_=a.metadata)==null?void 0:_.localCurrencyPrecision)||(c==null?void 0:c.precision),N=h===6||m===6,U=((z=a.metadata)==null?void 0:z.isForThirdPartyPaymentMethod)===!1,j=((W=n==null?void 0:n.categoryId)==null?void 0:W.toLowerCase())||"",v=((H=(G=(B=n==null?void 0:n.version)==null?void 0:B.category)==null?void 0:G.translationTag)==null?void 0:H.toLowerCase())||"",w=j.includes("gift-card")||v.includes("gift_card"),F=j.includes("bank")||v.includes("bank"),O=j.includes("card")||v.includes("card"),k=j.includes("transfer")||v.includes("transfer"),y=w||F||O||k,s=se(n),t=((K=(J=(Y=n==null?void 0:n.version)==null?void 0:Y.image)==null?void 0:J.urls)==null?void 0:K.logo)||((Q=(q=(V=n==null?void 0:n.version)==null?void 0:V.image)==null?void 0:q.urls)==null?void 0:Q.medium),o=d||N||U;let l=!1,i="",x=(r==null?void 0:r.symbol)||"$",f=a.grossAmount||"0",C="",S="",b="";if(a.operationType==="BUY"&&y){l=!0,i="withdrawal";const g=a.rateInfo;g!=null&&g.fundsToSendTaker&&(g!=null&&g.fundsToReceiveTaker)?(f=g.fundsToSendTaker,C=g.fundsToReceiveTaker,S=(c==null?void 0:c.symbol)||"$",b=parseFloat(((X=a.displayRate)==null?void 0:X.rate)||"1").toFixed(4)):f=a.grossAmount||"0",x=(r==null?void 0:r.symbol)||"$"}else o&&!y&&(a.operationType==="SELL"&&h===6?(l=!0,i="withdrawal",x="USDC",f=a.grossAmount||"0"):a.operationType==="BUY"&&m===6?(i="deposit",x="USDC"):(i="exchange",h===6&&(x="USDC")));return{amount:f,currency:x,originalAmount:C,originalCurrency:S,isUSDC:o,isWithdrawal:l,operationType:i,conversionNote:w?"Gift Card Purchase":y?"External Service":void 0,serviceName:s,serviceLogo:t,exchangeRate:b}}function se(a){var n,d;if(!a)return"Unknown";const r=a.categoryId||"",c=((d=(n=a.version)==null?void 0:n.category)==null?void 0:d.translationTag)||"";if(r){const h=r.split(":");if(h.length>2){const m=h[h.length-1].replace(/[-_]/g," ").replace(/\b\w/g,N=>N.toUpperCase());return m.toLowerCase()==="ebay"?"eBay":m.toLowerCase()==="paypal"?"PayPal":m.toLowerCase()==="amazon"?"Amazon":m}}return c&&c.replace("CATEGORY_TREE:AIRTM_","").replace("GIFT_CARD_","").replace("E_TRANSFER_","").replace("BANK_","").replace(/_/g," ").toLowerCase().replace(/\b\w/g,m=>m.toUpperCase())||"Unknown"}function te(){var h,m,N,U,j,v,w,F,O,k,y;const[a,r]=D.useState({offers:[],settings:null,stats:null,selectedOffer:null,isLoading:!0,error:null,isConnected:!1}),c=D.useCallback(async()=>{r(s=>({...s,isLoading:!0,error:null}));try{if(!R())throw new Error("Extension context not available");const s=await P({type:"GET_POPUP_DATA"});if(s!=null&&s.success&&s.data)r(t=>({...t,offers:s.data.offers||[],settings:s.data.settings||null,stats:s.data.stats||{totalOffers:0,newOffers:0,averageRate:0},isConnected:!0,isLoading:!1,error:null}));else throw new Error((s==null?void 0:s.error)||"Invalid response from background script")}catch(s){console.error("Popup data loading error:",s),r(t=>({...t,isLoading:!1,isConnected:!1,error:s instanceof Error?s.message:"Failed to load extension data"}))}},[]),n=D.useCallback(()=>{var t,o;const s=l=>{l.type==="OFFERS_UPDATED"?r(i=>({...i,offers:l.offers||[]})):l.type==="SETTINGS_UPDATED"?r(i=>({...i,settings:l.settings})):l.type==="STATS_UPDATED"&&r(i=>({...i,stats:l.stats}))};return R()?((o=(t=chrome.runtime)==null?void 0:t.onMessage)==null||o.addListener(s),()=>{var l,i;return(i=(l=chrome.runtime)==null?void 0:l.onMessage)==null?void 0:i.removeListener(s)}):()=>{}},[]);D.useEffect(()=>(c(),n()),[]);const d=D.useMemo(()=>({refresh:()=>c(),openSettings:()=>{try{if(!R())throw new Error("Extension context not available");chrome.runtime.openOptionsPage()}catch(s){console.error("Failed to open settings:",s),r(t=>({...t,error:"Failed to open settings page"}))}},selectOffer:s=>{r(t=>({...t,selectedOffer:s}))},updateSettings:async s=>{try{const t=await P({type:"SETTINGS_UPDATE",data:s});if(t!=null&&t.success)r(o=>({...o,settings:s}));else throw new Error((t==null?void 0:t.error)||"Failed to update settings")}catch(t){console.error("Settings update error:",t),r(o=>({...o,error:t instanceof Error?t.message:"Failed to update settings"}))}},acceptOffer:async s=>{try{const t=await P({type:"ACCEPT_OFFER",offerId:s.id||s.hash});if(!(t!=null&&t.success))throw new Error((t==null?void 0:t.error)||"Failed to accept offer")}catch(t){console.error("Accept offer error:",t),r(o=>({...o,error:t instanceof Error?t.message:"Failed to accept offer"}))}},rejectOffer:async s=>{try{const t=await P({type:"REJECT_OFFER",offerId:s.id||s.hash});if(!(t!=null&&t.success))throw new Error((t==null?void 0:t.error)||"Failed to reject offer")}catch(t){console.error("Reject offer error:",t),r(o=>({...o,error:t instanceof Error?t.message:"Failed to reject offer"}))}},dismissError:()=>{r(s=>({...s,error:null}))}}),[c]);return a.isLoading?e.jsx("div",{className:"modern-popup",children:e.jsxs("div",{className:"loading-state",children:[e.jsxs("div",{className:"loading-logo",children:[e.jsx("div",{className:"logo-circle",children:"A"}),e.jsx("div",{className:"loading-pulse"})]}),e.jsxs("div",{className:"loading-content",children:[e.jsx("h2",{className:"loading-title",children:"Airtm Monitor Pro"}),e.jsx("p",{className:"loading-text",children:"Connecting to extension..."}),e.jsx("div",{className:"loading-progress",children:e.jsx("div",{className:"progress-bar"})})]})]})}):e.jsxs("div",{className:"modern-popup",children:[e.jsxs("header",{className:"popup-header",children:[e.jsxs("div",{className:"header-brand",children:[e.jsxs("div",{className:"brand-logo",children:[e.jsx("div",{className:"logo-circle",children:"A"}),e.jsx("div",{className:`status-dot ${(h=a.settings)!=null&&h.monitoring?"active":"inactive"}`})]}),e.jsxs("div",{className:"brand-text",children:[e.jsx("h1",{className:"brand-title",children:"Airtm Monitor Pro"}),e.jsxs("p",{className:"brand-subtitle",children:[a.isConnected?"Connected":"Disconnected"," •",a.offers.length," offers"]})]})]}),e.jsxs("div",{className:"header-actions",children:[e.jsx("button",{onClick:d.refresh,className:"action-button secondary",title:"Refresh data",children:e.jsx(u,{d:p.refresh})}),e.jsx("button",{onClick:d.openSettings,className:"action-button primary",title:"Open settings",children:e.jsx(u,{d:p.settings})})]})]}),a.error&&e.jsxs("div",{className:"error-banner",children:[e.jsxs("div",{className:"error-content",children:[e.jsx(u,{d:p.alert,className:"error-icon"}),e.jsx("span",{className:"error-message",children:a.error})]}),e.jsx("button",{onClick:d.dismissError,className:"error-dismiss",title:"Dismiss error",children:e.jsx(u,{d:p.close})})]}),e.jsx("section",{className:"stats-section",children:e.jsxs("div",{className:"stats-grid",children:[e.jsxs("div",{className:"stat-card",children:[e.jsx("div",{className:"stat-value",children:((m=a.stats)==null?void 0:m.totalOffers)||0}),e.jsx("div",{className:"stat-label",children:"Total Offers"})]}),e.jsxs("div",{className:"stat-card",children:[e.jsx("div",{className:"stat-value",children:((N=a.stats)==null?void 0:N.newOffers)||0}),e.jsx("div",{className:"stat-label",children:"New Today"})]}),e.jsxs("div",{className:"stat-card",children:[e.jsx("div",{className:"stat-value",children:((U=a.stats)==null?void 0:U.acceptedOffers)||0}),e.jsx("div",{className:"stat-label",children:"Accepted"})]}),e.jsxs("div",{className:"stat-card",children:[e.jsx("div",{className:"stat-value",children:((j=a.stats)==null?void 0:j.rejectedOffers)||0}),e.jsx("div",{className:"stat-label",children:"Rejected"})]})]})}),e.jsxs("main",{className:"main-content",children:[e.jsxs("div",{className:"content-header",children:[e.jsx("h3",{className:"content-title",children:"Available Offers"}),e.jsx("div",{className:"content-meta",children:e.jsxs("div",{className:"live-status",children:[e.jsx("div",{className:"pulse-indicator"}),e.jsx("span",{children:"Live"})]})})]}),e.jsx("div",{className:"offers-container",children:a.offers.length===0?e.jsxs("div",{className:"empty-state",children:[e.jsx(u,{d:p.search,className:"empty-icon"}),e.jsx("h4",{className:"empty-title",children:"No offers available"}),e.jsx("p",{className:"empty-text",children:(v=a.settings)!=null&&v.monitoring?"Monitoring is active. New offers will appear here.":"Start monitoring to see offers."})]}):e.jsx("div",{className:"offers-list",children:a.offers.map(s=>{var o,l,i,x,f,C,S,b,T,E,A;const t=Z(s);return e.jsx("div",{className:`offer-item ${(((o=a.selectedOffer)==null?void 0:o.id)||((l=a.selectedOffer)==null?void 0:l.hash))===(s.id||s.hash)?"selected":""} ${t.isWithdrawal?"withdrawal":""} ${t.isUSDC?"usdc-operation":""}`,onClick:()=>d.selectOffer(s),children:t.isWithdrawal?e.jsxs("div",{className:"withdrawal-offer",children:[e.jsx("div",{className:"withdrawal-header",children:e.jsx("span",{className:"withdrawal-title",children:"💸 Withdraw"})}),e.jsxs("div",{className:"service-info",children:[t.serviceLogo&&e.jsx("img",{src:t.serviceLogo,alt:t.serviceName,className:"service-logo",onError:L=>{L.currentTarget.style.display="none"}}),e.jsx("span",{className:"service-name",children:t.serviceName})]}),e.jsxs("div",{className:"withdrawal-amounts",children:[e.jsxs("div",{className:"amount-debit",children:["- $",parseFloat(t.amount).toFixed(t.isUSDC?6:2)," ",t.currency]}),t.originalAmount&&e.jsxs("div",{className:"amount-credit",children:["+ $",parseFloat(t.originalAmount).toFixed(2)," ",t.originalCurrency]}),t.exchangeRate&&e.jsxs("div",{className:"exchange-rate",children:["$1 ",t.currency," = $",t.exchangeRate," ",t.originalCurrency]})]}),e.jsxs("div",{className:"peer-section",children:[((f=(x=(i=s.peer)==null?void 0:i.preferences)==null?void 0:x.profile)==null?void 0:f.avatar)&&e.jsx("img",{src:s.peer.preferences.profile.avatar,alt:"Peer avatar",className:"peer-avatar"}),e.jsxs("div",{className:"peer-details",children:[e.jsx("div",{className:"peer-name",children:s.peer?`${s.peer.firstName} ${s.peer.lastName}`:"Unknown"}),e.jsx("div",{className:"peer-date",children:new Date(s.createdAt).toLocaleDateString("en-US",{month:"short",year:"numeric"})}),e.jsx("div",{className:"peer-location",children:((C=s.peer)==null?void 0:C.country)&&e.jsxs(e.Fragment,{children:[((T=(b=(S=s.peer.countryInfo)==null?void 0:S.image)==null?void 0:b.urls)==null?void 0:T.avatar)&&e.jsx("img",{src:s.peer.countryInfo.image.urls.avatar,alt:s.peer.country,className:"country-flag"}),s.peer.country]})}),((E=s.peer)==null?void 0:E.numbers)&&e.jsxs("div",{className:"peer-stats",children:[s.peer.numbers.completedOperations," txns • ",s.peer.numbers.score,"⭐"]}),e.jsx("div",{className:"offer-timestamp",children:new Date(s.createdAt).toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric",hour:"numeric",minute:"2-digit"})})]})]})]}):e.jsxs("div",{className:"regular-offer",children:[e.jsxs("div",{className:"offer-header",children:[e.jsxs("div",{className:"offer-currency",children:[t.currency,t.isUSDC&&e.jsx("span",{className:"crypto-badge",children:e.jsx(u,{d:p.crypto,className:"w-3 h-3"})})]}),e.jsx("div",{className:"offer-type",children:s.operationType})]}),e.jsxs("div",{className:"offer-details",children:[e.jsxs("div",{className:"offer-amount",children:[t.currency==="USDC"?"":"$",parseFloat(t.amount).toFixed(t.isUSDC?6:2),t.currency==="USDC"?" USDC":""]}),e.jsxs("div",{className:"offer-rate",children:["Rate: ",s.rate||((A=s.displayRate)==null?void 0:A.rate)||"N/A"]})]}),e.jsx("div",{className:"offer-peer",children:s.peer?`${s.peer.firstName} ${s.peer.lastName}`:"Unknown"})]})},s.id||s.hash)})})})]}),e.jsxs("footer",{className:"control-panel",children:[a.selectedOffer&&e.jsxs("div",{className:"selected-offer",children:[e.jsxs("div",{className:"selected-info",children:[e.jsx("span",{className:"selected-label",children:"Selected:"}),e.jsx("span",{className:"selected-details",children:(()=>{const s=Z(a.selectedOffer);return s.isWithdrawal?e.jsxs(e.Fragment,{children:["💸 Withdraw: -",s.currency==="USDC"?"":"$",parseFloat(s.amount).toFixed(s.isUSDC?6:2),s.currency==="USDC"?" USDC":" "+s.currency,s.originalAmount&&e.jsxs(e.Fragment,{children:[" → +$",parseFloat(s.originalAmount).toFixed(2)," ",s.originalCurrency]}),s.serviceName&&e.jsxs("span",{className:"text-blue-600 ml-1",children:["(",s.serviceName,")"]})]}):e.jsxs(e.Fragment,{children:[s.currency," •",s.currency==="USDC"?"":"$",parseFloat(s.amount).toFixed(s.isUSDC?6:2),s.currency==="USDC"?" USDC":"",s.isUSDC&&e.jsx("span",{className:"text-blue-600 ml-1",children:"(USDC)"})]})})()})]}),e.jsxs("div",{className:"selected-actions",children:[e.jsxs("button",{onClick:()=>d.acceptOffer(a.selectedOffer),className:"action-button success",title:"Accept offer",children:[e.jsx(u,{d:p.check}),"Accept"]}),e.jsxs("button",{onClick:()=>d.rejectOffer(a.selectedOffer),className:"action-button danger",title:"Reject offer",children:[e.jsx(u,{d:p.close}),"Reject"]})]})]}),e.jsxs("div",{className:"main-controls",children:[e.jsxs("button",{onClick:()=>{var s;return d.updateSettings({...a.settings,monitoring:!((s=a.settings)!=null&&s.monitoring)})},className:`control-button ${(w=a.settings)!=null&&w.monitoring?"active":"inactive"}`,title:(F=a.settings)!=null&&F.monitoring?"Stop monitoring":"Start monitoring",children:[e.jsx(u,{d:(O=a.settings)!=null&&O.monitoring?p.pause:p.play}),e.jsx("span",{children:(k=a.settings)!=null&&k.monitoring?"Stop":"Start"})]}),e.jsxs("button",{onClick:()=>{var s;return d.updateSettings({...a.settings,autoAccept:!((s=a.settings)!=null&&s.autoAccept)})},className:`control-button ${(y=a.settings)!=null&&y.autoAccept?"active":"inactive"}`,title:"Toggle auto-accept",children:[e.jsx(u,{d:p.check}),e.jsx("span",{children:"Auto Accept"})]})]})]})]})}function $(){const a=document.documentElement,r=document.body,c=`
    width: 600px !important;
    height: 850px !important;
    min-width: 600px !important;
    min-height: 850px !important;
    max-width: 600px !important;
    max-height: 850px !important;
    margin: 0 !important;
    padding: 0 !important;
    overflow: hidden !important;
  `;a.style.cssText=c,r.style.cssText=c,r.className="popup",console.log("Popup dimensions enforced:",{htmlSize:`${a.offsetWidth}x${a.offsetHeight}`,bodySize:`${r.offsetWidth}x${r.offsetHeight}`,windowSize:`${window.innerWidth}x${window.innerHeight}`})}$();document.addEventListener("DOMContentLoaded",$);window.addEventListener("load",$);const I=document.getElementById("root");if(!I)throw new Error("Root element not found");I.style.cssText=`
  width: 600px !important;
  height: 850px !important;
  min-width: 600px !important;
  min-height: 850px !important;
  max-width: 600px !important;
  max-height: 850px !important;
  display: flex !important;
  flex-direction: column !important;
`;const ae=ee(I);ae.render(e.jsx(te,{}));
