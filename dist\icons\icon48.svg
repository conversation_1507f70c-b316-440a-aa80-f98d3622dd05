<svg width="48" height="48" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#764ba2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f093fb;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#f8fafc;stop-opacity:0.7" />
    </linearGradient>
    <linearGradient id="glowGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fbbf24;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#f59e0b;stop-opacity:0.4" />
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="1" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background with modern gradient -->
  <rect width="48" height="48" rx="10" fill="url(#bgGradient)"/>
  
  <!-- Outer glow ring -->
  <circle cx="24" cy="24" r="17" fill="none" stroke="url(#glowGradient)" stroke-width="1" opacity="0.6" filter="url(#glow)">
    <animate attributeName="r" values="17;19;17" dur="4s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.6;0.3;0.6" dur="4s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Central geometric design -->
  <g transform="translate(24, 24)">
    <!-- Main hexagon -->
    <polygon points="-7.5,0 -3.75,-6.5 3.75,-6.5 7.5,0 3.75,6.5 -3.75,6.5" fill="url(#accentGradient)" opacity="0.9">
      <animateTransform attributeName="transform" attributeType="XML" type="rotate" from="0" to="360" dur="20s" repeatCount="indefinite"/>
    </polygon>
    
    <!-- Inner rotating triangles -->
    <polygon points="0,-4.5 -3.9,2.25 3.9,2.25" fill="url(#glowGradient)" opacity="0.8">
      <animateTransform attributeName="transform" attributeType="XML" type="rotate" from="0" to="-360" dur="8s" repeatCount="indefinite"/>
    </polygon>
    
    <!-- Central core -->
    <circle cx="0" cy="0" r="2.5" fill="url(#accentGradient)" filter="url(#glow)"/>
  </g>
  
  <!-- Floating geometric elements -->
  <g opacity="0.7">
    <!-- Top left -->
    <circle cx="9" cy="9" r="1.5" fill="url(#accentGradient)">
      <animate attributeName="cy" values="9;7;9" dur="3s" repeatCount="indefinite"/>
    </circle>
    
    <!-- Top right -->
    <rect x="37" y="8" width="2" height="2" rx="0.5" fill="url(#accentGradient)">
      <animate attributeName="y" values="8;6;8" dur="2.5s" repeatCount="indefinite"/>
    </rect>
    
    <!-- Bottom left -->
    <polygon points="9,39 8,41 10,41" fill="url(#accentGradient)">
      <animate attributeName="opacity" values="0.7;0.3;0.7" dur="2s" repeatCount="indefinite"/>
    </polygon>
    
    <!-- Bottom right -->
    <circle cx="39" cy="39" r="1.5" fill="url(#glowGradient)">
      <animate attributeName="r" values="1.5;2.5;1.5" dur="3.5s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Connecting lines -->
  <g stroke="url(#accentGradient)" stroke-width="0.5" opacity="0.4" fill="none">
    <path d="M 13 13 Q 24 18 35 13">
      <animate attributeName="opacity" values="0.4;0.1;0.4" dur="4s" repeatCount="indefinite"/>
    </path>
    <path d="M 13 35 Q 24 30 35 35">
      <animate attributeName="opacity" values="0.4;0.1;0.4" dur="3s" repeatCount="indefinite"/>
    </path>
  </g>
</svg>
