import{r as _,j as e,c as re}from"./globals-31bbfa66.js";import{i as K,s as Y}from"./extension-context-5094bf00.js";const x=({d:t,className:r="w-4 h-4"})=>e.jsx("svg",{className:r,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:t})}),h={refresh:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15",settings:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z M15 12a3 3 0 11-6 0 3 3 0 016 0z",close:"M6 18L18 6M6 6l12 12",alert:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z",play:"M14.828 14.828a4 4 0 01-5.656 0M9 10h1.586a1 1 0 01.707.293l2.414 2.414a1 1 0 00.707.293H15",pause:"M10 9v6m4-6v6",check:"M5 13l4 4L19 7",search:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z",withdrawal:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z",crypto:"M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"};function te(t){var T,C,D,A,F,E,U,k,$,L,O,R,P,M,w,Q,X,Z,ee,se;const r=t.walletCurrency,o=t.currency,n=t.makerPaymentMethod||t.takerPaymentMethod,m=((T=n==null?void 0:n.categoryId)==null?void 0:T.toLowerCase().includes("usdc"))||((A=(D=(C=n==null?void 0:n.version)==null?void 0:C.category)==null?void 0:D.translationTag)==null?void 0:A.toLowerCase().includes("usdc")),u=((F=t.metadata)==null?void 0:F.walletCurrencyPrecision)||(r==null?void 0:r.precision),p=((E=t.metadata)==null?void 0:E.localCurrencyPrecision)||(o==null?void 0:o.precision),f=u===6||p===6,I=((U=t.metadata)==null?void 0:U.isForThirdPartyPaymentMethod)===!1,g=((k=n==null?void 0:n.categoryId)==null?void 0:k.toLowerCase())||"",v=((O=(L=($=n==null?void 0:n.version)==null?void 0:$.category)==null?void 0:L.translationTag)==null?void 0:O.toLowerCase())||"",S=g.includes("gift-card")||v.includes("gift_card"),z=g.includes("bank")||v.includes("bank"),W=g.includes("card")||v.includes("card"),B=g.includes("transfer")||v.includes("transfer"),j=S||z||W||B,G=ie(n),H=((M=(P=(R=n==null?void 0:n.version)==null?void 0:R.image)==null?void 0:P.urls)==null?void 0:M.logo)||((X=(Q=(w=n==null?void 0:n.version)==null?void 0:w.image)==null?void 0:Q.urls)==null?void 0:X.medium),b=m||f||I;let y=!1,s="",a=(r==null?void 0:r.symbol)||"$",c=t.grossAmount||"0",l="",d="",N="";if(t.operationType==="BUY"&&j){y=!0,s="withdrawal";const i=t.rateInfo;i!=null&&i.fundsToSendTaker&&(i!=null&&i.fundsToReceiveTaker)?(c=i.fundsToSendTaker,l=i.fundsToReceiveTaker,d=(o==null?void 0:o.symbol)||"$",N=parseFloat(((Z=t.displayRate)==null?void 0:Z.rate)||"1").toFixed(4)):c=t.grossAmount||"0",a=(r==null?void 0:r.symbol)||"$"}else if(t.operationType==="SELL"&&j){s="deposit";const i=t.rateInfo;i!=null&&i.fundsToSendTaker&&(i!=null&&i.fundsToReceiveTaker)?(c=i.fundsToReceiveTaker,l=i.fundsToSendTaker,d=(o==null?void 0:o.symbol)||"$",N=parseFloat(((ee=t.displayRate)==null?void 0:ee.rate)||"1").toFixed(4)):c=t.grossAmount||"0",a=(r==null?void 0:r.symbol)||"$"}else if(b&&!j)if(t.operationType==="SELL"&&u===6)y=!0,s="withdrawal",a="USDC",c=t.grossAmount||"0";else if(t.operationType==="BUY"&&p===6){s="deposit",a="USDC";const i=t.rateInfo;i!=null&&i.fundsToSendTaker&&(i!=null&&i.fundsToReceiveTaker)?(c=i.fundsToReceiveTaker,l=i.fundsToSendTaker,d=(r==null?void 0:r.symbol)||"$",N=parseFloat(((se=t.displayRate)==null?void 0:se.rate)||"1").toFixed(4)):c=t.grossAmount||"0"}else s="exchange",u===6&&(a="USDC");return{amount:c,currency:a,originalAmount:l,originalCurrency:d,isUSDC:b,isWithdrawal:y,isDeposit:s==="deposit",operationType:s,conversionNote:S?"Gift Card Purchase":j?"External Service":void 0,serviceName:G,serviceLogo:H,exchangeRate:N}}function ie(t){var n,m;if(!t)return"Unknown";const r=t.categoryId||"",o=((m=(n=t.version)==null?void 0:n.category)==null?void 0:m.translationTag)||"";if(r){const u=r.split(":");if(u.length>2){const p=u[u.length-1].replace(/[-_]/g," ").replace(/\b\w/g,f=>f.toUpperCase());return p.toLowerCase()==="ebay"?"eBay":p.toLowerCase()==="paypal"?"PayPal":p.toLowerCase()==="amazon"?"Amazon":p}}return o&&o.replace("CATEGORY_TREE:AIRTM_","").replace("GIFT_CARD_","").replace("E_TRANSFER_","").replace("BANK_","").replace(/_/g," ").toLowerCase().replace(/\b\w/g,p=>p.toUpperCase())||"Unknown"}function ne(){var u,p,f,I,g,v,S,z,W,B,j,G,H,b,y;const[t,r]=_.useState({offers:[],settings:null,stats:null,selectedOffer:null,isLoading:!0,error:null,isConnected:!1}),o=_.useCallback(async()=>{r(s=>({...s,isLoading:!0,error:null}));try{if(!K())throw new Error("Extension context not available");const s=await Y({type:"GET_POPUP_DATA"});if(s!=null&&s.success&&s.data)r(a=>({...a,offers:s.data.offers||[],settings:s.data.settings||null,stats:s.data.stats||{totalOffers:0,newOffers:0,averageRate:0},isConnected:!0,isLoading:!1,error:null}));else throw new Error((s==null?void 0:s.error)||"Invalid response from background script")}catch(s){console.error("Popup data loading error:",s),r(a=>({...a,isLoading:!1,isConnected:!1,error:s instanceof Error?s.message:"Failed to load extension data"}))}},[]),n=_.useCallback(()=>{var a,c;const s=l=>{l.type==="OFFERS_UPDATED"?r(d=>({...d,offers:l.offers||[]})):l.type==="SETTINGS_UPDATED"?r(d=>({...d,settings:l.settings})):l.type==="STATS_UPDATED"&&r(d=>({...d,stats:l.stats}))};return K()?((c=(a=chrome.runtime)==null?void 0:a.onMessage)==null||c.addListener(s),()=>{var l,d;return(d=(l=chrome.runtime)==null?void 0:l.onMessage)==null?void 0:d.removeListener(s)}):()=>{}},[]);_.useEffect(()=>(o(),n()),[]);const m=_.useMemo(()=>({refresh:()=>o(),openSettings:()=>{try{if(!K())throw new Error("Extension context not available");chrome.runtime.openOptionsPage()}catch(s){console.error("Failed to open settings:",s),r(a=>({...a,error:"Failed to open settings page"}))}},selectOffer:s=>{r(a=>({...a,selectedOffer:s}))},updateSettings:async s=>{try{const a=await Y({type:"SETTINGS_UPDATE",data:s});if(a!=null&&a.success)r(c=>({...c,settings:s}));else throw new Error((a==null?void 0:a.error)||"Failed to update settings")}catch(a){console.error("Settings update error:",a),r(c=>({...c,error:a instanceof Error?a.message:"Failed to update settings"}))}},acceptOffer:async s=>{try{const a=await Y({type:"ACCEPT_OFFER",offerId:s.id||s.hash});if(!(a!=null&&a.success))throw new Error((a==null?void 0:a.error)||"Failed to accept offer")}catch(a){console.error("Accept offer error:",a),r(c=>({...c,error:a instanceof Error?a.message:"Failed to accept offer"}))}},rejectOffer:async s=>{try{const a=await Y({type:"REJECT_OFFER",offerId:s.id||s.hash});if(!(a!=null&&a.success))throw new Error((a==null?void 0:a.error)||"Failed to reject offer")}catch(a){console.error("Reject offer error:",a),r(c=>({...c,error:a instanceof Error?a.message:"Failed to reject offer"}))}},dismissError:()=>{r(s=>({...s,error:null}))}}),[o]);return t.isLoading?e.jsx("div",{className:"modern-popup",children:e.jsxs("div",{className:"loading-state",children:[e.jsxs("div",{className:"loading-logo",children:[e.jsx("div",{className:"logo-circle",children:"A"}),e.jsx("div",{className:"loading-pulse"})]}),e.jsxs("div",{className:"loading-content",children:[e.jsx("h2",{className:"loading-title",children:"Airtm Monitor Pro"}),e.jsx("p",{className:"loading-text",children:"Connecting to extension..."}),e.jsx("div",{className:"loading-progress",children:e.jsx("div",{className:"progress-bar"})})]})]})}):e.jsxs("div",{className:"modern-popup",children:[e.jsxs("header",{className:"popup-header",children:[e.jsxs("div",{className:"header-brand",children:[e.jsxs("div",{className:"brand-logo",children:[e.jsx("div",{className:"logo-circle",children:"A"}),e.jsx("div",{className:`status-dot ${(u=t.settings)!=null&&u.monitoring?"active":"inactive"}`})]}),e.jsxs("div",{className:"brand-text",children:[e.jsx("h1",{className:"brand-title",children:"Airtm Monitor Pro"}),e.jsxs("p",{className:"brand-subtitle",children:[t.isConnected?"Connected":"Disconnected"," •",t.offers.length," offers"]})]})]}),e.jsxs("div",{className:"header-actions",children:[e.jsx("button",{onClick:m.refresh,className:"action-button secondary",title:"Refresh data",children:e.jsx(x,{d:h.refresh})}),e.jsx("button",{onClick:m.openSettings,className:"action-button primary",title:"Open settings",children:e.jsx(x,{d:h.settings})})]})]}),t.error&&e.jsxs("div",{className:"error-banner",children:[e.jsxs("div",{className:"error-content",children:[e.jsx(x,{d:h.alert,className:"error-icon"}),e.jsx("span",{className:"error-message",children:t.error})]}),e.jsx("button",{onClick:m.dismissError,className:"error-dismiss",title:"Dismiss error",children:e.jsx(x,{d:h.close})})]}),e.jsx("section",{className:"stats-section",children:e.jsxs("div",{className:"stats-grid",children:[e.jsxs("div",{className:"stat-card",children:[e.jsx("div",{className:"stat-value",children:((p=t.stats)==null?void 0:p.totalOffers)||0}),e.jsx("div",{className:"stat-label",children:"Total Offers"})]}),e.jsxs("div",{className:"stat-card",children:[e.jsx("div",{className:"stat-value",children:((f=t.stats)==null?void 0:f.newOffers)||0}),e.jsx("div",{className:"stat-label",children:"New Today"})]}),e.jsxs("div",{className:"stat-card",children:[e.jsx("div",{className:"stat-value",children:((I=t.stats)==null?void 0:I.acceptedOffers)||0}),e.jsx("div",{className:"stat-label",children:"Accepted"})]}),e.jsxs("div",{className:"stat-card",children:[e.jsx("div",{className:"stat-value",children:((g=t.stats)==null?void 0:g.rejectedOffers)||0}),e.jsx("div",{className:"stat-label",children:"Rejected"})]})]})}),e.jsxs("main",{className:"main-content",children:[e.jsxs("div",{className:"content-header",children:[e.jsx("h3",{className:"content-title",children:"Available Offers"}),e.jsx("div",{className:"content-meta",children:e.jsxs("div",{className:"live-status",children:[e.jsx("div",{className:"pulse-indicator"}),e.jsx("span",{children:"Live"})]})})]}),e.jsx("div",{className:"offers-container",children:t.offers.length===0?e.jsxs("div",{className:"empty-state",children:[e.jsx(x,{d:h.search,className:"empty-icon"}),e.jsx("h4",{className:"empty-title",children:"No offers available"}),e.jsx("p",{className:"empty-text",children:(v=t.settings)!=null&&v.monitoring?"Monitoring is active. New offers will appear here.":"Start monitoring to see offers."})]}):e.jsx("div",{className:"offers-list",children:t.offers.map(s=>{var c,l,d,N,J,T,C,D,A,F,E,U,k,$,L,O,R,P,M;const a=te(s);return e.jsx("div",{className:`offer-item ${(((c=t.selectedOffer)==null?void 0:c.id)||((l=t.selectedOffer)==null?void 0:l.hash))===(s.id||s.hash)?"selected":""} ${a.isWithdrawal?"withdrawal":""} ${a.isDeposit?"deposit":""} ${a.isUSDC?"usdc-operation":""}`,onClick:()=>m.selectOffer(s),children:a.isWithdrawal?e.jsxs("div",{className:"withdrawal-offer",children:[e.jsx("div",{className:"withdrawal-header",children:e.jsx("span",{className:"withdrawal-title",children:"💸 Withdraw"})}),e.jsxs("div",{className:"service-info",children:[a.serviceLogo&&e.jsx("img",{src:a.serviceLogo,alt:a.serviceName,className:"service-logo",onError:w=>{w.currentTarget.style.display="none"}}),e.jsx("span",{className:"service-name",children:a.serviceName})]}),e.jsxs("div",{className:"withdrawal-amounts",children:[e.jsxs("div",{className:"amount-debit",children:["- $",parseFloat(a.amount).toFixed(a.isUSDC?6:2)," ",a.currency]}),a.originalAmount&&e.jsxs("div",{className:"amount-credit",children:["+ $",parseFloat(a.originalAmount).toFixed(2)," ",a.originalCurrency]}),a.exchangeRate&&e.jsxs("div",{className:"exchange-rate",children:["$1 ",a.currency," = $",a.exchangeRate," ",a.originalCurrency]})]}),e.jsxs("div",{className:"peer-section",children:[((J=(N=(d=s.peer)==null?void 0:d.preferences)==null?void 0:N.profile)==null?void 0:J.avatar)&&e.jsx("img",{src:s.peer.preferences.profile.avatar,alt:"Peer avatar",className:"peer-avatar"}),e.jsxs("div",{className:"peer-details",children:[e.jsx("div",{className:"peer-name",children:s.peer?`${s.peer.firstName} ${s.peer.lastName}`:"Unknown"}),e.jsx("div",{className:"peer-date",children:new Date(s.createdAt).toLocaleDateString("en-US",{month:"short",year:"numeric"})}),e.jsx("div",{className:"peer-location",children:((T=s.peer)==null?void 0:T.country)&&e.jsxs(e.Fragment,{children:[((A=(D=(C=s.peer.countryInfo)==null?void 0:C.image)==null?void 0:D.urls)==null?void 0:A.avatar)&&e.jsx("img",{src:s.peer.countryInfo.image.urls.avatar,alt:s.peer.country,className:"country-flag"}),s.peer.country]})}),((F=s.peer)==null?void 0:F.numbers)&&e.jsxs("div",{className:"peer-stats",children:[s.peer.numbers.completedOperations," txns • ",s.peer.numbers.score,"⭐"]}),e.jsx("div",{className:"offer-timestamp",children:new Date(s.createdAt).toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric",hour:"numeric",minute:"2-digit"})})]})]})]}):a.isDeposit?e.jsxs("div",{className:"deposit-offer",children:[e.jsx("div",{className:"deposit-header",children:e.jsx("span",{className:"deposit-title",children:"💰 Add"})}),e.jsxs("div",{className:"service-info",children:[a.serviceLogo&&e.jsx("img",{src:a.serviceLogo,alt:a.serviceName,className:"service-logo",onError:w=>{w.currentTarget.style.display="none"}}),e.jsx("span",{className:"service-name",children:a.serviceName})]}),e.jsxs("div",{className:"deposit-amounts",children:[e.jsxs("div",{className:"amount-credit",children:["+ $",parseFloat(a.amount).toFixed(a.isUSDC?6:2)," ",a.currency]}),a.originalAmount&&e.jsxs("div",{className:"amount-debit",children:["- $",parseFloat(a.originalAmount).toFixed(2)," ",a.originalCurrency]}),a.exchangeRate&&e.jsxs("div",{className:"exchange-rate",children:["$1 ",a.originalCurrency," = $",a.exchangeRate," ",a.currency]})]}),e.jsxs("div",{className:"peer-section",children:[((k=(U=(E=s.peer)==null?void 0:E.preferences)==null?void 0:U.profile)==null?void 0:k.avatar)&&e.jsx("img",{src:s.peer.preferences.profile.avatar,alt:"Peer avatar",className:"peer-avatar"}),e.jsxs("div",{className:"peer-details",children:[e.jsx("div",{className:"peer-name",children:s.peer?`${s.peer.firstName} ${s.peer.lastName}`:"Unknown"}),e.jsx("div",{className:"peer-date",children:new Date(s.createdAt).toLocaleDateString("en-US",{month:"short",year:"numeric"})}),e.jsx("div",{className:"peer-location",children:(($=s.peer)==null?void 0:$.country)&&e.jsxs(e.Fragment,{children:[((R=(O=(L=s.peer.countryInfo)==null?void 0:L.image)==null?void 0:O.urls)==null?void 0:R.avatar)&&e.jsx("img",{src:s.peer.countryInfo.image.urls.avatar,alt:s.peer.country,className:"country-flag"}),s.peer.country]})}),((P=s.peer)==null?void 0:P.numbers)&&e.jsxs("div",{className:"peer-stats",children:[s.peer.numbers.completedOperations," txns • ",s.peer.numbers.score,"⭐"]}),e.jsx("div",{className:"offer-timestamp",children:new Date(s.createdAt).toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric",hour:"numeric",minute:"2-digit"})})]})]})]}):e.jsxs("div",{className:"regular-offer",children:[e.jsxs("div",{className:"offer-header",children:[e.jsxs("div",{className:"offer-currency",children:[a.currency,a.isUSDC&&e.jsx("span",{className:"crypto-badge",children:e.jsx(x,{d:h.crypto,className:"w-3 h-3"})})]}),e.jsx("div",{className:"offer-type",children:s.operationType})]}),e.jsxs("div",{className:"offer-details",children:[e.jsxs("div",{className:"offer-amount",children:[a.currency==="USDC"?"":"$",parseFloat(a.amount).toFixed(a.isUSDC?6:2),a.currency==="USDC"?" USDC":""]}),e.jsxs("div",{className:"offer-rate",children:["Rate: ",s.rate||((M=s.displayRate)==null?void 0:M.rate)||"N/A"]})]}),e.jsx("div",{className:"offer-peer",children:s.peer?`${s.peer.firstName} ${s.peer.lastName}`:"Unknown"})]})},s.id||s.hash)})})})]}),e.jsxs("footer",{className:"control-panel",children:[t.selectedOffer&&e.jsxs("div",{className:"selected-offer",children:[e.jsxs("div",{className:"selected-info",children:[e.jsx("span",{className:"selected-label",children:"Selected:"}),e.jsx("span",{className:"selected-details",children:(()=>{const s=te(t.selectedOffer);return s.isWithdrawal?e.jsxs(e.Fragment,{children:["💸 Withdraw: -",s.currency==="USDC"?"":"$",parseFloat(s.amount).toFixed(s.isUSDC?6:2),s.currency==="USDC"?" USDC":" "+s.currency,s.originalAmount&&e.jsxs(e.Fragment,{children:[" → +$",parseFloat(s.originalAmount).toFixed(2)," ",s.originalCurrency]}),s.serviceName&&e.jsxs("span",{className:"text-blue-600 ml-1",children:["(",s.serviceName,")"]})]}):s.isDeposit?e.jsxs(e.Fragment,{children:["💰 Add: +",s.currency==="USDC"?"":"$",parseFloat(s.amount).toFixed(s.isUSDC?6:2),s.currency==="USDC"?" USDC":" "+s.currency,s.originalAmount&&e.jsxs(e.Fragment,{children:[" ← -$",parseFloat(s.originalAmount).toFixed(2)," ",s.originalCurrency]}),s.serviceName&&e.jsxs("span",{className:"text-green-600 ml-1",children:["(",s.serviceName,")"]})]}):e.jsxs(e.Fragment,{children:[s.currency," •",s.currency==="USDC"?"":"$",parseFloat(s.amount).toFixed(s.isUSDC?6:2),s.currency==="USDC"?" USDC":"",s.isUSDC&&e.jsx("span",{className:"text-blue-600 ml-1",children:"(USDC)"})]})})()})]}),e.jsx("div",{className:"selected-actions",children:e.jsxs("div",{className:"button-group-primary",children:[e.jsxs("button",{onClick:()=>m.acceptOffer(t.selectedOffer),className:"action-button success",title:"Accept this offer and proceed with the transaction",children:[e.jsx(x,{d:h.check,className:"w-4 h-4"}),"Accept",e.jsx("span",{className:"button-tooltip",children:"Accept this offer"})]}),e.jsxs("button",{onClick:()=>m.rejectOffer(t.selectedOffer),className:"action-button danger",title:"Reject this offer and remove it from the list",children:[e.jsx(x,{d:h.close,className:"w-4 h-4"}),"Reject",e.jsx("span",{className:"button-tooltip",children:"Reject this offer"})]})]})})]}),e.jsxs("div",{className:"main-controls",children:[e.jsxs("div",{className:"button-group",children:[e.jsxs("button",{onClick:()=>{var s;return m.updateSettings({...t.settings,monitoring:!((s=t.settings)!=null&&s.monitoring)})},className:`control-button ${(S=t.settings)!=null&&S.monitoring?"active":"inactive"}`,title:(z=t.settings)!=null&&z.monitoring?"Stop monitoring for new offers":"Start monitoring for new offers",children:[e.jsx(x,{d:(W=t.settings)!=null&&W.monitoring?h.pause:h.play,className:"w-3 h-3"}),e.jsx("span",{children:(B=t.settings)!=null&&B.monitoring?"Stop":"Start"}),e.jsx("span",{className:"button-tooltip",children:(j=t.settings)!=null&&j.monitoring?"Stop monitoring":"Start monitoring"})]}),e.jsxs("button",{onClick:()=>{var s;return m.updateSettings({...t.settings,autoAccept:!((s=t.settings)!=null&&s.autoAccept)})},className:`control-button ${(G=t.settings)!=null&&G.autoAccept?"active":"inactive"}`,title:"Toggle automatic acceptance of offers that meet your criteria",children:[e.jsx(x,{d:h.check,className:"w-3 h-3"}),e.jsx("span",{children:"Auto"}),e.jsx("span",{className:"button-tooltip",children:(H=t.settings)!=null&&H.autoAccept?"Disable auto-accept":"Enable auto-accept"})]})]}),e.jsxs("div",{className:"stats-summary",children:[e.jsxs("span",{className:"stat-item",children:[e.jsx(x,{d:h.search,className:"w-3 h-3"}),((b=t.stats)==null?void 0:b.totalOffers)||0]}),(y=t.stats)!=null&&y.newOffers?e.jsxs("span",{className:"stat-item new",children:["+",t.stats.newOffers]}):null]})]})]})]})}function V(){const t=document.documentElement,r=document.body,o=`
    width: 600px !important;
    height: 850px !important;
    min-width: 600px !important;
    min-height: 850px !important;
    max-width: 600px !important;
    max-height: 850px !important;
    margin: 0 !important;
    padding: 0 !important;
    overflow: hidden !important;
  `;t.style.cssText=o,r.style.cssText=o,r.className="popup",console.log("Popup dimensions enforced:",{htmlSize:`${t.offsetWidth}x${t.offsetHeight}`,bodySize:`${r.offsetWidth}x${r.offsetHeight}`,windowSize:`${window.innerWidth}x${window.innerHeight}`})}V();document.addEventListener("DOMContentLoaded",V);window.addEventListener("load",V);const q=document.getElementById("root");if(!q)throw new Error("Root element not found");q.style.cssText=`
  width: 600px !important;
  height: 850px !important;
  min-width: 600px !important;
  min-height: 850px !important;
  max-width: 600px !important;
  max-height: 850px !important;
  display: flex !important;
  flex-direction: column !important;
`;const ce=re(q);ce.render(e.jsx(ne,{}));
