import{r as P,j as e,c as re}from"./globals-77571fb8.js";import{i as G,s as W}from"./extension-context-5094bf00.js";const g=({d:t,className:r="w-4 h-4"})=>e.jsx("svg",{className:r,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:t})}),x={refresh:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15",settings:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z M15 12a3 3 0 11-6 0 3 3 0 016 0z",close:"M6 18L18 6M6 6l12 12",alert:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z",play:"M14.828 14.828a4 4 0 01-5.656 0M9 10h1.586a1 1 0 01.707.293l2.414 2.414a1 1 0 00.707.293H15",pause:"M10 9v6m4-6v6",check:"M5 13l4 4L19 7",search:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z",withdrawal:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z",crypto:"M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"};function te(t){var D,A,F,E,U,k,$,L,R,O,C,J,K,V,q,Q,X,Z,ee,se;const r=t.walletCurrency,c=t.currency,n=t.makerPaymentMethod||t.takerPaymentMethod,m=((D=n==null?void 0:n.categoryId)==null?void 0:D.toLowerCase().includes("usdc"))||((E=(F=(A=n==null?void 0:n.version)==null?void 0:A.category)==null?void 0:F.translationTag)==null?void 0:E.toLowerCase().includes("usdc")),h=((U=t.metadata)==null?void 0:U.walletCurrencyPrecision)||(r==null?void 0:r.precision),p=((k=t.metadata)==null?void 0:k.localCurrencyPrecision)||(c==null?void 0:c.precision),T=h===6||p===6,M=(($=t.metadata)==null?void 0:$.isForThirdPartyPaymentMethod)===!1,j=((L=n==null?void 0:n.categoryId)==null?void 0:L.toLowerCase())||"",N=((C=(O=(R=n==null?void 0:n.version)==null?void 0:R.category)==null?void 0:O.translationTag)==null?void 0:C.toLowerCase())||"",b=j.includes("gift-card")||N.includes("gift_card"),_=j.includes("bank")||N.includes("bank"),I=j.includes("card")||N.includes("card"),z=j.includes("transfer")||N.includes("transfer"),y=b||_||I||z,s=ie(n),a=((V=(K=(J=n==null?void 0:n.version)==null?void 0:J.image)==null?void 0:K.urls)==null?void 0:V.logo)||((X=(Q=(q=n==null?void 0:n.version)==null?void 0:q.image)==null?void 0:Q.urls)==null?void 0:X.medium),l=m||T||M;let d=!1,o="",v=(r==null?void 0:r.symbol)||"$",u=t.grossAmount||"0",f="",w="",S="";if(t.operationType==="BUY"&&y){d=!0,o="withdrawal";const i=t.rateInfo;i!=null&&i.fundsToSendTaker&&(i!=null&&i.fundsToReceiveTaker)?(u=i.fundsToSendTaker,f=i.fundsToReceiveTaker,w=(c==null?void 0:c.symbol)||"$",S=parseFloat(((Z=t.displayRate)==null?void 0:Z.rate)||"1").toFixed(4)):u=t.grossAmount||"0",v=(r==null?void 0:r.symbol)||"$"}else if(t.operationType==="SELL"&&y){o="deposit";const i=t.rateInfo;i!=null&&i.fundsToSendTaker&&(i!=null&&i.fundsToReceiveTaker)?(u=i.fundsToReceiveTaker,f=i.fundsToSendTaker,w=(c==null?void 0:c.symbol)||"$",S=parseFloat(((ee=t.displayRate)==null?void 0:ee.rate)||"1").toFixed(4)):u=t.grossAmount||"0",v=(r==null?void 0:r.symbol)||"$"}else if(l&&!y)if(t.operationType==="SELL"&&h===6)d=!0,o="withdrawal",v="USDC",u=t.grossAmount||"0";else if(t.operationType==="BUY"&&p===6){o="deposit",v="USDC";const i=t.rateInfo;i!=null&&i.fundsToSendTaker&&(i!=null&&i.fundsToReceiveTaker)?(u=i.fundsToReceiveTaker,f=i.fundsToSendTaker,w=(r==null?void 0:r.symbol)||"$",S=parseFloat(((se=t.displayRate)==null?void 0:se.rate)||"1").toFixed(4)):u=t.grossAmount||"0"}else o="exchange",h===6&&(v="USDC");return{amount:u,currency:v,originalAmount:f,originalCurrency:w,isUSDC:l,isWithdrawal:d,isDeposit:o==="deposit",operationType:o,conversionNote:b?"Gift Card Purchase":y?"External Service":void 0,serviceName:s,serviceLogo:a,exchangeRate:S}}function ie(t){var n,m;if(!t)return"Unknown";const r=t.categoryId||"",c=((m=(n=t.version)==null?void 0:n.category)==null?void 0:m.translationTag)||"";if(r){const h=r.split(":");if(h.length>2){const p=h[h.length-1].replace(/[-_]/g," ").replace(/\b\w/g,T=>T.toUpperCase());return p.toLowerCase()==="ebay"?"eBay":p.toLowerCase()==="paypal"?"PayPal":p.toLowerCase()==="amazon"?"Amazon":p}}return c&&c.replace("CATEGORY_TREE:AIRTM_","").replace("GIFT_CARD_","").replace("E_TRANSFER_","").replace("BANK_","").replace(/_/g," ").toLowerCase().replace(/\b\w/g,p=>p.toUpperCase())||"Unknown"}function ne(){var h,p,T,M,j,N,b,_,I,z,y;const[t,r]=P.useState({offers:[],settings:null,stats:null,selectedOffer:null,isLoading:!0,error:null,isConnected:!1}),c=P.useCallback(async()=>{r(s=>({...s,isLoading:!0,error:null}));try{if(!G())throw new Error("Extension context not available");const s=await W({type:"GET_POPUP_DATA"});if(s!=null&&s.success&&s.data)r(a=>({...a,offers:s.data.offers||[],settings:s.data.settings||null,stats:s.data.stats||{totalOffers:0,newOffers:0,averageRate:0},isConnected:!0,isLoading:!1,error:null}));else throw new Error((s==null?void 0:s.error)||"Invalid response from background script")}catch(s){console.error("Popup data loading error:",s),r(a=>({...a,isLoading:!1,isConnected:!1,error:s instanceof Error?s.message:"Failed to load extension data"}))}},[]),n=P.useCallback(()=>{var a,l;const s=d=>{d.type==="OFFERS_UPDATED"?r(o=>({...o,offers:d.offers||[]})):d.type==="SETTINGS_UPDATED"?r(o=>({...o,settings:d.settings})):d.type==="STATS_UPDATED"&&r(o=>({...o,stats:d.stats}))};return G()?((l=(a=chrome.runtime)==null?void 0:a.onMessage)==null||l.addListener(s),()=>{var d,o;return(o=(d=chrome.runtime)==null?void 0:d.onMessage)==null?void 0:o.removeListener(s)}):()=>{}},[]);P.useEffect(()=>(c(),n()),[]);const m=P.useMemo(()=>({refresh:()=>c(),openSettings:()=>{try{if(!G())throw new Error("Extension context not available");chrome.runtime.openOptionsPage()}catch(s){console.error("Failed to open settings:",s),r(a=>({...a,error:"Failed to open settings page"}))}},selectOffer:s=>{r(a=>({...a,selectedOffer:s}))},updateSettings:async s=>{try{const a=await W({type:"SETTINGS_UPDATE",data:s});if(a!=null&&a.success)r(l=>({...l,settings:s}));else throw new Error((a==null?void 0:a.error)||"Failed to update settings")}catch(a){console.error("Settings update error:",a),r(l=>({...l,error:a instanceof Error?a.message:"Failed to update settings"}))}},acceptOffer:async s=>{try{const a=await W({type:"ACCEPT_OFFER",offerId:s.id||s.hash});if(!(a!=null&&a.success))throw new Error((a==null?void 0:a.error)||"Failed to accept offer")}catch(a){console.error("Accept offer error:",a),r(l=>({...l,error:a instanceof Error?a.message:"Failed to accept offer"}))}},rejectOffer:async s=>{try{const a=await W({type:"REJECT_OFFER",offerId:s.id||s.hash});if(!(a!=null&&a.success))throw new Error((a==null?void 0:a.error)||"Failed to reject offer")}catch(a){console.error("Reject offer error:",a),r(l=>({...l,error:a instanceof Error?a.message:"Failed to reject offer"}))}},dismissError:()=>{r(s=>({...s,error:null}))}}),[c]);return t.isLoading?e.jsx("div",{className:"modern-popup",children:e.jsxs("div",{className:"loading-state",children:[e.jsxs("div",{className:"loading-logo",children:[e.jsx("div",{className:"logo-circle",children:"A"}),e.jsx("div",{className:"loading-pulse"})]}),e.jsxs("div",{className:"loading-content",children:[e.jsx("h2",{className:"loading-title",children:"Airtm Monitor Pro"}),e.jsx("p",{className:"loading-text",children:"Connecting to extension..."}),e.jsx("div",{className:"loading-progress",children:e.jsx("div",{className:"progress-bar"})})]})]})}):e.jsxs("div",{className:"modern-popup",children:[e.jsxs("header",{className:"popup-header",children:[e.jsxs("div",{className:"header-brand",children:[e.jsxs("div",{className:"brand-logo",children:[e.jsx("div",{className:"logo-circle",children:"A"}),e.jsx("div",{className:`status-dot ${(h=t.settings)!=null&&h.monitoring?"active":"inactive"}`})]}),e.jsxs("div",{className:"brand-text",children:[e.jsx("h1",{className:"brand-title",children:"Airtm Monitor Pro"}),e.jsxs("p",{className:"brand-subtitle",children:[t.isConnected?"Connected":"Disconnected"," •",t.offers.length," offers"]})]})]}),e.jsxs("div",{className:"header-actions",children:[e.jsx("button",{onClick:m.refresh,className:"action-button secondary",title:"Refresh data",children:e.jsx(g,{d:x.refresh})}),e.jsx("button",{onClick:m.openSettings,className:"action-button primary",title:"Open settings",children:e.jsx(g,{d:x.settings})})]})]}),t.error&&e.jsxs("div",{className:"error-banner",children:[e.jsxs("div",{className:"error-content",children:[e.jsx(g,{d:x.alert,className:"error-icon"}),e.jsx("span",{className:"error-message",children:t.error})]}),e.jsx("button",{onClick:m.dismissError,className:"error-dismiss",title:"Dismiss error",children:e.jsx(g,{d:x.close})})]}),e.jsx("section",{className:"stats-section",children:e.jsxs("div",{className:"stats-grid",children:[e.jsxs("div",{className:"stat-card",children:[e.jsx("div",{className:"stat-value",children:((p=t.stats)==null?void 0:p.totalOffers)||0}),e.jsx("div",{className:"stat-label",children:"Total Offers"})]}),e.jsxs("div",{className:"stat-card",children:[e.jsx("div",{className:"stat-value",children:((T=t.stats)==null?void 0:T.newOffers)||0}),e.jsx("div",{className:"stat-label",children:"New Today"})]}),e.jsxs("div",{className:"stat-card",children:[e.jsx("div",{className:"stat-value",children:((M=t.stats)==null?void 0:M.acceptedOffers)||0}),e.jsx("div",{className:"stat-label",children:"Accepted"})]}),e.jsxs("div",{className:"stat-card",children:[e.jsx("div",{className:"stat-value",children:((j=t.stats)==null?void 0:j.rejectedOffers)||0}),e.jsx("div",{className:"stat-label",children:"Rejected"})]})]})}),e.jsxs("main",{className:"main-content",children:[e.jsxs("div",{className:"content-header",children:[e.jsx("h3",{className:"content-title",children:"Available Offers"}),e.jsx("div",{className:"content-meta",children:e.jsxs("div",{className:"live-status",children:[e.jsx("div",{className:"pulse-indicator"}),e.jsx("span",{children:"Live"})]})})]}),e.jsx("div",{className:"offers-container",children:t.offers.length===0?e.jsxs("div",{className:"empty-state",children:[e.jsx(g,{d:x.search,className:"empty-icon"}),e.jsx("h4",{className:"empty-title",children:"No offers available"}),e.jsx("p",{className:"empty-text",children:(N=t.settings)!=null&&N.monitoring?"Monitoring is active. New offers will appear here.":"Start monitoring to see offers."})]}):e.jsx("div",{className:"offers-list",children:t.offers.map(s=>{var l,d,o,v,u,f,w,S,B,D,A,F,E,U,k,$,L,R,O;const a=te(s);return e.jsx("div",{className:`offer-item ${(((l=t.selectedOffer)==null?void 0:l.id)||((d=t.selectedOffer)==null?void 0:d.hash))===(s.id||s.hash)?"selected":""} ${a.isWithdrawal?"withdrawal":""} ${a.isDeposit?"deposit":""} ${a.isUSDC?"usdc-operation":""}`,onClick:()=>m.selectOffer(s),children:a.isWithdrawal?e.jsxs("div",{className:"withdrawal-offer",children:[e.jsx("div",{className:"withdrawal-header",children:e.jsx("span",{className:"withdrawal-title",children:"💸 Withdraw"})}),e.jsxs("div",{className:"service-info",children:[a.serviceLogo&&e.jsx("img",{src:a.serviceLogo,alt:a.serviceName,className:"service-logo",onError:C=>{C.currentTarget.style.display="none"}}),e.jsx("span",{className:"service-name",children:a.serviceName})]}),e.jsxs("div",{className:"withdrawal-amounts",children:[e.jsxs("div",{className:"amount-debit",children:["- $",parseFloat(a.amount).toFixed(a.isUSDC?6:2)," ",a.currency]}),a.originalAmount&&e.jsxs("div",{className:"amount-credit",children:["+ $",parseFloat(a.originalAmount).toFixed(2)," ",a.originalCurrency]}),a.exchangeRate&&e.jsxs("div",{className:"exchange-rate",children:["$1 ",a.currency," = $",a.exchangeRate," ",a.originalCurrency]})]}),e.jsxs("div",{className:"peer-section",children:[((u=(v=(o=s.peer)==null?void 0:o.preferences)==null?void 0:v.profile)==null?void 0:u.avatar)&&e.jsx("img",{src:s.peer.preferences.profile.avatar,alt:"Peer avatar",className:"peer-avatar"}),e.jsxs("div",{className:"peer-details",children:[e.jsx("div",{className:"peer-name",children:s.peer?`${s.peer.firstName} ${s.peer.lastName}`:"Unknown"}),e.jsx("div",{className:"peer-date",children:new Date(s.createdAt).toLocaleDateString("en-US",{month:"short",year:"numeric"})}),e.jsx("div",{className:"peer-location",children:((f=s.peer)==null?void 0:f.country)&&e.jsxs(e.Fragment,{children:[((B=(S=(w=s.peer.countryInfo)==null?void 0:w.image)==null?void 0:S.urls)==null?void 0:B.avatar)&&e.jsx("img",{src:s.peer.countryInfo.image.urls.avatar,alt:s.peer.country,className:"country-flag"}),s.peer.country]})}),((D=s.peer)==null?void 0:D.numbers)&&e.jsxs("div",{className:"peer-stats",children:[s.peer.numbers.completedOperations," txns • ",s.peer.numbers.score,"⭐"]}),e.jsx("div",{className:"offer-timestamp",children:new Date(s.createdAt).toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric",hour:"numeric",minute:"2-digit"})})]})]})]}):a.isDeposit?e.jsxs("div",{className:"deposit-offer",children:[e.jsx("div",{className:"deposit-header",children:e.jsx("span",{className:"deposit-title",children:"💰 Add"})}),e.jsxs("div",{className:"service-info",children:[a.serviceLogo&&e.jsx("img",{src:a.serviceLogo,alt:a.serviceName,className:"service-logo",onError:C=>{C.currentTarget.style.display="none"}}),e.jsx("span",{className:"service-name",children:a.serviceName})]}),e.jsxs("div",{className:"deposit-amounts",children:[e.jsxs("div",{className:"amount-credit",children:["+ $",parseFloat(a.amount).toFixed(a.isUSDC?6:2)," ",a.currency]}),a.originalAmount&&e.jsxs("div",{className:"amount-debit",children:["- $",parseFloat(a.originalAmount).toFixed(2)," ",a.originalCurrency]}),a.exchangeRate&&e.jsxs("div",{className:"exchange-rate",children:["$1 ",a.originalCurrency," = $",a.exchangeRate," ",a.currency]})]}),e.jsxs("div",{className:"peer-section",children:[((E=(F=(A=s.peer)==null?void 0:A.preferences)==null?void 0:F.profile)==null?void 0:E.avatar)&&e.jsx("img",{src:s.peer.preferences.profile.avatar,alt:"Peer avatar",className:"peer-avatar"}),e.jsxs("div",{className:"peer-details",children:[e.jsx("div",{className:"peer-name",children:s.peer?`${s.peer.firstName} ${s.peer.lastName}`:"Unknown"}),e.jsx("div",{className:"peer-date",children:new Date(s.createdAt).toLocaleDateString("en-US",{month:"short",year:"numeric"})}),e.jsx("div",{className:"peer-location",children:((U=s.peer)==null?void 0:U.country)&&e.jsxs(e.Fragment,{children:[((L=($=(k=s.peer.countryInfo)==null?void 0:k.image)==null?void 0:$.urls)==null?void 0:L.avatar)&&e.jsx("img",{src:s.peer.countryInfo.image.urls.avatar,alt:s.peer.country,className:"country-flag"}),s.peer.country]})}),((R=s.peer)==null?void 0:R.numbers)&&e.jsxs("div",{className:"peer-stats",children:[s.peer.numbers.completedOperations," txns • ",s.peer.numbers.score,"⭐"]}),e.jsx("div",{className:"offer-timestamp",children:new Date(s.createdAt).toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric",hour:"numeric",minute:"2-digit"})})]})]})]}):e.jsxs("div",{className:"regular-offer",children:[e.jsxs("div",{className:"offer-header",children:[e.jsxs("div",{className:"offer-currency",children:[a.currency,a.isUSDC&&e.jsx("span",{className:"crypto-badge",children:e.jsx(g,{d:x.crypto,className:"w-3 h-3"})})]}),e.jsx("div",{className:"offer-type",children:s.operationType})]}),e.jsxs("div",{className:"offer-details",children:[e.jsxs("div",{className:"offer-amount",children:[a.currency==="USDC"?"":"$",parseFloat(a.amount).toFixed(a.isUSDC?6:2),a.currency==="USDC"?" USDC":""]}),e.jsxs("div",{className:"offer-rate",children:["Rate: ",s.rate||((O=s.displayRate)==null?void 0:O.rate)||"N/A"]})]}),e.jsx("div",{className:"offer-peer",children:s.peer?`${s.peer.firstName} ${s.peer.lastName}`:"Unknown"})]})},s.id||s.hash)})})})]}),e.jsxs("footer",{className:"control-panel",children:[t.selectedOffer&&e.jsxs("div",{className:"selected-offer",children:[e.jsxs("div",{className:"selected-info",children:[e.jsx("span",{className:"selected-label",children:"Selected:"}),e.jsx("span",{className:"selected-details",children:(()=>{const s=te(t.selectedOffer);return s.isWithdrawal?e.jsxs(e.Fragment,{children:["💸 Withdraw: -",s.currency==="USDC"?"":"$",parseFloat(s.amount).toFixed(s.isUSDC?6:2),s.currency==="USDC"?" USDC":" "+s.currency,s.originalAmount&&e.jsxs(e.Fragment,{children:[" → +$",parseFloat(s.originalAmount).toFixed(2)," ",s.originalCurrency]}),s.serviceName&&e.jsxs("span",{className:"text-blue-600 ml-1",children:["(",s.serviceName,")"]})]}):s.isDeposit?e.jsxs(e.Fragment,{children:["💰 Add: +",s.currency==="USDC"?"":"$",parseFloat(s.amount).toFixed(s.isUSDC?6:2),s.currency==="USDC"?" USDC":" "+s.currency,s.originalAmount&&e.jsxs(e.Fragment,{children:[" ← -$",parseFloat(s.originalAmount).toFixed(2)," ",s.originalCurrency]}),s.serviceName&&e.jsxs("span",{className:"text-green-600 ml-1",children:["(",s.serviceName,")"]})]}):e.jsxs(e.Fragment,{children:[s.currency," •",s.currency==="USDC"?"":"$",parseFloat(s.amount).toFixed(s.isUSDC?6:2),s.currency==="USDC"?" USDC":"",s.isUSDC&&e.jsx("span",{className:"text-blue-600 ml-1",children:"(USDC)"})]})})()})]}),e.jsxs("div",{className:"selected-actions",children:[e.jsxs("button",{onClick:()=>m.acceptOffer(t.selectedOffer),className:"action-button success",title:"Accept offer",children:[e.jsx(g,{d:x.check}),"Accept"]}),e.jsxs("button",{onClick:()=>m.rejectOffer(t.selectedOffer),className:"action-button danger",title:"Reject offer",children:[e.jsx(g,{d:x.close}),"Reject"]})]})]}),e.jsxs("div",{className:"main-controls",children:[e.jsxs("button",{onClick:()=>{var s;return m.updateSettings({...t.settings,monitoring:!((s=t.settings)!=null&&s.monitoring)})},className:`control-button ${(b=t.settings)!=null&&b.monitoring?"active":"inactive"}`,title:(_=t.settings)!=null&&_.monitoring?"Stop monitoring":"Start monitoring",children:[e.jsx(g,{d:(I=t.settings)!=null&&I.monitoring?x.pause:x.play}),e.jsx("span",{children:(z=t.settings)!=null&&z.monitoring?"Stop":"Start"})]}),e.jsxs("button",{onClick:()=>{var s;return m.updateSettings({...t.settings,autoAccept:!((s=t.settings)!=null&&s.autoAccept)})},className:`control-button ${(y=t.settings)!=null&&y.autoAccept?"active":"inactive"}`,title:"Toggle auto-accept",children:[e.jsx(g,{d:x.check}),e.jsx("span",{children:"Auto Accept"})]})]})]})]})}function H(){const t=document.documentElement,r=document.body,c=`
    width: 600px !important;
    height: 850px !important;
    min-width: 600px !important;
    min-height: 850px !important;
    max-width: 600px !important;
    max-height: 850px !important;
    margin: 0 !important;
    padding: 0 !important;
    overflow: hidden !important;
  `;t.style.cssText=c,r.style.cssText=c,r.className="popup",console.log("Popup dimensions enforced:",{htmlSize:`${t.offsetWidth}x${t.offsetHeight}`,bodySize:`${r.offsetWidth}x${r.offsetHeight}`,windowSize:`${window.innerWidth}x${window.innerHeight}`})}H();document.addEventListener("DOMContentLoaded",H);window.addEventListener("load",H);const Y=document.getElementById("root");if(!Y)throw new Error("Root element not found");Y.style.cssText=`
  width: 600px !important;
  height: 850px !important;
  min-width: 600px !important;
  min-height: 850px !important;
  max-width: 600px !important;
  max-height: 850px !important;
  display: flex !important;
  flex-direction: column !important;
`;const ce=re(Y);ce.render(e.jsx(ne,{}));
