/**
 * Fetch Interceptor for Airtm GraphQL API
 * Intercepts and processes GraphQL requests/responses
 */

import { parseAvailableOperations, extractBearerToken } from './data-parser'
import type { AirtmOperation } from '../types/airtm-schema'

// GraphQL operations to monitor
const MONITORED_OPERATIONS = [
  'AvailableOperations',
  'GetBalance',
  'GetTransactions',
  'CreateOperation',
  'AcceptOperation',
  'OperationDetails',  // Track accepted operation details
  'UnmatchOperationMutation'  // Track declined operations
]

// Airtm GraphQL endpoint
const AIRTM_GRAPHQL_ENDPOINT = 'https://app.airtm.com/graphql'

// Cache for preventing duplicate processing
const responseCache = new Map<string, number>()
const CACHE_DURATION = 2000 // 2 seconds - reduced for better polling capture

/**
 * Initialize XMLHttpRequest interceptor to monitor Airtm GraphQL requests
 */
function initializeXHRInterceptor(): void {
  console.log('🔧 Initializing XHR interceptor...')
  
  if (typeof window === 'undefined' || !window.XMLHttpRequest) {
    console.warn('❌ XHR interceptor: XMLHttpRequest not available')
    return
  }

  const originalXHR = window.XMLHttpRequest
  const originalOpen = originalXHR.prototype.open
  const originalSend = originalXHR.prototype.send

  // Override XMLHttpRequest.prototype.open
  originalXHR.prototype.open = function(method: string, url: string | URL, async?: boolean, username?: string | null, password?: string | null) {
    const urlString = typeof url === 'string' ? url : url.toString();
    console.log('🌐 XHR open called for URL:', urlString);
    
    // Store request details for later use
    (this as any)._interceptedMethod = method;
    (this as any)._interceptedUrl = urlString;
    
    return originalOpen.call(this, method, url, async ?? true, username, password);
  }

  // Override XMLHttpRequest.prototype.send
  originalXHR.prototype.send = function(body?: any) {
    const method = (this as any)._interceptedMethod;
    const url = (this as any)._interceptedUrl;
    
    console.log('📤 XHR send called:', method, url);
    
    // Check if this is an Airtm GraphQL request
    if (isAirtmGraphQLCall(url, { method, body })) {
      console.log('🎯 Intercepting Airtm GraphQL XHR request:', url);
      
      // Store original onreadystatechange
      const originalOnReadyStateChange = this.onreadystatechange;
      
      // Override onreadystatechange to intercept response
      this.onreadystatechange = function(event) {
        if (this.readyState === 4 && this.status === 200) {
          console.log('📥 XHR GraphQL response received, status:', this.status);
          
          try {
            const responseText = this.responseText;
            if (responseText) {
              console.log('📄 XHR Response preview:', responseText.substring(0, 200) + '...');
              
              // Process the response
              try {
                 processGraphQLResponseText(body || '');
               } catch (error) {
                 console.error('❌ Error processing XHR GraphQL response:', error);
               }
            }
          } catch (error) {
            console.error('❌ Error parsing XHR response:', error);
          }
        }
        
        // Call original handler if it exists
        if (originalOnReadyStateChange) {
          return originalOnReadyStateChange.apply(this, [event]);
        }
      };
    } else {
      console.log('⏭️ Skipping non-GraphQL XHR request:', url);
    }
    
    return originalSend.apply(this, [body]);
  };
  
  console.log('✅ XHR interceptor initialized');
}

/**
 * Initialize all interceptors to monitor Airtm GraphQL requests
 */
export function initializeFetchInterceptor(): void {
  console.log('🔧 Initializing all interceptors (fetch, XHR, WebSocket, DOM observer)...')
  
  if (typeof window === 'undefined') {
    console.warn('❌ Fetch interceptor: window not available')
    return
  }
  
  console.log('✅ Window is available')
  console.log('📋 Monitored operations:', MONITORED_OPERATIONS)
  console.log('🌐 Target GraphQL endpoint:', AIRTM_GRAPHQL_ENDPOINT)

  // Initialize fetch interceptor if available
  if (window.fetch) {
    const originalFetch = window.fetch
    window.fetch = interceptFetch(originalFetch)
    console.log('✅ Fetch interceptor initialized')
  } else {
    console.warn('⚠️ window.fetch not available')
  }

  // Initialize all other interceptors
  initializeXHRInterceptor()
  initializeWebSocketInterceptor()
  initializeDOMObserver()
  initializeNetworkMonitor()
  initializeEventListeners()
  
  // Initialize advanced monitoring techniques
  initializeAdvancedMonitoring()

  console.log('🎉 All interceptors and monitoring techniques initialized successfully')
  
  // Add a comprehensive status check
  setTimeout(() => {
    console.log('📊 Extension Status Check:');
    console.log('- Fetch interceptor:', typeof window.fetch === 'function' ? '✅' : '❌');
    console.log('- XHR interceptor:', typeof XMLHttpRequest !== 'undefined' ? '✅' : '❌');
    console.log('- WebSocket interceptor:', typeof WebSocket !== 'undefined' ? '✅' : '❌');
    console.log('- DOM observer:', typeof MutationObserver !== 'undefined' ? '✅' : '❌');
    console.log('- Performance API:', typeof PerformanceObserver !== 'undefined' ? '✅' : '❌');
    console.log('- Service Worker:', 'serviceWorker' in navigator ? '✅' : '❌');
    console.log('- Global function:', typeof (window as any).airtmExtensionReceiveData === 'function' ? '✅' : '❌');
    console.log('🔍 If you still don\'t see offers, check the Network tab in DevTools for GraphQL requests');
  }, 1000);
  console.log('🔍 Ready to intercept GraphQL requests!')
}

/**
 * Initialize WebSocket interceptor to catch real-time communications
 */
function initializeWebSocketInterceptor(): void {
  console.log('🔧 Initializing WebSocket interceptor...');
  
  if (typeof WebSocket !== 'undefined') {
    const OriginalWebSocket = WebSocket;
    
    (window as any).WebSocket = function(url: string | URL, protocols?: string | string[]) {
      console.log('🔌 WebSocket connection intercepted:', url);
      
      const ws = new OriginalWebSocket(url, protocols);
      
      // Intercept messages
      const originalAddEventListener = ws.addEventListener;
      ws.addEventListener = function(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions) {
        if (type === 'message') {
          const wrappedListener = (event: Event) => {
            const messageEvent = event as MessageEvent;
            console.log('📨 WebSocket message received:', messageEvent.data);
            
            try {
              const parsedData = JSON.parse(messageEvent.data);
              if (parsedData && (parsedData.data || parsedData.errors)) {
                console.log('🎯 Potential GraphQL data via WebSocket:', parsedData);
                processGraphQLResponseText(messageEvent.data);
              }
            } catch (e) {
              // Not JSON, ignore
            }
            
            if (typeof listener === 'function') {
              return (listener as Function).call(ws, event);
            } else if (listener && typeof listener.handleEvent === 'function') {
              return listener.handleEvent(event);
            }
          };
          return originalAddEventListener.call(this, type, wrappedListener as EventListener, options);
        }
        return originalAddEventListener.call(this, type, listener, options);
      };
      
      return ws;
    };
    
    console.log('✅ WebSocket interceptor initialized');
  } else {
    console.warn('⚠️ WebSocket not available');
  }
}

/**
 * Initialize DOM observer to watch for dynamic content changes
 */
function initializeDOMObserver(): void {
  console.log('🔧 Initializing DOM observer...');
  
  if (typeof MutationObserver !== 'undefined') {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element;
              
              // Look for elements that might contain offer data
              if (element.textContent && element.textContent.includes('availableOperations')) {
                console.log('🎯 DOM element with availableOperations detected:', element);
                try {
                  const jsonMatch = element.textContent.match(/\{.*availableOperations.*\}/);
                  if (jsonMatch) {
                    processGraphQLResponseText(jsonMatch[0]);
                  }
                } catch (e) {
                  console.log('❌ Failed to parse DOM content as JSON');
                }
              }
              
              // Check for script tags with JSON data
              if (element.tagName === 'SCRIPT' && element.textContent) {
                try {
                  const scriptData = JSON.parse(element.textContent);
                  if (scriptData && scriptData.data && scriptData.data.availableOperations) {
                    console.log('🎯 Script tag with availableOperations detected');
                    processGraphQLResponseText(element.textContent || '');
                  }
                } catch (e) {
                  // Not JSON, ignore
                }
              }
              
              // Check for data attributes
              const dataAttrs = element.getAttributeNames().filter(name => name.startsWith('data-'));
              dataAttrs.forEach(attr => {
                const value = element.getAttribute(attr);
                if (value && (value.includes('availableOperations') || value.includes('graphql'))) {
                  console.log('📄 Found GraphQL data in data attribute:', attr);
                  processGraphQLResponseText(value);
                }
              });
              
              // Check for React component props or state
              const reactKeys = Object.keys(element).filter(key => key.startsWith('__react'));
              reactKeys.forEach(key => {
                try {
                  const reactData = (element as any)[key];
                  if (reactData) {
                    const reactStr = JSON.stringify(reactData);
                    if (reactStr.includes('availableOperations')) {
                      console.log('🎯 Found GraphQL data in React component:', key);
                      processGraphQLResponseText(reactStr);
                    }
                  }
                } catch (e) {
                  // Ignore React data access errors
                }
              });
            }
          });
        }
        
        // Also check for attribute changes that might contain GraphQL data
        if (mutation.type === 'attributes') {
          const target = mutation.target as Element;
          const attrValue = target.getAttribute(mutation.attributeName || '');
          if (attrValue && attrValue.includes('availableOperations')) {
            console.log('📄 Found GraphQL data in modified attribute:', mutation.attributeName);
            processGraphQLResponseText(attrValue);
          }
        }
      });
    });
    
    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['data-state', 'data-props', 'data-apollo', 'data-graphql']
    });
    
    console.log('✅ DOM observer initialized');
  } else {
    console.warn('⚠️ MutationObserver not available');
  }
  
  // Additional periodic DOM scanning
  setInterval(() => {
    // Scan all script tags periodically
    const scripts = document.querySelectorAll('script');
    scripts.forEach((script, index) => {
      if (script.textContent && script.textContent.includes('availableOperations') && !(script as any).scanned) {
        console.log(`🔍 Periodic scan found GraphQL data in script ${index}`);
        processGraphQLResponseText(script.textContent);
        (script as any).scanned = true;
      }
    });
    
    // Scan for elements with data attributes
    const elementsWithData = document.querySelectorAll('[data-state], [data-props], [data-apollo], [data-graphql]');
    elementsWithData.forEach((element, index) => {
      const dataAttrs = ['data-state', 'data-props', 'data-apollo', 'data-graphql'];
      dataAttrs.forEach(attr => {
        const value = element.getAttribute(attr);
        if (value && value.includes('availableOperations') && !(element as any)[attr + '_scanned']) {
          console.log(`🔍 Periodic scan found GraphQL data in ${attr} of element ${index}`);
          processGraphQLResponseText(value);
          (element as any)[attr + '_scanned'] = true;
        }
      });
    });
  }, 3000);
}

/**
 * Initialize network monitoring using Performance API
 */
function initializeNetworkMonitor(): void {
  console.log('🔧 Initializing network monitor...');
  
  if (typeof PerformanceObserver !== 'undefined') {
    try {
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.name.includes('graphql') || entry.name.includes('airtm.com')) {
            console.log('🌐 Network request detected via Performance API:', entry.name);
            
            // Try to intercept the response using fetch override
            if (entry.name.includes('graphql')) {
              console.log('🎯 GraphQL request detected, attempting to capture response...');
              
              // Check if we can access the response through the global fetch cache
              setTimeout(() => {
                try {
                  // Look for cached responses in common locations
                  const possibleCaches = [
                    (window as any).__APOLLO_CLIENT__,
                    (window as any).__NEXT_DATA__,
                    (window as any).__INITIAL_STATE__,
                    (window as any).store,
                    (window as any).app
                  ];
                  
                  possibleCaches.forEach((cache, index) => {
                    if (cache) {
                      console.log(`🔍 Checking cache location ${index + 1}:`, cache);
                      const cacheStr = JSON.stringify(cache);
                      if (cacheStr.includes('availableOperations')) {
                        console.log('🎯 Found availableOperations in cache!');
                        processGraphQLResponseText(cacheStr);
                      }
                    }
                  });
                } catch (e) {
                  console.warn('⚠️ Error checking caches:', e);
                }
              }, 100);
            }
          }
        });
      });
      
      observer.observe({ entryTypes: ['resource'] });
      console.log('✅ Network monitor initialized');
    } catch (e) {
      console.warn('⚠️ PerformanceObserver failed to initialize:', e);
    }
  } else {
    console.warn('⚠️ PerformanceObserver not available');
  }
  
  // Enhanced Resource Timing API monitoring
  setInterval(() => {
    const resources = performance.getEntriesByType('resource');
    resources.forEach((resource) => {
      if (resource.name.includes('graphql') && !(resource as any).logged) {
        console.log('📊 Resource timing detected GraphQL call:', resource.name);
        
        // Try to find the response data
        setTimeout(() => {
          // Check for response data in various locations
          const scripts = document.querySelectorAll('script');
          scripts.forEach(script => {
            if (script.textContent && script.textContent.includes('availableOperations')) {
              console.log('🎯 Found GraphQL data in script tag!');
              processGraphQLResponseText(script.textContent);
            }
          });
          
          // Check for data in window object
          Object.keys(window).forEach(key => {
            try {
              const value = (window as any)[key];
              if (value && typeof value === 'object') {
                const valueStr = JSON.stringify(value);
                if (valueStr.includes('availableOperations')) {
                  console.log('🎯 Found GraphQL data in window.' + key);
                  processGraphQLResponseText(valueStr);
                }
              }
            } catch (e) {
              // Ignore errors when accessing window properties
            }
          });
        }, 200);
        
        // Mark as logged to avoid duplicates
        (resource as any).logged = true;
      }
    });
  }, 2000);
}

/**
 * Initialize additional event listeners for various scenarios
 */
function initializeEventListeners(): void {
  console.log('🔧 Initializing additional event listeners...');
  
  // Listen for custom events that might be dispatched by the page
  window.addEventListener('airtm-data-update', (event: any) => {
    console.log('🎯 Custom airtm-data-update event detected:', event.detail);
    if (event.detail && typeof event.detail === 'string') {
      processGraphQLResponseText(event.detail);
    }
  });
  
  // Listen for storage events
  window.addEventListener('storage', (event) => {
    if (event.key && event.key.includes('airtm') && event.newValue) {
      console.log('💾 Storage event with airtm data detected:', event.key, event.newValue);
      try {
        const storageData = JSON.parse(event.newValue);
        if (storageData && storageData.availableOperations) {
          processGraphQLResponseText(event.newValue);
        }
      } catch (e) {
        // Not JSON, ignore
      }
    }
  });
  
  // Listen for postMessage events
  window.addEventListener('message', (event) => {
    if (event.data && typeof event.data === 'object') {
      if (event.data.type === 'airtm-graphql-response' || 
          (event.data.data && event.data.data.availableOperations)) {
        console.log('📬 PostMessage with GraphQL data detected:', event.data);
        processGraphQLResponseText(JSON.stringify(event.data));
      }
    }
  });
  
  // Periodically check for data in common storage locations
  setInterval(() => {
    // Check localStorage
    try {
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.includes('airtm')) {
          const value = localStorage.getItem(key);
          if (value && value.includes('availableOperations')) {
            console.log('🔍 Found airtm data in localStorage:', key);
            processGraphQLResponseText(value);
          }
        }
      }
    } catch (e) {
      // localStorage not accessible
    }
    
    // Check sessionStorage
    try {
      for (let i = 0; i < sessionStorage.length; i++) {
        const key = sessionStorage.key(i);
        if (key && key.includes('airtm')) {
          const value = sessionStorage.getItem(key);
          if (value && value.includes('availableOperations')) {
            console.log('🔍 Found airtm data in sessionStorage:', key);
            processGraphQLResponseText(value);
          }
        }
      }
    } catch (e) {
      // sessionStorage not accessible
    }
  }, 5000);
  
  console.log('✅ Additional event listeners initialized');
}

/**
 * Initialize Service Worker message listener for network interception
 */
function initializeServiceWorkerListener(): void {
  console.log('🔧 Initializing Service Worker listener...');
  
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.addEventListener('message', (event) => {
      if (event.data && event.data.type === 'network-request') {
        console.log('🔧 Service Worker network request:', event.data);
        if (event.data.url && event.data.url.includes('graphql')) {
          console.log('🎯 GraphQL request detected via Service Worker');
        }
      }
    });
    
    // Try to register a simple service worker for network monitoring
    navigator.serviceWorker.register('/service-worker.js').catch(() => {
      console.log('ℹ️ Service Worker registration failed (expected if not available)');
    });
    
    console.log('✅ Service Worker listener initialized');
  } else {
    console.warn('⚠️ Service Worker not available');
  }
}

/**
 * Initialize DevTools Network API monitoring (experimental)
 */
function initializeDevToolsMonitor(): void {
  console.log('🔧 Initializing DevTools monitor...');
  
  // Try to access Chrome DevTools APIs if available
  if ((window as any).chrome && (window as any).chrome.devtools) {
    try {
      (window as any).chrome.devtools.network.onRequestFinished.addListener((request: any) => {
        if (request.request.url.includes('graphql')) {
          console.log('🔧 DevTools detected GraphQL request:', request);
          request.getContent((content: string) => {
            if (content) {
              processGraphQLResponseText(content);
            }
          });
        }
      });
      console.log('✅ DevTools monitor initialized');
    } catch (e) {
      console.log('ℹ️ DevTools API not accessible (expected in content script)');
    }
  }
}

/**
 * Initialize aggressive polling for data detection
 */
function initializeAggressivePolling(): void {
  console.log('🔧 Initializing aggressive polling...');
  
  // Check for data in window object
  setInterval(() => {
    try {
      // Check if there's any GraphQL data in window object
      const windowKeys = Object.keys(window);
      windowKeys.forEach(key => {
        if (key.toLowerCase().includes('airtm') || key.toLowerCase().includes('graphql')) {
          const value = (window as any)[key];
          if (value && typeof value === 'object') {
            const stringified = JSON.stringify(value);
            if (stringified.includes('availableOperations')) {
              console.log('🔍 Found GraphQL data in window object:', key);
              processGraphQLResponseText(stringified);
            }
          }
        }
      });
      
      // Check for React/Vue component data
      const reactRoot = document.querySelector('[data-reactroot]');
      if (reactRoot && (reactRoot as any)._reactInternalFiber) {
        console.log('🔍 React app detected, checking for data...');
        // This is a simplified check - in practice, you'd need to traverse the React fiber tree
      }
      
      // Check for Apollo Client cache
      if ((window as any).__APOLLO_CLIENT__) {
        console.log('🔍 Apollo Client detected');
        const apolloClient = (window as any).__APOLLO_CLIENT__;
        if (apolloClient.cache && apolloClient.cache.data) {
          const cacheData = JSON.stringify(apolloClient.cache.data);
          if (cacheData.includes('availableOperations')) {
            console.log('🎯 Found availableOperations in Apollo cache');
            processGraphQLResponseText(cacheData);
          }
        }
      }
      
    } catch (e) {
      // Ignore errors in polling
    }
  }, 3000);
  
  console.log('✅ Aggressive polling initialized');
}

/**
 * Initialize all advanced monitoring techniques
 */
export function initializeAdvancedMonitoring(): void {
  console.log('🚀 Initializing advanced monitoring techniques...');
  
  initializeServiceWorkerListener();
  initializeDevToolsMonitor();
  initializeAggressivePolling();
  
  // Inject script into main world for deeper access
  injectPageScript();
  
  // Add a global function that pages can call to send data
  (window as any).airtmExtensionReceiveData = function(data: any) {
    console.log('📞 Data received via global function:', data);
    if (typeof data === 'string') {
       processGraphQLResponseText(data);
     } else {
       processGraphQLResponseText(JSON.stringify(data));
     }
  };
  
  console.log('✅ Advanced monitoring initialized');
}

/**
 * Inject a script into the page to access the main world context
 */
function injectPageScript(): void {
  console.log('🔧 Injecting page script for main world access...');
  
  const script = document.createElement('script');
  // Use external script file to avoid CSP violations
  script.src = chrome.runtime.getURL('inject-script.js');
  script.type = 'text/javascript';
  
  // Listen for events from the injected script
  window.addEventListener('airtm-graphql-detected', (event: any) => {
    console.log('📡 Received data from main world:', event.detail);
    if (event.detail && event.detail.responseText) {
      processGraphQLResponseText(event.detail.responseText);
    }
  });
  
  (document.head || document.documentElement).appendChild(script);
  script.remove();
  
  console.log('✅ Page script injected successfully');
}

/**
 * Creates an intercepted version of fetch
 */
function interceptFetch(originalFetch: typeof fetch): typeof fetch {
  return async function(input: RequestInfo | URL, init?: RequestInit): Promise<Response> {
    const url = typeof input === 'string' ? input : input instanceof URL ? input.href : input.url
    
    // Log all fetch requests for debugging
    console.log('🌐 Fetch interceptor called for URL:', url)

    // Check if this is an Airtm GraphQL request
    if (isAirtmGraphQLCall(url, init)) {
      try {
        console.log('🎯 Intercepting Airtm GraphQL request:', url)
        
        // Make the original request
        const response = await originalFetch(input, init)
        
        // Clone response for processing (original response can only be read once)
        const responseClone = response.clone()
        
        console.log('📥 GraphQL response received, status:', response.status)
        
        // Process the response asynchronously
        processGraphQLResponse(responseClone).catch(error => {
          console.error('❌ Error processing GraphQL response:', error)
        })
        
        return response
      } catch (error) {
        console.error('❌ Error in fetch interceptor:', error)
        // Return original request if interception fails
        return originalFetch(input, init)
      }
    } else {
      console.log('⏭️ Skipping non-GraphQL request:', url)
    }

    // For non-GraphQL requests, use original fetch
    return originalFetch(input, init)
  }
}

/**
 * Checks if a request is an Airtm GraphQL call
 */
function isAirtmGraphQLCall(url: string, options?: RequestInit): boolean {
  console.log('🔍 Checking URL for GraphQL interception:', url)
  
  if (!url.includes(AIRTM_GRAPHQL_ENDPOINT)) {
    console.log('❌ URL does not match Airtm GraphQL endpoint')
    return false
  }
  
  console.log('✅ URL matches Airtm GraphQL endpoint')

  // Check if it's a POST request (GraphQL typically uses POST)
  if (options?.method && options.method.toUpperCase() !== 'POST') {
    console.log('❌ Request method is not POST:', options.method)
    return false
  }
  
  console.log('✅ Request method is POST or undefined (default)')

  // Check if request body contains GraphQL query
  if (options?.body) {
    const bodyStr = typeof options.body === 'string' ? options.body : JSON.stringify(options.body)
    console.log('📝 Request body preview:', bodyStr.substring(0, 200) + '...')
    
    // Check for monitored operations with case-insensitive matching
    // This handles potential case variations from Airtm's GraphQL operations
    const matchFound = MONITORED_OPERATIONS.some(operation => {
      // First try exact match (most common case)
      if (bodyStr.includes(operation)) {
        console.log('✅ Found exact match for operation:', operation)
        return true
      }
      // Then try case-insensitive match as fallback
      if (bodyStr.toLowerCase().includes(operation.toLowerCase())) {
        console.log('✅ Found case-insensitive match for operation:', operation)
        return true
      }
      return false
    })
    
    if (!matchFound) {
      console.log('❌ No monitored operations found in request body')
      console.log('🔍 Monitored operations:', MONITORED_OPERATIONS)
    }
    
    return matchFound
  }
  
  console.log('⚠️ No request body found, defaulting to true for Airtm GraphQL endpoint')
  return true // Default to true for Airtm GraphQL endpoint
}

/**
 * Handle successful AcceptOperation response
 */
function handleAcceptOperationResponse(data: any): void {
  try {
    const acceptOperationData = data.data.acceptOperation;
    console.log('✅ Processing successful AcceptOperation:', acceptOperationData);
    
    // Check if operation was successfully accepted
    if (acceptOperationData && acceptOperationData.id) {
      console.log('🎉 Operation successfully accepted:', acceptOperationData.id);
      
      // Store acceptance timestamp and details
      const acceptanceInfo = {
        operationId: acceptOperationData.id,
        timestamp: new Date().toISOString(),
        status: 'accepted',
        data: acceptOperationData
      };
      
      // Notify extension with success
      notifyExtension('OPERATION_ACCEPTED', acceptanceInfo);
      
      // Send Telegram notification for successful acceptance
      chrome.runtime.sendMessage({
        type: 'SEND_TELEGRAM_OPERATION_UPDATE',
        operationId: acceptOperationData.id,
        status: 'accepted'
      });
      
    } else {
      console.warn('⚠️ AcceptOperation response missing operation ID');
    }
    
  } catch (error) {
    console.error('❌ Error handling AcceptOperation response:', error);
  }
}

/**
 * Handle successful UnmatchOperation response
 */
function handleUnmatchOperationResponse(data: any): void {
  try {
    const unmatchResult = data.data.unmatchOperation;
    console.log('❌ Processing successful UnmatchOperation:', unmatchResult);
    
    // Check if operation was successfully declined
    if (unmatchResult === true) {
      console.log('🚫 Operation successfully declined');
      
      // Extract operation ID from the request variables if available
      // Note: We'll need to capture this from the request context
      const declineInfo = {
        timestamp: new Date().toISOString(),
        status: 'declined',
        success: true
      };
      
      // Notify extension with success
      notifyExtension('OPERATION_DECLINED', declineInfo);
      
      // Send Telegram notification for successful decline
      notifyExtension('SEND_TELEGRAM_OPERATION_UPDATE', {
        status: 'declined',
        timestamp: new Date().toISOString()
      });
      
    } else {
      console.warn('⚠️ UnmatchOperation response unexpected result:', unmatchResult);
    }
    
  } catch (error) {
    console.error('❌ Error handling UnmatchOperation response:', error);
  }
}

/**
 * Handle UnmatchOperation error response
 */
function handleUnmatchOperationError(data: any): void {
  try {
    const errors = data.errors || [];
    console.log('❌ Processing UnmatchOperation errors:', errors);
    
    // Find UnmatchOperation specific errors
    const unmatchErrors = errors.filter((error: any) => 
      error.path && error.path.includes('unmatchOperation')
    );
    
    for (const error of unmatchErrors) {
      console.log('🔍 UnmatchOperation error details:', error);
      
      const errorInfo = {
        timestamp: new Date().toISOString(),
        status: 'decline_failed',
        error: error.message || 'Unknown decline error',
        errorCode: error.extensions?.code || 'UNKNOWN'
      };
      
      // Notify extension with error
      notifyExtension('OPERATION_DECLINE_ERROR', errorInfo);
      
      // Send Telegram notification for decline error
      notifyExtension('SEND_TELEGRAM_OPERATION_UPDATE', {
        status: 'decline_error',
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
    
  } catch (error) {
    console.error('❌ Error handling UnmatchOperation error:', error);
  }
}

/**
 * Handle AcceptOperation error response
 */
function handleAcceptOperationError(data: any): void {
  try {
    const errors = data.errors || [];
    console.log('❌ Processing AcceptOperation errors:', errors);
    
    // Find AcceptOperation specific errors
    const acceptErrors = errors.filter((error: any) => 
      error.path && error.path.includes('acceptOperation')
    );
    
    for (const error of acceptErrors) {
      console.log('🔍 AcceptOperation error details:', error);
      
      // Check for "operation no longer available" error
      const isNotAvailable = error.message && (
        error.message.toLowerCase().includes('no longer available') ||
        error.message.toLowerCase().includes('not available') ||
        error.message.toLowerCase().includes('already taken') ||
        error.message.toLowerCase().includes('expired')
      );
      
      if (isNotAvailable) {
        console.log('🚫 Operation is no longer available');
        
        // Extract operation ID if available in error context
        const operationId = extractOperationIdFromError(error);
        
        // Notify extension about unavailable operation
        notifyExtension('OPERATION_NOT_AVAILABLE', {
          error: error.message,
          operationId,
          timestamp: new Date().toISOString()
        });
        
        // Send Telegram notification for unavailable operation
        chrome.runtime.sendMessage({
          type: 'SEND_TELEGRAM_OPERATION_UPDATE',
          operationId: operationId || 'unknown',
          status: 'Not Available'
        });
        
      } else {
        // Handle other types of AcceptOperation errors
        console.log('⚠️ Other AcceptOperation error:', error.message);
        
        notifyExtension('OPERATION_ACCEPT_ERROR', {
          error: error.message,
          timestamp: new Date().toISOString(),
          errorType: 'accept_failed'
        });
      }
    }
    
  } catch (error) {
    console.error('❌ Error handling AcceptOperation error:', error);
  }
}

/**
 * Extract operation ID from error context
 */
function extractOperationIdFromError(error: any): string | null {
  try {
    // Try to extract from error path or extensions
    if (error.path && Array.isArray(error.path)) {
      // Look for numeric values in path that could be operation ID
      for (const pathItem of error.path) {
        if (typeof pathItem === 'string' && /^\d+$/.test(pathItem)) {
          return pathItem;
        }
      }
    }
    
    // Try to extract from error message using regex
    if (error.message) {
      const idMatch = error.message.match(/operation[\s\w]*?(\d+)/i);
      if (idMatch && idMatch[1]) {
        return idMatch[1];
      }
    }
    
    return null;
  } catch (e) {
    console.error('Error extracting operation ID from error:', e);
    return null;
  }
}

/**
 * Processes GraphQL response and extracts offer data
 */
/**
 * Process GraphQL response text directly with extension context validation
 */
function processGraphQLResponseText(responseText: string): void {
  try {
    // Check extension context before processing
    if (!isExtensionContextValid()) {
      console.warn('⚠️ Extension context invalidated, skipping GraphQL response processing');
      return;
    }

    console.log('🔄 Processing GraphQL response text...');
    console.log('📄 Response text length:', responseText.length);
    console.log('📄 Response preview:', responseText.substring(0, 500) + '...');

    // Create cache key to prevent duplicate processing
    const cacheKey = createCacheKey(responseText);
    const now = Date.now();

    if (responseCache.has(cacheKey)) {
      const lastProcessed = responseCache.get(cacheKey)!;
      if (now - lastProcessed < CACHE_DURATION) {
        console.log('⏭️ Skipping duplicate GraphQL response processing');
        return;
      }
    }
    
    responseCache.set(cacheKey, now);
    
    // Clean old cache entries
    cleanCache();
    
    let data: any;
    try {
      data = JSON.parse(responseText);
      console.log('✅ JSON parsing successful');
      console.log('📊 Parsed data keys:', Object.keys(data));
    } catch (parseError) {
      console.error('❌ Failed to parse GraphQL response as JSON:', parseError);
      return;
    }
    
    // Check for GraphQL errors
    if (data.errors && Array.isArray(data.errors)) {
      console.warn('⚠️ GraphQL response contains errors:', data.errors);
    }
    
    // Handle availableOperations
    if (data.data && data.data.availableOperations) {
      console.log('🎯 availableOperations detected, length:', data.data.availableOperations.length);
      handleAvailableOperations(data.data);
    } else {
      console.log('ℹ️ No availableOperations found in response');
    }
    
    // Handle other operations
    if (data.data && data.data.getBalance) {
      console.log('💰 getBalance operation detected');
      notifyExtension('BALANCE_UPDATE', data.data.getBalance);
    }
    
    if (data.data && data.data.createOperation) {
      console.log('🆕 createOperation detected');
      notifyExtension('OPERATION_CREATED', data.data.createOperation);
    }
    
    if (data.data && data.data.acceptOperation) {
      console.log('✅ acceptOperation detected');
      handleAcceptOperationResponse(data);
    } else if (data.errors && data.errors.some((error: any) => 
      error.path && error.path.includes('acceptOperation'))) {
      console.log('❌ acceptOperation error detected');
      handleAcceptOperationError(data);
    }
    
    // Handle unmatch operation (decline)
    if (data.data && data.data.unmatchOperation !== undefined) {
      console.log('❌ unmatchOperation detected');
      handleUnmatchOperationResponse(data);
    } else if (data.errors && data.errors.some((error: any) => 
      error.path && error.path.includes('unmatchOperation'))) {
      console.log('❌ unmatchOperation error detected');
      handleUnmatchOperationError(data);
    }
    
    // Handle operation details (post-acceptance tracking)
    if (data.data && data.data.operation) {
      console.log('📋 OperationDetails detected');
      handleOperationDetails(data.data.operation);
    }
    
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);

    // Handle extension context invalidation gracefully
    if (errorMessage.includes('Extension context invalidated') ||
        errorMessage.includes('context invalidated')) {
      console.warn('⚠️ Extension context invalidated during GraphQL response processing');
      return;
    }

    console.error('❌ Error processing GraphQL response text:', error);
  }
}

/**
 * Check if extension context is still valid
 */
function isExtensionContextValid(): boolean {
  try {
    return !!(chrome?.runtime?.id);
  } catch (error) {
    return false;
  }
}

/**
 * Process GraphQL response from Response object
 */
async function processGraphQLResponse(response: Response): Promise<void> {
  try {
    const responseText = await response.text()
    processGraphQLResponseText(responseText)
  } catch (error) {
    console.error('❌ Error processing GraphQL response:', error)
  }
}

/**
 * Handles AvailableOperations response
 * Processes both empty and populated responses during Airtm's polling cycle
 */
async function handleOperationDetails(operation: any): Promise<void> {
  try {
    console.log('🔧 Handling OperationDetails with status tracking');
    console.log('📊 Operation data:', JSON.stringify(operation, null, 2).substring(0, 1000) + '...');
    
    const operationData = {
      id: operation.id,
      hash: operation.hash,
      status: operation.status,
      operationType: operation.operationType,
      grossAmount: operation.grossAmount,
      netAmount: operation.netAmount,
      airtmFee: operation.airtmFee,
      takerCommission: operation.takerCommission,
      createdAt: operation.createdAt,
      updatedAt: operation.updatedAt,
      previousStatus: operation.previousStatus,
      timeToLive: operation.timeToLive,
      makerPaymentMethod: operation.makerPaymentMethod,
      takerPaymentMethod: operation.takerPaymentMethod
    };
    
    console.log(`📈 OperationDetails processed: ${operationData.id} - Status: ${operationData.status}`);
    
    // Notify extension with operation details
    notifyExtension('OPERATION_DETAILS_UPDATE', {
      operation: operationData,
      timestamp: new Date().toISOString(),
      source: 'graphql-interception'
    });
    
    // Store operation details for tracking
    await chrome.storage.local.set({
      [`operation_${operationData.id}`]: operationData,
      'lastOperationUpdate': new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Error handling OperationDetails:', error);
  }
}

async function handleAvailableOperations(data: any, _requestInit?: RequestInit): Promise<void> {
  try {
    // Check extension context before processing
    if (!isExtensionContextValid()) {
      console.warn('⚠️ Extension context invalidated, skipping AvailableOperations handling');
      return;
    }

    console.log('🔧 Handling AvailableOperations query format with comprehensive validation')
    console.log('📊 Raw data structure:', JSON.stringify(data, null, 2).substring(0, 1000) + '...')

    const offers = parseAvailableOperations(data)
    
    // Log all responses, including empty ones, for better debugging
    console.log(`📈 AvailableOperations GraphQL query processed: ${offers.length} offers found`)
    
    if (offers.length === 0) {
      console.log('📭 No offers found - notifying extension with empty array')
      // Still notify extension of empty response to maintain state consistency
      notifyExtension('OFFERS_UPDATE', {
        offers: [],
        timestamp: new Date().toISOString(),
        source: 'graphql-interception'
      })
      return
    }

    console.log(`✅ Successfully parsed ${offers.length} offers from GraphQL response`)
    console.log('🔍 First offer preview:', JSON.stringify(offers[0], null, 2).substring(0, 500) + '...')

    // Store offers in Chrome storage
    await storeOffers(offers)

    // Extract bearer token if available
    const token = extractBearerToken()
    if (token) {
      console.log('🔑 Bearer token extracted and storing...')
      await storeBearerToken(token)
    } else {
      console.log('⚠️ No bearer token found')
    }

    // Notify extension components
    console.log('📢 Notifying extension components of offers update')
    notifyExtension('OFFERS_UPDATE', {
      offers,
      timestamp: new Date().toISOString(),
      source: 'graphql-interception'
    })

    // Dispatch custom event for content script listeners
    console.log('📡 Dispatching custom event for content script listeners')
    window.dispatchEvent(new CustomEvent('airtm-offers-detected', {
      detail: { offers, timestamp: new Date().toISOString() }
    }))

    console.log('🎉 AvailableOperations handling completed successfully')

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);

    // Handle extension context invalidation gracefully
    if (errorMessage.includes('Extension context invalidated') ||
        errorMessage.includes('context invalidated')) {
      console.warn('⚠️ Extension context invalidated during AvailableOperations handling');
      return;
    }

    console.error('❌ Error handling AvailableOperations:', error)
    console.error('📊 Error details:', error instanceof Error ? error.stack : error)

    // Notify extension of parsing error (only if extension context is valid)
    if (isExtensionContextValid()) {
      notifyExtension('PARSING_ERROR', {
        error: error instanceof Error ? error.message : 'Unknown parsing error',
        timestamp: new Date().toISOString()
      })
    }
  }
}

/**
 * Stores offers in Chrome storage with extension context validation
 */
async function storeOffers(offers: AirtmOperation[]): Promise<void> {
  try {
    // Check if Chrome APIs are available and extension context is valid
    if (typeof chrome !== 'undefined' && chrome.storage && chrome.runtime && chrome.runtime.id) {
      // Test extension context before attempting storage operation
      try {
        await chrome.storage.local.set({
          airtm_offers: offers,
          airtm_offers_timestamp: new Date().toISOString(),
          airtm_offers_count: offers.length
        })
        console.log(`✅ Stored ${offers.length} offers in Chrome storage`)
        return
      } catch (storageError) {
        // Check if this is an extension context invalidation error
        const errorMessage = storageError instanceof Error ? storageError.message : String(storageError)
        if (errorMessage.includes('Extension context invalidated') || 
            errorMessage.includes('context invalidated') ||
            errorMessage.includes('receiving end does not exist')) {
          console.warn('⚠️ Extension context invalidated, falling back to localStorage')
          // Fall through to localStorage fallback
        } else {
          throw storageError // Re-throw other storage errors
        }
      }
    }
    
    // Fallback to localStorage (for testing or when extension context is invalid)
    try {
      localStorage.setItem('airtm_offers', JSON.stringify(offers))
      localStorage.setItem('airtm_offers_timestamp', new Date().toISOString())
      localStorage.setItem('airtm_offers_count', offers.length.toString())
      console.log(`📦 Stored ${offers.length} offers in localStorage (fallback)`)
    } catch (localStorageError) {
      console.error('❌ Failed to store in localStorage:', localStorageError)
      // If both Chrome storage and localStorage fail, we can't persist the data
      throw new Error('Unable to store offers: Both Chrome storage and localStorage failed')
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    console.error('❌ Error storing offers:', errorMessage)
    
    // Don't throw the error to prevent breaking the fetch interceptor
    // Just log it and continue - the offers will still be processed in memory
  }
}

/**
 * Stores bearer token for API requests with extension context validation
 */
async function storeBearerToken(token: string): Promise<void> {
  try {
    // Check if Chrome APIs are available and extension context is valid
    if (typeof chrome !== 'undefined' && chrome.storage && chrome.runtime && chrome.runtime.id) {
      try {
        await chrome.storage.local.set({
          airtm_bearer_token: token,
          airtm_token_timestamp: new Date().toISOString()
        })
        console.log('✅ Bearer token stored successfully in Chrome storage')
        return
      } catch (storageError) {
        // Check if this is an extension context invalidation error
        const errorMessage = storageError instanceof Error ? storageError.message : String(storageError)
        if (errorMessage.includes('Extension context invalidated') || 
            errorMessage.includes('context invalidated') ||
            errorMessage.includes('receiving end does not exist')) {
          console.warn('⚠️ Extension context invalidated, falling back to localStorage for bearer token')
          // Fall through to localStorage fallback
        } else {
          throw storageError // Re-throw other storage errors
        }
      }
    }
    
    // Fallback to localStorage when extension context is invalid
    try {
      localStorage.setItem('airtm_bearer_token', token)
      localStorage.setItem('airtm_token_timestamp', new Date().toISOString())
      console.log('📦 Bearer token stored in localStorage (fallback)')
    } catch (localStorageError) {
      console.error('❌ Failed to store bearer token in localStorage:', localStorageError)
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    console.error('❌ Error storing bearer token:', errorMessage)
    // Don't throw the error to prevent breaking the authentication flow
  }
}

/**
 * Notifies extension components of events with extension context validation
 */
function notifyExtension(type: string, data: any): void {
  try {
    // Check if Chrome APIs are available and extension context is valid
    if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id) {
      chrome.runtime.sendMessage({
        type,
        data,
        metadata: {
          timestamp: new Date().toISOString(),
          source: 'fetch-interceptor'
        }
      }).catch(error => {
        const errorMessage = error instanceof Error ? error.message : String(error)
        if (errorMessage.includes('Extension context invalidated') || 
            errorMessage.includes('context invalidated') ||
            errorMessage.includes('receiving end does not exist')) {
          console.warn(`⚠️ Extension context invalidated while sending message: ${type}`)
          console.log('💡 Extension may have been reloaded or disabled. Data will be stored locally.')
        } else {
          console.log(`❌ Failed to send message to extension (${type}):`, errorMessage)
        }
      })
    } else {
      console.log(`📭 Chrome extension APIs not available for message: ${type}`)
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    console.log(`❌ Could not send message to extension (${type}):`, errorMessage)
  }
}

/**
 * Creates a cache key for response deduplication
 * Uses timestamp-based approach for AvailableOperations to handle polling
 */
function createCacheKey(responseText: string): string {
  try {
    const parsedData = JSON.parse(responseText)
    
    // For AvailableOperations, use operation count + first few operation IDs as cache key
    // This allows capturing updates when offers change during polling
    if (parsedData.data?.availableOperations && Array.isArray(parsedData.data.availableOperations)) {
      const operations = parsedData.data.availableOperations
      const operationCount = operations.length
      
      // Safely extract IDs with fallback for missing IDs
      const firstThreeIds = operations
        .slice(0, 3)
        .map((op: any, index: number) => op?.id || op?.hash || `op${index}`)
        .join('-')
      
      return `availableOps-${operationCount}-${firstThreeIds}`
    }
  } catch (error) {
    // Fallback to hash-based approach for non-JSON or other responses
    console.debug('Cache key generation fallback due to:', error)
  }
  
  // Create a simple hash of the response for other operations
  let hash = 0
  for (let i = 0; i < responseText.length; i++) {
    const char = responseText.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // Convert to 32-bit integer
  }
  return hash.toString()
}

/**
 * Cleans old entries from response cache
 */
function cleanCache(): void {
  const now = Date.now()
  const keysToDelete: string[] = []
  
  responseCache.forEach((timestamp, key) => {
    if (now - timestamp > CACHE_DURATION * 2) {
      keysToDelete.push(key)
    }
  })
  
  keysToDelete.forEach(key => responseCache.delete(key))
}

/**
 * Gets stored offers from Chrome storage
 */
export async function getStoredOffers(): Promise<AirtmOperation[]> {
  try {
    // Try Chrome storage first if extension context is valid
    if (typeof chrome !== 'undefined' && chrome.storage && chrome.runtime && chrome.runtime.id) {
      try {
        const result = await chrome.storage.local.get('airtm_offers')
        return result.airtm_offers || []
      } catch (storageError) {
        // Check if this is an extension context invalidation error
        const errorMessage = storageError instanceof Error ? storageError.message : String(storageError)
        if (errorMessage.includes('Extension context invalidated') || 
            errorMessage.includes('context invalidated') ||
            errorMessage.includes('receiving end does not exist')) {
          console.warn('⚠️ Extension context invalidated, falling back to localStorage for offers retrieval')
          // Fall through to localStorage fallback
        } else {
          throw storageError // Re-throw other storage errors
        }
      }
    }
    
    // Fallback to localStorage
    try {
      const stored = localStorage.getItem('airtm_offers')
      return stored ? JSON.parse(stored) : []
    } catch (localStorageError) {
      console.error('❌ Failed to retrieve offers from localStorage:', localStorageError)
      return []
    }
  } catch (error) {
    console.error('❌ Error retrieving stored offers:', error)
    return []
  }
}

/**
 * Gets stored bearer token
 */
export async function getStoredBearerToken(): Promise<string | null> {
  try {
    // Try Chrome storage first if extension context is valid
    if (typeof chrome !== 'undefined' && chrome.storage && chrome.runtime && chrome.runtime.id) {
      try {
        const result = await chrome.storage.local.get('airtm_bearer_token')
        return result.airtm_bearer_token || null
      } catch (storageError) {
        // Check if this is an extension context invalidation error
        const errorMessage = storageError instanceof Error ? storageError.message : String(storageError)
        if (errorMessage.includes('Extension context invalidated') || 
            errorMessage.includes('context invalidated') ||
            errorMessage.includes('receiving end does not exist')) {
          console.warn('⚠️ Extension context invalidated, falling back to localStorage for bearer token retrieval')
          // Fall through to localStorage fallback
        } else {
          throw storageError // Re-throw other storage errors
        }
      }
    }
    
    // Fallback to localStorage
    try {
      return localStorage.getItem('airtm_bearer_token') || null
    } catch (localStorageError) {
      console.error('❌ Failed to retrieve bearer token from localStorage:', localStorageError)
      return null
    }
  } catch (error) {
    console.error('❌ Error retrieving bearer token:', error)
    return null
  }
}

/**
 * Update duplicate detection configuration
 */
export function updateDuplicateDetectionConfig(config: any): void {
  console.log('🔧 Updating duplicate detection config:', config)
  // This function can be used to update duplicate detection settings
  // For now, it's a placeholder that logs the configuration
  // Future implementation could store the config and use it for filtering
}
