import React, { useState } from 'react';
import { 
  Settings, 
  Play, 
  Pause, 
  Bell, 
  BellOff, 
  Volume2, 
  VolumeX, 
  Plus, 
  X, 
  Check, 
  Trash2,
  Shield,
  Activity,
  Filter,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import type { ExtensionSettings, Offer } from '../types';

interface ControlPanelProps {
  settings: ExtensionSettings;
  onSettingsUpdate: (settings: ExtensionSettings) => void;
  selectedOffer: Offer | null;
  onAcceptOffer: (offerId: string) => void;
  onRejectOffer: (offerId: string) => void;
}

const ControlPanel: React.FC<ControlPanelProps> = ({
  settings,
  onSettingsUpdate,
  selectedOffer,
  onAcceptOffer,
  onRejectOffer
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [newKeyword, setNewKeyword] = useState('');
  const [showKeywordInput, setShowKeywordInput] = useState(false);
  const [keywordGroup, setKeywordGroup] = useState<string[]>([]);
  const [isGrouping, setIsGrouping] = useState(false);

  // Toggle functions
  const toggleMonitoring = () => {
    onSettingsUpdate({ ...settings, monitoring: !settings.monitoring });
  };

  const toggleAutoAccept = () => {
    onSettingsUpdate({ ...settings, autoAccept: !settings.autoAccept });
  };

  const toggleNotifications = () => {
    onSettingsUpdate({ ...settings, notificationsEnabled: !settings.notificationsEnabled });
  };

  const toggleSound = () => {
    onSettingsUpdate({ ...settings, soundEnabled: !settings.soundEnabled });
  };

  // Keyword management
  const handleKeywordAdd = () => {
    if (newKeyword.trim() && !settings.blacklistKeywords.includes(newKeyword.trim())) {
      if (isGrouping) {
        // Add to temporary group
        if (!keywordGroup.includes(newKeyword.trim())) {
          setKeywordGroup([...keywordGroup, newKeyword.trim()]);
        }
        setNewKeyword('');
      } else {
        // Add directly to blacklist
        onSettingsUpdate({
          ...settings,
          blacklistKeywords: [...settings.blacklistKeywords, newKeyword.trim()]
        });
        setNewKeyword('');
        setShowKeywordInput(false);
      }
    }
  };

  const finishGrouping = () => {
    if (keywordGroup.length > 0) {
      // Add all grouped keywords to blacklist
      const newKeywords = keywordGroup.filter(keyword => !settings.blacklistKeywords.includes(keyword));
      onSettingsUpdate({
        ...settings,
        blacklistKeywords: [...settings.blacklistKeywords, ...newKeywords]
      });
      setKeywordGroup([]);
      setIsGrouping(false);
      setNewKeyword('');
      setShowKeywordInput(false);
    }
  };

  const cancelGrouping = () => {
    setKeywordGroup([]);
    setIsGrouping(false);
    setNewKeyword('');
    setShowKeywordInput(false);
  };

  const removeFromGroup = (keyword: string) => {
    setKeywordGroup(keywordGroup.filter(k => k !== keyword));
  };

  const handleKeywordRemove = (keyword: string) => {
    onSettingsUpdate({
      ...settings,
      blacklistKeywords: settings.blacklistKeywords.filter(k => k !== keyword)
    });
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      if (isGrouping) {
        if (newKeyword.trim()) {
          handleKeywordAdd();
        } else {
          // Empty enter finishes grouping
          finishGrouping();
        }
      } else {
        if (newKeyword.trim()) {
          // First enter starts grouping mode
          setIsGrouping(true);
          handleKeywordAdd();
        }
      }
    } else if (e.key === 'Escape') {
      if (isGrouping) {
        cancelGrouping();
      } else {
        setNewKeyword('');
        setShowKeywordInput(false);
      }
    }
  };

  // ControlPanel component rendering

  return (
    <div className="control-panel animate-slide-up">
      {/* Main Controls */}
      <div className="control-section">
        {/* Selected Offer Actions */}
        {selectedOffer && (
          <div className="mb-3 p-3 glass-effect border border-emerald-200/60 rounded-xl animate-scale-in">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-2">
                <div className="w-6 h-6 gradient-secondary rounded-lg flex items-center justify-center shadow-sm">
                  <Check className="w-3 h-3 text-white" />
                </div>
                <div>
                  <span className="text-xs font-bold text-slate-800">Selected Offer</span>
                  <div className="text-xs text-slate-600 font-medium">
                    {selectedOffer.currency?.symbol || selectedOffer.walletCurrency?.symbol || 'N/A'} • {selectedOffer.operationType}
                  </div>
                </div>
              </div>
              <div className="badge badge-success text-xs">
                Active
              </div>
            </div>

            <div className="control-grid">
              <button
                onClick={() => onAcceptOffer(selectedOffer.hash)}
                className="btn btn-success hover-lift text-sm py-2"
              >
                <Check className="w-4 h-4" />
                <span>Accept</span>
              </button>
              <button
                onClick={() => onRejectOffer(selectedOffer.hash)}
                className="btn btn-danger hover-lift text-sm py-2"
              >
                <X className="w-4 h-4" />
                <span>Reject</span>
              </button>
            </div>
          </div>
        )}

        {/* Quick Controls */}
        <div className="control-grid mb-4">
          {/* Monitoring Toggle */}
          <button
            onClick={toggleMonitoring}
            className={`control-button hover-lift py-3 ${
              settings.monitoring ? 'active' : 'inactive'
            }`}
          >
            <div className="flex items-center space-x-2">
              {settings.monitoring ? (
                <Pause className="w-4 h-4" />
              ) : (
                <Play className="w-4 h-4" />
              )}
              <span className="font-semibold text-sm">
                {settings.monitoring ? 'Pause' : 'Start'}
              </span>
            </div>
            {settings.monitoring && (
              <div className="status-online animate-pulse" />
            )}
          </button>

          {/* Auto Accept Toggle */}
          <button
            onClick={toggleAutoAccept}
            className={`control-button hover-lift py-3 ${
              settings.autoAccept ? 'active' : 'inactive'
            }`}
          >
            <div className="flex items-center space-x-2">
              <Shield className={`w-4 h-4 ${settings.autoAccept ? 'animate-pulse' : ''}`} />
              <span className="font-semibold text-sm">Auto Accept</span>
            </div>
            {settings.autoAccept && (
              <div className="status-warning animate-pulse" />
            )}
          </button>
        </div>

      </div>

      {/* Settings Section */}
      <div className="control-section">
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="w-full btn btn-secondary hover-lift py-2"
        >
          <div className="flex items-center space-x-2">
            <div className="w-6 h-6 gradient-primary rounded-lg flex items-center justify-center shadow-sm">
              <Settings className="w-3 h-3 text-white" />
            </div>
            <span className="font-semibold text-sm">Advanced Settings</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="badge badge-primary text-xs">
              {settings.blacklistKeywords.length} filters
            </div>
            {isExpanded ? (
              <ChevronUp className="w-4 h-4" />
            ) : (
              <ChevronDown className="w-4 h-4" />
            )}
          </div>
        </button>
      </div>

      {/* Expanded Settings */}
      {isExpanded && (
        <div className="control-section animate-slide-down max-h-64 overflow-y-auto scrollbar-thin">
          {/* Notification Settings */}
          <div className="space-y-3">
            <div className="flex items-center space-x-2 mb-3">
              <div className="w-6 h-6 bg-amber-500 rounded-lg flex items-center justify-center shadow-sm">
                <Bell className="w-3 h-3 text-white" />
              </div>
              <h3 className="text-sm font-bold text-slate-800">Notifications</h3>
            </div>

            <div className="control-grid">
              {/* Notifications Toggle */}
              <button
                onClick={toggleNotifications}
                className={`control-button hover-lift py-2 ${
                  settings.notificationsEnabled ? 'active' : 'inactive'
                }`}
              >
                <div className="flex items-center space-x-2">
                  {settings.notificationsEnabled ? (
                    <Bell className="w-4 h-4" />
                  ) : (
                    <BellOff className="w-4 h-4" />
                  )}
                  <span className="font-semibold text-sm">Alerts</span>
                </div>
                {settings.notificationsEnabled && (
                  <div className="status-warning animate-pulse" />
                )}
              </button>

              {/* Sound Toggle */}
              <button
                onClick={toggleSound}
                className={`control-button hover-lift py-2 ${
                  settings.soundEnabled ? 'active' : 'inactive'
                }`}
              >
                <div className="flex items-center space-x-2">
                  {settings.soundEnabled ? (
                    <Volume2 className="w-4 h-4" />
                  ) : (
                    <VolumeX className="w-4 h-4" />
                  )}
                  <span className="font-semibold text-sm">Sound</span>
                </div>
                {settings.soundEnabled && (
                  <div className="status-warning animate-pulse" />
                )}
              </button>
            </div>
          </div>

          {/* Blacklist Keywords */}
          <div className="space-y-3 mt-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className="w-6 h-6 bg-red-500 rounded-lg flex items-center justify-center shadow-sm">
                  <Filter className="w-3 h-3 text-white" />
                </div>
                <div>
                  <h3 className="text-sm font-bold text-slate-800">Blacklist</h3>
                  <p className="text-xs text-slate-600">{settings.blacklistKeywords.length} keywords</p>
                </div>
              </div>
              <button
                onClick={() => setShowKeywordInput(!showKeywordInput)}
                className="btn btn-primary hover-lift text-sm py-1 px-3"
              >
                <Plus className="w-3 h-3" />
                <span>Add</span>
              </button>
            </div>

            {/* Add Keyword Input */}
            {showKeywordInput && (
              <div className="space-y-4 animate-slide-down">
                <div className="flex space-x-3">
                  <input
                    type="text"
                    value={newKeyword}
                    onChange={(e) => setNewKeyword(e.target.value)}
                    onKeyDown={handleKeyPress}
                    placeholder={isGrouping ? "Add another keyword or press Enter to finish..." : "Enter keyword..."}
                    className={`input-glass flex-1 ${
                      isGrouping
                        ? 'border-amber-400 focus:border-amber-500'
                        : 'border-slate-200 focus:border-primary-500'
                    }`}
                    autoFocus
                  />
                  <button
                    onClick={handleKeywordAdd}
                    disabled={!newKeyword.trim()}
                    className="btn btn-success hover-lift disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <Check className="w-4 h-4" />
                  </button>
                  {isGrouping ? (
                    <button
                      onClick={finishGrouping}
                      className="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2.5 rounded-xl transition-colors duration-200 shadow-sm"
                      title="Finish grouping"
                    >
                      <Plus className="w-4 h-4" />
                    </button>
                  ) : null}
                  <button
                    onClick={() => {
                      if (isGrouping) {
                        cancelGrouping();
                      } else {
                        setNewKeyword('');
                        setShowKeywordInput(false);
                      }
                    }}
                    className="bg-slate-200 hover:bg-slate-300 text-slate-600 px-4 py-2.5 rounded-xl transition-colors duration-200 border border-slate-200 shadow-sm"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>

                {/* Grouping Status */}
                {isGrouping && (
                  <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-3">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-yellow-800">Grouping Mode Active</span>
                      <span className="text-xs text-yellow-600">{keywordGroup.length} keyword(s) in group</span>
                    </div>
                    <p className="text-xs text-yellow-700 mb-3">
                      Type keywords and press Enter to add them. Press Enter on empty input to finish grouping.
                    </p>
                    
                    {/* Temporary Group Preview */}
                    {keywordGroup.length > 0 && (
                      <div className="space-y-1">
                        <span className="text-xs text-yellow-800 font-medium">Keywords in group:</span>
                        <div className="flex flex-wrap gap-1">
                          {keywordGroup.map((keyword, index) => (
                            <div
                              key={index}
                              className="flex items-center space-x-1 bg-yellow-200 rounded-lg px-2 py-1 text-xs text-yellow-800"
                            >
                              <span>{keyword}</span>
                              <button
                                onClick={() => removeFromGroup(keyword)}
                                className="text-yellow-600 hover:text-yellow-800 transition-colors duration-200"
                              >
                                <X className="w-3 h-3" />
                              </button>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}

            {/* Keywords List */}
            {settings.blacklistKeywords.length > 0 && (
              <div className="max-h-32 overflow-y-auto space-y-2 custom-scrollbar-modern">
                {settings.blacklistKeywords.map((keyword, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between bg-white rounded-lg px-3 py-2 border border-slate-200 shadow-sm"
                  >
                    <span className="text-sm text-slate-700">{keyword}</span>
                    <button
                      onClick={() => handleKeywordRemove(keyword)}
                      className="text-red-500 hover:text-red-600 transition-colors duration-200 p-1 rounded hover:bg-red-50"
                    >
                      <Trash2 className="w-3 h-3" />
                    </button>
                  </div>
                ))}
              </div>
            )}

            {settings.blacklistKeywords.length === 0 && (
              <div className="text-center py-4 text-slate-500 text-sm">
                No keywords added
              </div>
            )}
          </div>

          {/* Fuzzy Matching Settings */}
          <div className="space-y-3">
            <h3 className="text-sm font-semibold text-slate-700 flex items-center space-x-2">
              <Filter className="w-4 h-4" />
              <span>Smart Matching</span>
            </h3>

            {/* Enable Fuzzy Matching */}
            <div className="flex items-center justify-between">
              <span className="text-sm text-slate-600">Enable fuzzy matching</span>
              <button
                onClick={() => onSettingsUpdate({
                  ...settings,
                  fuzzyMatching: {
                    ...settings.fuzzyMatching,
                    enabled: !settings.fuzzyMatching.enabled
                  }
                })}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200 ${
                  settings.fuzzyMatching.enabled ? 'bg-emerald-500' : 'bg-slate-300'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200 shadow-sm ${
                    settings.fuzzyMatching.enabled ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>
            
            {/* Fuzzy Matching Options */}
            {settings.fuzzyMatching.enabled && (
              <div className="space-y-3 bg-white rounded-xl p-4 border border-slate-200 shadow-sm">
                {/* Threshold Slider */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-slate-600">Match Threshold</span>
                    <span className="text-xs text-emerald-600 font-mono bg-emerald-50 px-2 py-1 rounded">{settings.fuzzyMatching.threshold}</span>
                  </div>
                  <input
                    type="range"
                    min="0.1"
                    max="1.0"
                    step="0.1"
                    value={settings.fuzzyMatching.threshold}
                    onChange={(e) => onSettingsUpdate({
                      ...settings,
                      fuzzyMatching: {
                        ...settings.fuzzyMatching,
                        threshold: parseFloat(e.target.value)
                      }
                    })}
                    className="w-full h-2 bg-slate-200 rounded-lg appearance-none cursor-pointer slider"
                  />
                  <div className="flex justify-between text-xs text-slate-500">
                    <span>Strict</span>
                    <span>Loose</span>
                  </div>
                </div>
                
                {/* Enable Aliases */}
                <div className="flex items-center justify-between">
                  <span className="text-sm text-slate-600">Use payment aliases</span>
                  <button
                    onClick={() => onSettingsUpdate({
                      ...settings,
                      fuzzyMatching: {
                        ...settings.fuzzyMatching,
                        enableAliases: !settings.fuzzyMatching.enableAliases
                      }
                    })}
                    className={`relative inline-flex h-5 w-9 items-center rounded-full transition-colors duration-200 ${
                      settings.fuzzyMatching.enableAliases ? 'bg-emerald-500' : 'bg-slate-300'
                    }`}
                  >
                    <span
                      className={`inline-block h-3 w-3 transform rounded-full bg-white transition-transform duration-200 shadow-sm ${
                        settings.fuzzyMatching.enableAliases ? 'translate-x-5' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>

                <div className="text-xs text-blue-700 bg-blue-50 border border-blue-200 rounded-lg p-2">
                  <span className="text-blue-800 font-medium">💡 Tip:</span> Fuzzy matching helps catch AirTM payment methods with slight variations in naming.
                </div>
              </div>
            )}
          </div>

          {/* Status Info */}
          <div className="pt-4 border-t border-slate-200/60">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 gradient-primary rounded-xl flex items-center justify-center shadow-sm">
                  <Activity className="w-4 h-4 text-white" />
                </div>
                <div>
                  <span className="text-sm font-bold text-slate-800">
                    Status: {settings.monitoring ? 'Active' : 'Paused'}
                  </span>
                  <div className="text-xs text-slate-600">
                    {settings.monitoring ? 'Real-time monitoring enabled' : 'Monitoring is stopped'}
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <div className={`status-dot ${
                  settings.monitoring ? 'status-online' : 'status-offline'
                } animate-pulse`} />
                <span className="text-sm font-semibold text-slate-700">
                  {settings.monitoring ? 'Monitoring' : 'Stopped'}
                </span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ControlPanel;
