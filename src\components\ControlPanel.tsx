import React, { useState } from 'react';
import { 
  Settings, 
  Play, 
  Pause, 
  Bell, 
  BellOff, 
  Volume2, 
  VolumeX, 
  Plus, 
  X, 
  Check, 
  Trash2,
  Shield,
  Activity,
  Filter,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import type { ExtensionSettings, Offer } from '../types';

interface ControlPanelProps {
  settings: ExtensionSettings;
  onSettingsUpdate: (settings: ExtensionSettings) => void;
  selectedOffer: Offer | null;
  onAcceptOffer: (offerId: string) => void;
  onRejectOffer: (offerId: string) => void;
}

const ControlPanel: React.FC<ControlPanelProps> = ({
  settings,
  onSettingsUpdate,
  selectedOffer,
  onAcceptOffer,
  onRejectOffer
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [newKeyword, setNewKeyword] = useState('');
  const [showKeywordInput, setShowKeywordInput] = useState(false);
  const [keywordGroup, setKeywordGroup] = useState<string[]>([]);
  const [isGrouping, setIsGrouping] = useState(false);

  // Toggle functions
  const toggleMonitoring = () => {
    onSettingsUpdate({ ...settings, monitoring: !settings.monitoring });
  };

  const toggleAutoAccept = () => {
    onSettingsUpdate({ ...settings, autoAccept: !settings.autoAccept });
  };

  const toggleNotifications = () => {
    onSettingsUpdate({ ...settings, notificationsEnabled: !settings.notificationsEnabled });
  };

  const toggleSound = () => {
    onSettingsUpdate({ ...settings, soundEnabled: !settings.soundEnabled });
  };

  // Keyword management
  const handleKeywordAdd = () => {
    if (newKeyword.trim() && !settings.blacklistKeywords.includes(newKeyword.trim())) {
      if (isGrouping) {
        // Add to temporary group
        if (!keywordGroup.includes(newKeyword.trim())) {
          setKeywordGroup([...keywordGroup, newKeyword.trim()]);
        }
        setNewKeyword('');
      } else {
        // Add directly to blacklist
        onSettingsUpdate({
          ...settings,
          blacklistKeywords: [...settings.blacklistKeywords, newKeyword.trim()]
        });
        setNewKeyword('');
        setShowKeywordInput(false);
      }
    }
  };

  const finishGrouping = () => {
    if (keywordGroup.length > 0) {
      // Add all grouped keywords to blacklist
      const newKeywords = keywordGroup.filter(keyword => !settings.blacklistKeywords.includes(keyword));
      onSettingsUpdate({
        ...settings,
        blacklistKeywords: [...settings.blacklistKeywords, ...newKeywords]
      });
      setKeywordGroup([]);
      setIsGrouping(false);
      setNewKeyword('');
      setShowKeywordInput(false);
    }
  };

  const cancelGrouping = () => {
    setKeywordGroup([]);
    setIsGrouping(false);
    setNewKeyword('');
    setShowKeywordInput(false);
  };

  const removeFromGroup = (keyword: string) => {
    setKeywordGroup(keywordGroup.filter(k => k !== keyword));
  };

  const handleKeywordRemove = (keyword: string) => {
    onSettingsUpdate({
      ...settings,
      blacklistKeywords: settings.blacklistKeywords.filter(k => k !== keyword)
    });
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      if (isGrouping) {
        if (newKeyword.trim()) {
          handleKeywordAdd();
        } else {
          // Empty enter finishes grouping
          finishGrouping();
        }
      } else {
        if (newKeyword.trim()) {
          // First enter starts grouping mode
          setIsGrouping(true);
          handleKeywordAdd();
        }
      }
    } else if (e.key === 'Escape') {
      if (isGrouping) {
        cancelGrouping();
      } else {
        setNewKeyword('');
        setShowKeywordInput(false);
      }
    }
  };

  // ControlPanel component rendering

  return (
    <div className="bg-white/90 backdrop-blur-sm border border-slate-200/50 rounded-xl shadow-sm">
      {/* Main Controls */}
      <div className="p-4">
        {/* Selected Offer Actions */}
        {selectedOffer && (
          <div className="mb-4 p-4 bg-gradient-to-r from-emerald-50 to-blue-50 border border-emerald-200 rounded-xl">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse" />
                <span className="text-sm font-medium text-slate-700">Selected Offer</span>
              </div>
              <div className="text-xs text-slate-500">
                {selectedOffer.currency?.symbol || selectedOffer.walletCurrency?.symbol || 'N/A'} • {selectedOffer.operationType}
              </div>
            </div>

            <div className="flex space-x-2">
              <button
                onClick={() => onAcceptOffer(selectedOffer.hash)}
                className="flex-1 flex items-center justify-center space-x-2 bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2.5 rounded-lg transition-colors duration-200 font-medium shadow-sm"
              >
                <Check className="w-4 h-4" />
                <span>Accept</span>
              </button>
              <button
                onClick={() => onRejectOffer(selectedOffer.hash)}
                className="flex-1 flex items-center justify-center space-x-2 bg-red-600 hover:bg-red-700 text-white px-4 py-2.5 rounded-lg transition-colors duration-200 font-medium shadow-sm"
              >
                <X className="w-4 h-4" />
                <span>Reject</span>
              </button>
            </div>
          </div>
        )}

        {/* Quick Controls */}
        <div className="grid grid-cols-2 gap-3 mb-4">
          {/* Monitoring Toggle */}
          <button
            onClick={toggleMonitoring}
            className={`flex items-center justify-center space-x-2 p-3 rounded-xl transition-all duration-200 font-medium shadow-sm ${
              settings.monitoring
                ? 'bg-emerald-500 hover:bg-emerald-600 text-white shadow-emerald-500/25 border border-emerald-400'
                : 'bg-slate-100 hover:bg-slate-200 text-slate-600 border border-slate-200'
            }`}
          >
            {settings.monitoring ? (
              <Pause className="w-4 h-4" />
            ) : (
              <Play className="w-4 h-4" />
            )}
            <span className="text-sm">
              {settings.monitoring ? 'Pause' : 'Start'}
            </span>
          </button>

          {/* Auto Accept Toggle */}
          <button
            onClick={toggleAutoAccept}
            className={`flex items-center justify-center space-x-2 p-3 rounded-xl transition-all duration-200 font-medium shadow-sm ${
              settings.autoAccept
                ? 'bg-blue-500 hover:bg-blue-600 text-white shadow-blue-500/25 border border-blue-400'
                : 'bg-slate-100 hover:bg-slate-200 text-slate-600 border border-slate-200'
            }`}
          >
            <Shield className={`w-4 h-4 ${settings.autoAccept ? 'animate-pulse' : ''}`} />
            <span className="text-sm">Auto</span>
          </button>
        </div>

        {/* Settings Toggle */}
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="w-full flex items-center justify-between p-3 bg-slate-100 hover:bg-slate-200 rounded-xl transition-all duration-200 text-slate-700 border border-slate-200 shadow-sm"
        >
          <div className="flex items-center space-x-2">
            <Settings className="w-4 h-4" />
            <span className="font-medium">Advanced Settings</span>
          </div>
          {isExpanded ? (
            <ChevronUp className="w-4 h-4" />
          ) : (
            <ChevronDown className="w-4 h-4" />
          )}
        </button>
      </div>

      {/* Expanded Settings */}
      {isExpanded && (
        <div className="border-t border-slate-200 p-4 space-y-4 animate-fade-in bg-slate-50/50">
          {/* Notification Settings */}
          <div className="space-y-3">
            <h3 className="text-sm font-semibold text-slate-700 flex items-center space-x-2">
              <Bell className="w-4 h-4" />
              <span>Notifications</span>
            </h3>
            
            <div className="grid grid-cols-2 gap-3">
              {/* Notifications Toggle */}
              <button
                onClick={toggleNotifications}
                className={`flex items-center justify-center space-x-2 p-3 rounded-xl transition-all duration-200 text-sm shadow-sm ${
                  settings.notificationsEnabled
                    ? 'bg-yellow-500 hover:bg-yellow-600 text-white border border-yellow-400'
                    : 'bg-white hover:bg-gray-50 text-slate-600 border border-slate-200'
                }`}
              >
                {settings.notificationsEnabled ? (
                  <Bell className="w-4 h-4" />
                ) : (
                  <BellOff className="w-4 h-4" />
                )}
                <span>Alerts</span>
              </button>

              {/* Sound Toggle */}
              <button
                onClick={toggleSound}
                className={`flex items-center justify-center space-x-2 p-3 rounded-xl transition-all duration-200 text-sm shadow-sm ${
                  settings.soundEnabled
                    ? 'bg-purple-500 hover:bg-purple-600 text-white border border-purple-400'
                    : 'bg-white hover:bg-gray-50 text-slate-600 border border-slate-200'
                }`}
              >
                {settings.soundEnabled ? (
                  <Volume2 className="w-4 h-4" />
                ) : (
                  <VolumeX className="w-4 h-4" />
                )}
                <span>Sound</span>
              </button>
            </div>
          </div>

          {/* Blacklist Keywords */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-semibold text-slate-700 flex items-center space-x-2">
                <Filter className="w-4 h-4" />
                <span>Blacklist ({settings.blacklistKeywords.length})</span>
              </h3>
              <button
                onClick={() => setShowKeywordInput(!showKeywordInput)}
                className="flex items-center space-x-1 text-emerald-600 hover:text-emerald-700 transition-colors duration-200 text-sm font-medium"
              >
                <Plus className="w-4 h-4" />
                <span>Add</span>
              </button>
            </div>

            {/* Add Keyword Input */}
            {showKeywordInput && (
              <div className="space-y-3">
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={newKeyword}
                    onChange={(e) => setNewKeyword(e.target.value)}
                    onKeyDown={handleKeyPress}
                    placeholder={isGrouping ? "Add another keyword or press Enter to finish..." : "Enter keyword..."}
                    className={`flex-1 bg-white border rounded-xl px-4 py-2.5 text-slate-700 text-sm focus:outline-none focus:ring-2 focus:border-emerald-500 transition-all duration-200 ${
                      isGrouping
                        ? 'border-yellow-400 focus:ring-yellow-500'
                        : 'border-slate-200 focus:ring-emerald-500'
                    }`}
                    autoFocus
                  />
                  <button
                    onClick={handleKeywordAdd}
                    disabled={!newKeyword.trim()}
                    className="bg-emerald-500 hover:bg-emerald-600 disabled:bg-gray-400 disabled:cursor-not-allowed text-white px-4 py-2.5 rounded-xl transition-colors duration-200 shadow-sm"
                  >
                    <Check className="w-4 h-4" />
                  </button>
                  {isGrouping ? (
                    <button
                      onClick={finishGrouping}
                      className="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2.5 rounded-xl transition-colors duration-200 shadow-sm"
                      title="Finish grouping"
                    >
                      <Plus className="w-4 h-4" />
                    </button>
                  ) : null}
                  <button
                    onClick={() => {
                      if (isGrouping) {
                        cancelGrouping();
                      } else {
                        setNewKeyword('');
                        setShowKeywordInput(false);
                      }
                    }}
                    className="bg-slate-200 hover:bg-slate-300 text-slate-600 px-4 py-2.5 rounded-xl transition-colors duration-200 border border-slate-200 shadow-sm"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>

                {/* Grouping Status */}
                {isGrouping && (
                  <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-3">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-yellow-800">Grouping Mode Active</span>
                      <span className="text-xs text-yellow-600">{keywordGroup.length} keyword(s) in group</span>
                    </div>
                    <p className="text-xs text-yellow-700 mb-3">
                      Type keywords and press Enter to add them. Press Enter on empty input to finish grouping.
                    </p>
                    
                    {/* Temporary Group Preview */}
                    {keywordGroup.length > 0 && (
                      <div className="space-y-1">
                        <span className="text-xs text-yellow-800 font-medium">Keywords in group:</span>
                        <div className="flex flex-wrap gap-1">
                          {keywordGroup.map((keyword, index) => (
                            <div
                              key={index}
                              className="flex items-center space-x-1 bg-yellow-200 rounded-lg px-2 py-1 text-xs text-yellow-800"
                            >
                              <span>{keyword}</span>
                              <button
                                onClick={() => removeFromGroup(keyword)}
                                className="text-yellow-600 hover:text-yellow-800 transition-colors duration-200"
                              >
                                <X className="w-3 h-3" />
                              </button>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}

            {/* Keywords List */}
            {settings.blacklistKeywords.length > 0 && (
              <div className="max-h-32 overflow-y-auto space-y-2 custom-scrollbar-modern">
                {settings.blacklistKeywords.map((keyword, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between bg-white rounded-lg px-3 py-2 border border-slate-200 shadow-sm"
                  >
                    <span className="text-sm text-slate-700">{keyword}</span>
                    <button
                      onClick={() => handleKeywordRemove(keyword)}
                      className="text-red-500 hover:text-red-600 transition-colors duration-200 p-1 rounded hover:bg-red-50"
                    >
                      <Trash2 className="w-3 h-3" />
                    </button>
                  </div>
                ))}
              </div>
            )}

            {settings.blacklistKeywords.length === 0 && (
              <div className="text-center py-4 text-slate-500 text-sm">
                No keywords added
              </div>
            )}
          </div>

          {/* Fuzzy Matching Settings */}
          <div className="space-y-3">
            <h3 className="text-sm font-semibold text-slate-700 flex items-center space-x-2">
              <Filter className="w-4 h-4" />
              <span>Smart Matching</span>
            </h3>

            {/* Enable Fuzzy Matching */}
            <div className="flex items-center justify-between">
              <span className="text-sm text-slate-600">Enable fuzzy matching</span>
              <button
                onClick={() => onSettingsUpdate({
                  ...settings,
                  fuzzyMatching: {
                    ...settings.fuzzyMatching,
                    enabled: !settings.fuzzyMatching.enabled
                  }
                })}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200 ${
                  settings.fuzzyMatching.enabled ? 'bg-emerald-500' : 'bg-slate-300'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200 shadow-sm ${
                    settings.fuzzyMatching.enabled ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>
            
            {/* Fuzzy Matching Options */}
            {settings.fuzzyMatching.enabled && (
              <div className="space-y-3 bg-white rounded-xl p-4 border border-slate-200 shadow-sm">
                {/* Threshold Slider */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-slate-600">Match Threshold</span>
                    <span className="text-xs text-emerald-600 font-mono bg-emerald-50 px-2 py-1 rounded">{settings.fuzzyMatching.threshold}</span>
                  </div>
                  <input
                    type="range"
                    min="0.1"
                    max="1.0"
                    step="0.1"
                    value={settings.fuzzyMatching.threshold}
                    onChange={(e) => onSettingsUpdate({
                      ...settings,
                      fuzzyMatching: {
                        ...settings.fuzzyMatching,
                        threshold: parseFloat(e.target.value)
                      }
                    })}
                    className="w-full h-2 bg-slate-200 rounded-lg appearance-none cursor-pointer slider"
                  />
                  <div className="flex justify-between text-xs text-slate-500">
                    <span>Strict</span>
                    <span>Loose</span>
                  </div>
                </div>
                
                {/* Enable Aliases */}
                <div className="flex items-center justify-between">
                  <span className="text-sm text-slate-600">Use payment aliases</span>
                  <button
                    onClick={() => onSettingsUpdate({
                      ...settings,
                      fuzzyMatching: {
                        ...settings.fuzzyMatching,
                        enableAliases: !settings.fuzzyMatching.enableAliases
                      }
                    })}
                    className={`relative inline-flex h-5 w-9 items-center rounded-full transition-colors duration-200 ${
                      settings.fuzzyMatching.enableAliases ? 'bg-emerald-500' : 'bg-slate-300'
                    }`}
                  >
                    <span
                      className={`inline-block h-3 w-3 transform rounded-full bg-white transition-transform duration-200 shadow-sm ${
                        settings.fuzzyMatching.enableAliases ? 'translate-x-5' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>

                <div className="text-xs text-blue-700 bg-blue-50 border border-blue-200 rounded-lg p-2">
                  <span className="text-blue-800 font-medium">💡 Tip:</span> Fuzzy matching helps catch AirTM payment methods with slight variations in naming.
                </div>
              </div>
            )}
          </div>

          {/* Status Info */}
          <div className="pt-3 border-t border-slate-200">
            <div className="flex items-center justify-between text-xs text-slate-500">
              <div className="flex items-center space-x-2">
                <Activity className="w-3 h-3" />
                <span>Status: {settings.monitoring ? 'Active' : 'Paused'}</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className={`w-2 h-2 rounded-full ${
                  settings.monitoring ? 'bg-emerald-500 animate-pulse' : 'bg-slate-400'
                }`} />
                <span>{settings.monitoring ? 'Monitoring' : 'Stopped'}</span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ControlPanel;
