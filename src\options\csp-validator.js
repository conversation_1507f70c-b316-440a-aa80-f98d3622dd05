/**
 * CSP Compliance Validator for Options Page
 * Run this in browser console to validate CSP compliance
 */

function validateCSPCompliance() {
  console.log('🔒 CSP COMPLIANCE VALIDATION');
  console.log('============================');
  
  const results = {
    passed: 0,
    failed: 0,
    warnings: 0,
    details: []
  };
  
  function addResult(test, status, message, details = '') {
    results.details.push({ test, status, message, details });
    if (status === 'PASS') results.passed++;
    else if (status === 'FAIL') results.failed++;
    else results.warnings++;
  }
  
  // Test 1: Check for CSP violations in console
  console.log('\n🧪 TEST 1: Console CSP Violations');
  const consoleErrors = [];
  const originalError = console.error;
  console.error = function(...args) {
    const message = args.join(' ');
    if (message.includes('Content Security Policy') || message.includes('CSP')) {
      consoleErrors.push(message);
    }
    originalError.apply(console, args);
  };
  
  setTimeout(() => {
    console.error = originalError;
    if (consoleErrors.length === 0) {
      addResult('CSP Console Errors', 'PASS', 'No CSP violations found in console');
    } else {
      addResult('CSP Console Errors', 'FAIL', `Found ${consoleErrors.length} CSP violations`, consoleErrors.join('\\n'));
    }
  }, 2000);
  
  // Test 2: Script loading validation
  console.log('\n🧪 TEST 2: Script Loading');
  const scripts = Array.from(document.scripts);
  let inlineScripts = 0;
  let externalScripts = 0;
  
  scripts.forEach(script => {
    if (script.src) {
      externalScripts++;
      if (!script.src.startsWith('chrome-extension://') && !script.src.startsWith('./')) {
        addResult('External Scripts', 'WARN', `External script detected: ${script.src}`);
      }
    } else if (script.textContent.trim()) {
      inlineScripts++;
    }
  });
  
  if (inlineScripts === 0) {
    addResult('Inline Scripts', 'PASS', 'No inline scripts found');
  } else {
    addResult('Inline Scripts', 'FAIL', `Found ${inlineScripts} inline scripts`);
  }
  
  addResult('External Scripts', 'PASS', `${externalScripts} external scripts loaded`);
  
  // Test 3: Style loading validation
  console.log('\n🧪 TEST 3: Style Loading');
  const stylesheets = Array.from(document.styleSheets);
  let inlineStyles = 0;
  let externalStyles = 0;
  
  stylesheets.forEach(sheet => {
    if (sheet.href) {
      externalStyles++;
    } else {
      inlineStyles++;
    }
  });
  
  addResult('Stylesheets', 'PASS', `${externalStyles} external, ${inlineStyles} inline stylesheets`);
  
  // Test 4: Chrome Extension API access
  console.log('\n🧪 TEST 4: Extension API Access');
  if (typeof chrome !== 'undefined' && chrome.storage) {
    addResult('Chrome APIs', 'PASS', 'Chrome extension APIs accessible');
  } else {
    addResult('Chrome APIs', 'FAIL', 'Chrome extension APIs not accessible');
  }
  
  // Test 5: React initialization
  console.log('\n🧪 TEST 5: React Initialization');
  const root = document.getElementById('root');
  if (root) {
    const hasReactContent = root.innerHTML.includes('min-h-screen') || 
                           root.innerHTML.includes('options-loading') ||
                           root.innerHTML.includes('options-error');
    
    if (hasReactContent) {
      addResult('React Loading', 'PASS', 'React application loaded successfully');
    } else if (root.innerHTML.includes('initial-loading')) {
      addResult('React Loading', 'WARN', 'React still loading...');
    } else {
      addResult('React Loading', 'FAIL', 'React failed to load');
    }
  } else {
    addResult('React Loading', 'FAIL', 'Root element not found');
  }
  
  // Test 6: Resource loading
  console.log('\n🧪 TEST 6: Resource Loading');
  const images = Array.from(document.images);
  let loadedImages = 0;
  let failedImages = 0;
  
  images.forEach(img => {
    if (img.complete && img.naturalHeight !== 0) {
      loadedImages++;
    } else {
      failedImages++;
    }
  });
  
  if (failedImages === 0) {
    addResult('Image Loading', 'PASS', `${loadedImages} images loaded successfully`);
  } else {
    addResult('Image Loading', 'WARN', `${failedImages} images failed to load`);
  }
  
  // Test 7: Font loading
  console.log('\n🧪 TEST 7: Font Loading');
  if (document.fonts) {
    document.fonts.ready.then(() => {
      addResult('Font Loading', 'PASS', `${document.fonts.size} fonts loaded`);
    });
  } else {
    addResult('Font Loading', 'WARN', 'Font loading API not available');
  }
  
  // Test 8: Network requests
  console.log('\n🧪 TEST 8: Network Requests');
  const originalFetch = window.fetch;
  let networkRequests = 0;
  let blockedRequests = 0;
  
  window.fetch = function(...args) {
    networkRequests++;
    return originalFetch.apply(this, args).catch(error => {
      if (error.message.includes('CSP') || error.message.includes('Content Security Policy')) {
        blockedRequests++;
      }
      throw error;
    });
  };
  
  setTimeout(() => {
    window.fetch = originalFetch;
    if (blockedRequests === 0) {
      addResult('Network Requests', 'PASS', `${networkRequests} requests, none blocked by CSP`);
    } else {
      addResult('Network Requests', 'FAIL', `${blockedRequests} requests blocked by CSP`);
    }
  }, 3000);
  
  // Generate final report
  setTimeout(() => {
    console.log('\n📊 CSP COMPLIANCE REPORT');
    console.log('========================');
    console.log(`✅ Passed: ${results.passed}`);
    console.log(`❌ Failed: ${results.failed}`);
    console.log(`⚠️  Warnings: ${results.warnings}`);
    
    console.log('\n📋 DETAILED RESULTS:');
    results.details.forEach(result => {
      const icon = result.status === 'PASS' ? '✅' : result.status === 'FAIL' ? '❌' : '⚠️';
      console.log(`${icon} ${result.test}: ${result.message}`);
      if (result.details) {
        console.log(`   Details: ${result.details}`);
      }
    });
    
    const overallStatus = results.failed === 0 ? 'COMPLIANT' : 'NON-COMPLIANT';
    console.log(`\n🎯 OVERALL STATUS: ${overallStatus}`);
    
    if (results.failed > 0) {
      console.log('\n🔧 RECOMMENDED ACTIONS:');
      console.log('1. Check browser console for specific CSP error messages');
      console.log('2. Verify manifest.json CSP configuration');
      console.log('3. Ensure all scripts are external (no inline JavaScript)');
      console.log('4. Check that all resources load from allowed sources');
    }
  }, 5000);
}

// Auto-run validation if in development
if (window.location.href.includes('chrome-extension://')) {
  setTimeout(validateCSPCompliance, 2000);
}

// Export for manual use
window.validateCSPCompliance = validateCSPCompliance;

console.log('🔒 CSP validator loaded. Run validateCSPCompliance() to check compliance.');
