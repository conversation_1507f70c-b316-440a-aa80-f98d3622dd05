import React from 'react';
import { StatusCardsProps } from '../types';
import StatusItem from './StatusItem';
import MonitoringTime from './MonitoringTime';
import ToggleSwitch from './ToggleSwitch';

const StatusCards: React.FC<StatusCardsProps> = ({ 
  stats, 
  settings, 
  onMonitoringToggle 
}) => {
  return (
    <>
      {/* Status Overview */}
      <div className="status-card">
        <h3>📊 Monitoring Status</h3>
        <div className="status-grid">
          <StatusItem
            value={stats.offersCount}
            label="Active Offers"
          />
          <StatusItem
            value={
              <MonitoringTime
                monitoringStartTime={stats.monitoringStartTime}
                isMonitoring={settings.monitoring}
              />
            }
            label="Monitoring Time"
          />
        </div>
      </div>

      {/* Quick Toggle */}
      <div className="status-card">
        <h3>🎛️ Quick Controls</h3>
        <div className="action-button" id="toggle-monitoring">
          <span className="action-icon">👁️</span>
          <span>Monitoring</span>
          <div style={{ marginLeft: 'auto' }}>
            <ToggleSwitch
              checked={settings.monitoring}
              onChange={onMonitoringToggle}
            />
          </div>
        </div>
      </div>
    </>
  );
};

export default StatusCards;
