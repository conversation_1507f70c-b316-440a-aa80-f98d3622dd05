<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ControlPanel Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
            min-height: 100vh;
        }
        
        .popup-container {
            width: 600px;
            height: 800px;
            margin: 0 auto;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .control-panel-container {
            position: absolute;
            bottom: 20px;
            left: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(16px);
            border: 1px solid rgba(226, 232, 240, 0.5);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
            padding: 16px;
        }
        
        .mock-control-panel {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
        }
        
        .quick-controls {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 16px;
        }
        
        .control-button {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 12px;
            border-radius: 12px;
            font-weight: 500;
            font-size: 14px;
            border: 1px solid;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        
        .control-button.active {
            background: #10b981;
            color: white;
            border-color: #059669;
        }
        
        .control-button.inactive {
            background: #f1f5f9;
            color: #64748b;
            border-color: #e2e8f0;
        }
        
        .control-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        
        .settings-toggle {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px;
            background: #f1f5f9;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            font-weight: 500;
            color: #475569;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-bottom: 16px;
        }
        
        .settings-toggle:hover {
            background: #e2e8f0;
        }
        
        .expanded-settings {
            background: #f8fafc;
            border-top: 1px solid #e2e8f0;
            padding: 16px;
            border-radius: 0 0 12px 12px;
            margin: -16px -16px 0 -16px;
        }
        
        .notification-controls {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 16px;
        }
        
        .notification-button {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 12px;
            border-radius: 12px;
            font-size: 14px;
            border: 1px solid;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .notification-button.enabled {
            background: #f59e0b;
            color: white;
            border-color: #d97706;
        }
        
        .notification-button.disabled {
            background: white;
            color: #64748b;
            border-color: #e2e8f0;
        }
        
        .section-title {
            font-size: 14px;
            font-weight: 600;
            color: #475569;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .status-info {
            padding-top: 12px;
            border-top: 1px solid #e2e8f0;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 12px;
            color: #64748b;
        }
        
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #10b981;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .icon {
            width: 16px;
            height: 16px;
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="popup-container">
        <div style="padding: 20px; height: calc(100% - 200px); background: rgba(255, 255, 255, 0.7); margin: 20px; border-radius: 16px; margin-bottom: 0;">
            <h2 style="margin: 0 0 20px 0; color: #1e293b;">Airtm Monitor Pro - Control Panel Test</h2>
            <p style="color: #64748b; margin-bottom: 20px;">This preview shows the ControlPanel component layout with all buttons visible.</p>
            
            <div style="background: rgba(255, 255, 255, 0.8); padding: 16px; border-radius: 12px; border: 1px solid #e2e8f0;">
                <h3 style="margin: 0 0 12px 0; color: #475569;">Mock Offers List</h3>
                <div style="height: 200px; background: #f8fafc; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #64748b;">
                    Offers would be displayed here...
                </div>
            </div>
        </div>
        
        <div class="control-panel-container">
            <div class="mock-control-panel">
                <!-- Quick Controls -->
                <div class="quick-controls">
                    <button class="control-button active">
                        <span class="icon">▶</span>
                        <span>Monitoring</span>
                    </button>
                    <button class="control-button inactive">
                        <span class="icon">🛡</span>
                        <span>Auto Accept</span>
                    </button>
                </div>
                
                <!-- Settings Toggle -->
                <button class="settings-toggle" onclick="toggleSettings()">
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <span class="icon">⚙</span>
                        <span>Advanced Settings</span>
                    </div>
                    <span class="icon" id="chevron">▼</span>
                </button>
                
                <!-- Expanded Settings -->
                <div class="expanded-settings" id="expandedSettings" style="display: none;">
                    <div class="section-title">
                        <span class="icon">🔔</span>
                        <span>Notifications</span>
                    </div>
                    
                    <div class="notification-controls">
                        <button class="notification-button enabled">
                            <span class="icon">🔔</span>
                            <span>Alerts</span>
                        </button>
                        <button class="notification-button enabled">
                            <span class="icon">🔊</span>
                            <span>Sound</span>
                        </button>
                    </div>
                    
                    <div class="section-title">
                        <span class="icon">🔍</span>
                        <span>Blacklist (3)</span>
                    </div>
                    
                    <div style="background: white; border-radius: 8px; padding: 12px; margin-bottom: 16px; border: 1px solid #e2e8f0;">
                        <div style="font-size: 12px; color: #64748b; margin-bottom: 8px;">Keywords: paypal, zelle, cashapp</div>
                        <button style="background: #10b981; color: white; border: none; padding: 6px 12px; border-radius: 6px; font-size: 12px; cursor: pointer;">+ Add Keyword</button>
                    </div>
                    
                    <div class="section-title">
                        <span class="icon">🎯</span>
                        <span>Smart Matching</span>
                    </div>
                    
                    <div style="background: white; border-radius: 8px; padding: 12px; margin-bottom: 16px; border: 1px solid #e2e8f0;">
                        <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 8px;">
                            <span style="font-size: 14px; color: #64748b;">Enable fuzzy matching</span>
                            <div style="width: 44px; height: 24px; background: #10b981; border-radius: 12px; position: relative; cursor: pointer;">
                                <div style="width: 16px; height: 16px; background: white; border-radius: 50%; position: absolute; top: 4px; right: 4px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);"></div>
                            </div>
                        </div>
                        <div style="font-size: 12px; color: #64748b;">Threshold: 0.8</div>
                        <input type="range" min="0.1" max="1.0" step="0.1" value="0.8" style="width: 100%; margin: 8px 0;">
                    </div>
                    
                    <div class="status-info">
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <span class="icon">📊</span>
                            <span>Status: Active</span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 4px;">
                            <div class="status-indicator"></div>
                            <span>Monitoring</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function toggleSettings() {
            const expandedSettings = document.getElementById('expandedSettings');
            const chevron = document.getElementById('chevron');
            
            if (expandedSettings.style.display === 'none') {
                expandedSettings.style.display = 'block';
                chevron.textContent = '▲';
            } else {
                expandedSettings.style.display = 'none';
                chevron.textContent = '▼';
            }
        }
    </script>
</body>
</html>
