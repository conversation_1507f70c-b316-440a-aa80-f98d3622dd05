function t(){try{return!!chrome?.runtime?.id}catch{return!1}}function i(){if(!t())throw new Error("Extension context is invalid. Please reload the extension or refresh the page.")}function s(e){const n=e instanceof Error?e.message:String(e);return n?.includes("Could not establish connection")||n?.includes("Receiving end does not exist")||n?.includes("Extension context invalidated")}async function o(e){i();try{return await chrome.runtime.sendMessage(e)}catch(n){throw s(n)?new Error("Extension context lost during message sending. Please reload the extension."):n}}export{s as a,t as i,o as s};
