<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Popup Preview - Airtm Monitor Pro</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: #f1f5f9;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        
        .preview-container {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 800px;
        }
        
        .popup-frame {
            width: 600px;
            height: 800px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            margin: 0 auto;
        }
        
        .instructions {
            text-align: center;
            margin-bottom: 20px;
            color: #64748b;
        }
        
        .note {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 12px;
            margin-top: 20px;
            font-size: 14px;
            color: #92400e;
        }
        
        .steps {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 8px;
            padding: 16px;
            margin-top: 20px;
        }
        
        .steps h3 {
            margin: 0 0 12px 0;
            color: #0c4a6e;
        }
        
        .steps ol {
            margin: 0;
            padding-left: 20px;
            color: #0c4a6e;
        }
        
        .steps li {
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <h1 style="text-align: center; color: #1e293b; margin-bottom: 10px;">Airtm Monitor Pro - Popup Preview</h1>
        <p class="instructions">This iframe shows the actual built popup from the extension</p>
        
        <div class="popup-frame">
            <iframe 
                src="./dist/src/popup/index.html" 
                width="600" 
                height="800"
                style="border: none; display: block;"
                title="Popup Preview">
            </iframe>
        </div>
        
        <div class="note">
            <strong>Note:</strong> This preview loads the actual built popup HTML file from the dist folder. 
            If you see a blank frame, make sure you've run <code>npm run build</code> first.
        </div>
        
        <div class="steps">
            <h3>To see the real popup in action:</h3>
            <ol>
                <li>Load the extension in Chrome (chrome://extensions/)</li>
                <li>Enable "Developer mode"</li>
                <li>Click "Load unpacked" and select the <code>dist</code> folder</li>
                <li>Click the extension icon in the toolbar to see the actual popup</li>
            </ol>
        </div>
        
        <div class="note" style="background: #ecfdf5; border-color: #10b981; color: #065f46;">
            <strong>Alternative:</strong> You can also open <code>dist/src/popup/index.html</code> directly in your browser, 
            but some Chrome extension APIs won't work outside the extension context.
        </div>
    </div>
</body>
</html>
