import{c as i,j as e,R as X,X as T,r as g,C as P,P as J,a as te}from"./globals-653d0391.js";import{A as ae}from"./AirTMPaymentMethodMatcher-d2f6c65f.js";/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const le=[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]],re=i("activity",le);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ne=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m4.9 4.9 14.2 14.2",key:"1m5liu"}]],ce=i("ban",ne);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const oe=[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M17 17H4a1 1 0 0 1-.74-1.673C4.59 13.956 6 12.499 6 8a6 6 0 0 1 .258-1.742",key:"178tsu"}],["path",{d:"m2 2 20 20",key:"1ooewy"}],["path",{d:"M8.668 3.01A6 6 0 0 1 18 8c0 2.687.77 4.653 1.707 6.05",key:"1hqiys"}]],ie=i("bell-off",oe);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const de=[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]],Q=i("bell",de);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const me=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],xe=i("chevron-down",me);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const he=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],pe=i("chevron-up",he);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ue=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]],fe=i("circle-alert",ue);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const je=[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]],be=i("circle-check-big",je);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ye=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]],Ne=i("circle-x",ye);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ge=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]],ee=i("clock",ge);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ve=[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],we=i("eye",ve);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ke=[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]],L=i("funnel",ke);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ce=[["rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",key:"zuxfzm"}],["rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",key:"1okwgv"}]],Ae=i("pause",Ce);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Me=[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]],_e=i("play",Me);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ze=[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]],Z=i("search",ze);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ee=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],$e=i("settings",Ee);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Se=[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]],se=i("shield",Se);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Te=[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]],Le=i("star",Te);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Re=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]],Pe=i("trash-2",Re);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fe=[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]],De=i("trending-down",Fe);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Oe=[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]],F=i("trending-up",Oe);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ie=[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["path",{d:"M16 9a5 5 0 0 1 0 6",key:"1q6k2b"}],["path",{d:"M19.364 18.364a9 9 0 0 0 0-12.728",key:"ijwkga"}]],Ke=i("volume-2",Ie);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Be=[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["line",{x1:"22",x2:"16",y1:"9",y2:"15",key:"1ewh16"}],["line",{x1:"16",x2:"22",y1:"9",y2:"15",key:"5ykzw1"}]],He=i("volume-x",Be),Ge=({offer:s,isSelected:l,onClick:n,isBlacklisted:j=!1})=>{var _,$,S,z,E,x,N,I,K,B,H,G,V,W;const a=((_=s.currency)==null?void 0:_.symbol)||(($=s.walletCurrency)==null?void 0:$.symbol)||"USD",u=s.operationType||"BUY",f=typeof s.grossAmount=="string"?parseFloat(s.grossAmount)||0:s.grossAmount||0,h=typeof s.netAmount=="string"?parseFloat(s.netAmount)||0:s.netAmount||0,m=s.peer?`${s.peer.firstName} ${s.peer.lastName}`:"Unknown User",o=((z=(S=s.peer)==null?void 0:S.numbers)==null?void 0:z.score)||0,p=((x=(E=s.peer)==null?void 0:E.numbers)==null?void 0:x.completedOperations)||0,b=((I=(N=s.peer)==null?void 0:N.securityHub)==null?void 0:I.facialVerified)&&((B=(K=s.peer)==null?void 0:K.securityHub)==null?void 0:B.documentVerified)||!1;let d=((V=(G=(H=s.makerPaymentMethod)==null?void 0:H.version)==null?void 0:G.category)==null?void 0:V.translationTag)||"Unknown";d.startsWith("CATEGORY_TREE:AIRTM_")&&(d=d.replace("CATEGORY_TREE:AIRTM_","")),d.startsWith("E_TRANSFER_")&&(d=d.replace("E_TRANSFER_","")),d=d.replace(/_/g," ").toLowerCase().replace(/\b\w/g,A=>A.toUpperCase());const v=s.status||"CREATED",k=s.createdAt?new Date(s.createdAt):new Date,c=Date.now()-k.getTime()<5*60*1e3,C=A=>{const R=new Date().getTime()-A.getTime(),U=Math.floor(R/(1e3*60)),q=Math.floor(R/(1e3*60*60)),Y=Math.floor(R/(1e3*60*60*24));return Y>0?`${Y}d ago`:q>0?`${q}h ago`:U>0?`${U}m ago`:"Just now"},r=(A=>{switch(A){case"BUY":return{color:"text-emerald-400",bgColor:"bg-emerald-500/10",borderColor:"border-emerald-500/20",icon:F,label:"Buy"};case"SELL":return{color:"text-red-400",bgColor:"bg-red-500/10",borderColor:"border-red-500/20",icon:De,label:"Sell"};default:return{color:"text-blue-400",bgColor:"bg-blue-500/10",borderColor:"border-blue-500/20",icon:F,label:A}}})(u),t=r.icon,w=A=>{switch(A){case"CREATED":return"text-emerald-400";case"PAUSED":return"text-yellow-400";case"INACTIVE":return"text-red-400";default:return"text-gray-400"}};return e.jsxs("div",{onClick:n,className:`
        relative rounded-xl border transition-all duration-200 cursor-pointer
        hover:scale-[1.02] hover:shadow-lg hover:shadow-black/20
        ${l?"border-emerald-500 bg-gradient-to-br from-emerald-500/10 to-gray-800 shadow-lg shadow-emerald-500/20":j?"border-red-500/50 bg-gradient-to-br from-red-500/5 to-gray-800 opacity-60":"border-gray-700 bg-gradient-to-br from-gray-800 to-gray-900 hover:border-gray-600"}
        ${c?"ring-2 ring-emerald-500/30":""}
      `,children:[j&&e.jsx("div",{className:"absolute top-2 right-2 z-10",children:e.jsxs("div",{className:"flex items-center space-x-1 bg-red-500/20 border border-red-500/30 rounded-lg px-2 py-1",children:[e.jsx(ce,{className:"w-3 h-3 text-red-400"}),e.jsx("span",{className:"text-xs text-red-400 font-medium",children:"Blocked"})]})}),c&&!j&&e.jsx("div",{className:"absolute top-2 right-2 z-10",children:e.jsxs("div",{className:"flex items-center space-x-1 bg-emerald-500/20 border border-emerald-500/30 rounded-lg px-2 py-1",children:[e.jsx("div",{className:"w-2 h-2 bg-emerald-400 rounded-full animate-pulse"}),e.jsx("span",{className:"text-xs text-emerald-400 font-medium",children:"New"})]})}),e.jsxs("div",{className:"p-4",children:[e.jsxs("div",{className:"flex items-start justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:`${r.bgColor} ${r.borderColor} border rounded-lg p-2`,children:e.jsx(t,{className:`w-4 h-4 ${r.color}`})}),e.jsx("div",{children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-lg font-bold text-white",children:a}),e.jsx("span",{className:`text-xs px-2 py-1 rounded-full ${r.bgColor} ${r.color} font-medium`,children:r.label})]})})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:`text-xs font-medium ${w(v)}`,children:v}),e.jsxs("div",{className:"text-xs text-gray-500 flex items-center space-x-1 mt-1",children:[e.jsx(ee,{className:"w-3 h-3"}),e.jsx("span",{children:C(k)})]})]})]}),e.jsxs("div",{className:"mb-3",children:[e.jsx("div",{className:"text-sm text-gray-400 mb-1",children:"Amount Range"}),e.jsxs("div",{className:"text-white font-semibold",children:[h.toLocaleString()," - ",f.toLocaleString()," ",a]}),f>0&&e.jsxs("div",{className:"text-xs text-gray-500",children:["Available: ",f.toLocaleString()," ",a]})]}),e.jsx("div",{className:"flex items-center justify-between mb-3",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:"w-8 h-8 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-full flex items-center justify-center",children:e.jsx("span",{className:"text-white text-xs font-bold",children:m.charAt(0).toUpperCase()})}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx("span",{className:"text-sm font-medium text-white",children:m}),b&&e.jsx(se,{className:"w-3 h-3 text-blue-400"})]}),e.jsxs("div",{className:"flex items-center space-x-2 text-xs text-gray-400",children:[e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx(Le,{className:"w-3 h-3 text-yellow-400"}),e.jsx("span",{children:o.toFixed(1)})]}),e.jsx("span",{children:"•"}),e.jsxs("span",{children:[p," trades"]})]})]})]})}),e.jsxs("div",{className:"mb-3",children:[e.jsx("div",{className:"text-sm text-gray-400 mb-1",children:"Payment Method"}),e.jsx("div",{className:"text-white font-medium",children:d})]}),e.jsxs("div",{className:"flex items-center justify-between pt-3 border-t border-gray-700",children:[e.jsxs("div",{className:"text-xs text-gray-500",children:["ID: ",(W=s.hash)==null?void 0:W.substring(0,8),"..."]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[v==="CREATED"&&e.jsx("div",{className:"w-2 h-2 bg-emerald-400 rounded-full animate-pulse"}),e.jsx("span",{className:"text-xs text-gray-400",children:"Click to select"})]})]})]})]})},Ve=(s,l,n)=>{if(!l.length)return s;const j=new ae({threshold:n.fuzzyMatching.threshold,enableAliases:n.fuzzyMatching.enableAliases,customAliases:n.fuzzyMatching.customAliases});return s.filter(a=>{var f,h,m;const u=((m=(h=(f=a.makerPaymentMethod)==null?void 0:f.version)==null?void 0:h.category)==null?void 0:m.translationTag)||"";if(n.fuzzyMatching.enabled)return!j.matchAnyPaymentMethod(u,l).isMatch;{let o=u.toLowerCase();return o.startsWith("category_tree:airtm_")&&(o=o.replace("category_tree:airtm_","")),o.startsWith("e_transfer_")&&(o=o.replace("e_transfer_","")),!l.some(p=>o.includes(p.toLowerCase()))}})},We=({offers:s,selectedOffer:l,onOfferSelect:n,settings:j})=>{var k;const[a,u]=X.useState(""),[f,h]=X.useState(!1),m=Ve(s,j.blacklistKeywords||[],j),p=m.filter(c=>{var M,r,t,w,_;return a?[c.peer?`${c.peer.firstName} ${c.peer.lastName}`:"",((t=(r=(M=c.makerPaymentMethod)==null?void 0:M.version)==null?void 0:r.category)==null?void 0:t.translationTag)||"",c.hash||"",c.status||"",((w=c.currency)==null?void 0:w.symbol)||((_=c.walletCurrency)==null?void 0:_.symbol)||"",""].join(" ").toLowerCase().includes(a.toLowerCase()):!0}),b=((k=j.blacklistKeywords)==null?void 0:k.length)>0||a.length>0,y=s.length-m.length,d=()=>{u("")},v=()=>{h(!f)};return e.jsxs("div",{className:"offers-list-container",children:[e.jsxs("div",{className:"offers-list-header",children:[e.jsxs("div",{className:"relative",children:[e.jsx(Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search offers...",value:a,onChange:c=>u(c.target.value),className:`
              w-full pl-10 pr-10 py-3 bg-white/10 border border-white/20 rounded-xl
              text-white placeholder-gray-400 text-sm backdrop-blur-sm
              focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500
              transition-all duration-200
            `}),a&&e.jsx("button",{onClick:d,className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors",children:e.jsx(T,{className:"w-4 h-4"})})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[y>0&&e.jsxs("div",{className:"flex items-center space-x-2 bg-red-500/20 border border-red-500/30 rounded-xl px-4 py-2 backdrop-blur-sm",children:[e.jsx(L,{className:"w-3 h-3 text-red-400"}),e.jsxs("span",{className:"text-xs text-red-400 font-medium",children:[y," blocked"]}),e.jsx("button",{onClick:v,className:"text-xs text-red-300 hover:text-red-200 underline",children:f?"Hide":"Show"})]}),a&&e.jsxs("div",{className:"flex items-center space-x-2 bg-blue-500/20 border border-blue-500/30 rounded-xl px-4 py-2 backdrop-blur-sm",children:[e.jsx(Z,{className:"w-3 h-3 text-blue-400"}),e.jsxs("span",{className:"text-xs text-blue-400 font-medium",children:[p.length," found"]})]})]}),e.jsxs("div",{className:"text-xs text-gray-400",children:[p.length," of ",s.length," offers"]})]})]}),e.jsx("div",{className:"offers-list-scroll",children:p.length===0?e.jsxs("div",{className:"offers-empty-state",children:[e.jsx(fe,{className:"w-12 h-12 text-gray-500 mb-4"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-300 mb-2",children:s.length===0?"No offers available":"No matching offers"}),e.jsx("p",{className:"text-sm text-gray-500 max-w-sm",children:s.length===0?"Start monitoring to see offers appear here":b?"Try adjusting your search or filter criteria":"Check back later for new offers"}),(a||y>0)&&e.jsx("button",{onClick:()=>{u(""),h(!1)},className:"mt-4 px-6 py-3 bg-emerald-500 hover:bg-emerald-600 text-white text-sm rounded-xl transition-colors border border-emerald-400/30",children:"Clear filters"})]}):e.jsx("div",{className:"offers-list-items",children:(f?s.filter(c=>!m.includes(c)):p).map((c,C)=>e.jsx("div",{className:"offer-item-wrapper",style:{animationDelay:`${C*50}ms`},children:e.jsx(Ge,{offer:c,isSelected:(l==null?void 0:l.hash)===c.hash,onClick:()=>n(c),isBlacklisted:f&&!m.includes(c)})},c.hash))})}),p.length>0&&e.jsx("div",{className:"offers-list-footer",children:e.jsxs("div",{className:"flex items-center justify-between text-xs text-gray-400",children:[e.jsxs("span",{children:[b?`Filtered: ${p.length}`:`Total: ${s.length}`," offers"]}),e.jsx("span",{children:l?"Offer selected":"Select an offer to view details"})]})})]})},Ue=({settings:s,onSettingsUpdate:l,selectedOffer:n,onAcceptOffer:j,onRejectOffer:a})=>{var z,E;const[u,f]=g.useState(!1),[h,m]=g.useState(""),[o,p]=g.useState(!1),[b,y]=g.useState([]),[d,v]=g.useState(!1),k=()=>{l({...s,monitoring:!s.monitoring})},c=()=>{l({...s,autoAccept:!s.autoAccept})},C=()=>{l({...s,notificationsEnabled:!s.notificationsEnabled})},M=()=>{l({...s,soundEnabled:!s.soundEnabled})},r=()=>{h.trim()&&!s.blacklistKeywords.includes(h.trim())&&(d?(b.includes(h.trim())||y([...b,h.trim()]),m("")):(l({...s,blacklistKeywords:[...s.blacklistKeywords,h.trim()]}),m(""),p(!1)))},t=()=>{if(b.length>0){const x=b.filter(N=>!s.blacklistKeywords.includes(N));l({...s,blacklistKeywords:[...s.blacklistKeywords,...x]}),y([]),v(!1),m(""),p(!1)}},w=()=>{y([]),v(!1),m(""),p(!1)},_=x=>{y(b.filter(N=>N!==x))},$=x=>{l({...s,blacklistKeywords:s.blacklistKeywords.filter(N=>N!==x)})},S=x=>{x.key==="Enter"?d?h.trim()?r():t():h.trim()&&(v(!0),r()):x.key==="Escape"&&(d?w():(m(""),p(!1)))};return e.jsxs("div",{className:"control-panel animate-slide-up",children:[e.jsxs("div",{className:"control-section",children:[n&&e.jsxs("div",{className:"mb-3 p-3 glass-effect border border-emerald-200/60 rounded-xl animate-scale-in",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:"w-6 h-6 gradient-secondary rounded-lg flex items-center justify-center shadow-sm",children:e.jsx(P,{className:"w-3 h-3 text-white"})}),e.jsxs("div",{children:[e.jsx("span",{className:"text-xs font-bold text-slate-800",children:"Selected Offer"}),e.jsxs("div",{className:"text-xs text-slate-600 font-medium",children:[((z=n.currency)==null?void 0:z.symbol)||((E=n.walletCurrency)==null?void 0:E.symbol)||"N/A"," • ",n.operationType]})]})]}),e.jsx("div",{className:"badge badge-success text-xs",children:"Active"})]}),e.jsxs("div",{className:"control-grid",children:[e.jsxs("button",{onClick:()=>j(n.hash),className:"btn btn-success hover-lift text-sm py-2",children:[e.jsx(P,{className:"w-4 h-4"}),e.jsx("span",{children:"Accept"})]}),e.jsxs("button",{onClick:()=>a(n.hash),className:"btn btn-danger hover-lift text-sm py-2",children:[e.jsx(T,{className:"w-4 h-4"}),e.jsx("span",{children:"Reject"})]})]})]}),e.jsxs("div",{className:"control-grid mb-4",children:[e.jsxs("button",{onClick:k,className:`control-button hover-lift py-3 ${s.monitoring?"active":"inactive"}`,children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[s.monitoring?e.jsx(Ae,{className:"w-4 h-4"}):e.jsx(_e,{className:"w-4 h-4"}),e.jsx("span",{className:"font-semibold text-sm",children:s.monitoring?"Pause":"Start"})]}),s.monitoring&&e.jsx("div",{className:"status-online animate-pulse"})]}),e.jsxs("button",{onClick:c,className:`control-button hover-lift py-3 ${s.autoAccept?"active":"inactive"}`,children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(se,{className:`w-4 h-4 ${s.autoAccept?"animate-pulse":""}`}),e.jsx("span",{className:"font-semibold text-sm",children:"Auto Accept"})]}),s.autoAccept&&e.jsx("div",{className:"status-warning animate-pulse"})]})]})]}),e.jsx("div",{className:"control-section",children:e.jsxs("button",{onClick:()=>f(!u),className:"w-full btn btn-secondary hover-lift py-2",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:"w-6 h-6 gradient-primary rounded-lg flex items-center justify-center shadow-sm",children:e.jsx($e,{className:"w-3 h-3 text-white"})}),e.jsx("span",{className:"font-semibold text-sm",children:"Advanced Settings"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("div",{className:"badge badge-primary text-xs",children:[s.blacklistKeywords.length," filters"]}),u?e.jsx(pe,{className:"w-4 h-4"}):e.jsx(xe,{className:"w-4 h-4"})]})]})}),u&&e.jsxs("div",{className:"control-section animate-slide-down max-h-64 overflow-y-auto scrollbar-thin",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center space-x-2 mb-3",children:[e.jsx("div",{className:"w-6 h-6 bg-amber-500 rounded-lg flex items-center justify-center shadow-sm",children:e.jsx(Q,{className:"w-3 h-3 text-white"})}),e.jsx("h3",{className:"text-sm font-bold text-slate-800",children:"Notifications"})]}),e.jsxs("div",{className:"control-grid",children:[e.jsxs("button",{onClick:C,className:`control-button hover-lift py-2 ${s.notificationsEnabled?"active":"inactive"}`,children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[s.notificationsEnabled?e.jsx(Q,{className:"w-4 h-4"}):e.jsx(ie,{className:"w-4 h-4"}),e.jsx("span",{className:"font-semibold text-sm",children:"Alerts"})]}),s.notificationsEnabled&&e.jsx("div",{className:"status-warning animate-pulse"})]}),e.jsxs("button",{onClick:M,className:`control-button hover-lift py-2 ${s.soundEnabled?"active":"inactive"}`,children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[s.soundEnabled?e.jsx(Ke,{className:"w-4 h-4"}):e.jsx(He,{className:"w-4 h-4"}),e.jsx("span",{className:"font-semibold text-sm",children:"Sound"})]}),s.soundEnabled&&e.jsx("div",{className:"status-warning animate-pulse"})]})]})]}),e.jsxs("div",{className:"space-y-3 mt-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:"w-6 h-6 bg-red-500 rounded-lg flex items-center justify-center shadow-sm",children:e.jsx(L,{className:"w-3 h-3 text-white"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-bold text-slate-800",children:"Blacklist"}),e.jsxs("p",{className:"text-xs text-slate-600",children:[s.blacklistKeywords.length," keywords"]})]})]}),e.jsxs("button",{onClick:()=>p(!o),className:"btn btn-primary hover-lift text-sm py-1 px-3",children:[e.jsx(J,{className:"w-3 h-3"}),e.jsx("span",{children:"Add"})]})]}),o&&e.jsxs("div",{className:"space-y-4 animate-slide-down",children:[e.jsxs("div",{className:"flex space-x-3",children:[e.jsx("input",{type:"text",value:h,onChange:x=>m(x.target.value),onKeyDown:S,placeholder:d?"Add another keyword or press Enter to finish...":"Enter keyword...",className:`input-glass flex-1 ${d?"border-amber-400 focus:border-amber-500":"border-slate-200 focus:border-primary-500"}`,autoFocus:!0}),e.jsx("button",{onClick:r,disabled:!h.trim(),className:"btn btn-success hover-lift disabled:opacity-50 disabled:cursor-not-allowed",children:e.jsx(P,{className:"w-4 h-4"})}),d?e.jsx("button",{onClick:t,className:"bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2.5 rounded-xl transition-colors duration-200 shadow-sm",title:"Finish grouping",children:e.jsx(J,{className:"w-4 h-4"})}):null,e.jsx("button",{onClick:()=>{d?w():(m(""),p(!1))},className:"bg-slate-200 hover:bg-slate-300 text-slate-600 px-4 py-2.5 rounded-xl transition-colors duration-200 border border-slate-200 shadow-sm",children:e.jsx(T,{className:"w-4 h-4"})})]}),d&&e.jsxs("div",{className:"bg-yellow-50 border border-yellow-200 rounded-xl p-3",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("span",{className:"text-sm font-medium text-yellow-800",children:"Grouping Mode Active"}),e.jsxs("span",{className:"text-xs text-yellow-600",children:[b.length," keyword(s) in group"]})]}),e.jsx("p",{className:"text-xs text-yellow-700 mb-3",children:"Type keywords and press Enter to add them. Press Enter on empty input to finish grouping."}),b.length>0&&e.jsxs("div",{className:"space-y-1",children:[e.jsx("span",{className:"text-xs text-yellow-800 font-medium",children:"Keywords in group:"}),e.jsx("div",{className:"flex flex-wrap gap-1",children:b.map((x,N)=>e.jsxs("div",{className:"flex items-center space-x-1 bg-yellow-200 rounded-lg px-2 py-1 text-xs text-yellow-800",children:[e.jsx("span",{children:x}),e.jsx("button",{onClick:()=>_(x),className:"text-yellow-600 hover:text-yellow-800 transition-colors duration-200",children:e.jsx(T,{className:"w-3 h-3"})})]},N))})]})]})]}),s.blacklistKeywords.length>0&&e.jsx("div",{className:"max-h-32 overflow-y-auto space-y-2 custom-scrollbar-modern",children:s.blacklistKeywords.map((x,N)=>e.jsxs("div",{className:"flex items-center justify-between bg-white rounded-lg px-3 py-2 border border-slate-200 shadow-sm",children:[e.jsx("span",{className:"text-sm text-slate-700",children:x}),e.jsx("button",{onClick:()=>$(x),className:"text-red-500 hover:text-red-600 transition-colors duration-200 p-1 rounded hover:bg-red-50",children:e.jsx(Pe,{className:"w-3 h-3"})})]},N))}),s.blacklistKeywords.length===0&&e.jsx("div",{className:"text-center py-4 text-slate-500 text-sm",children:"No keywords added"})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("h3",{className:"text-sm font-semibold text-slate-700 flex items-center space-x-2",children:[e.jsx(L,{className:"w-4 h-4"}),e.jsx("span",{children:"Smart Matching"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-slate-600",children:"Enable fuzzy matching"}),e.jsx("button",{onClick:()=>l({...s,fuzzyMatching:{...s.fuzzyMatching,enabled:!s.fuzzyMatching.enabled}}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200 ${s.fuzzyMatching.enabled?"bg-emerald-500":"bg-slate-300"}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200 shadow-sm ${s.fuzzyMatching.enabled?"translate-x-6":"translate-x-1"}`})})]}),s.fuzzyMatching.enabled&&e.jsxs("div",{className:"space-y-3 bg-white rounded-xl p-4 border border-slate-200 shadow-sm",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-slate-600",children:"Match Threshold"}),e.jsx("span",{className:"text-xs text-emerald-600 font-mono bg-emerald-50 px-2 py-1 rounded",children:s.fuzzyMatching.threshold})]}),e.jsx("input",{type:"range",min:"0.1",max:"1.0",step:"0.1",value:s.fuzzyMatching.threshold,onChange:x=>l({...s,fuzzyMatching:{...s.fuzzyMatching,threshold:parseFloat(x.target.value)}}),className:"w-full h-2 bg-slate-200 rounded-lg appearance-none cursor-pointer slider"}),e.jsxs("div",{className:"flex justify-between text-xs text-slate-500",children:[e.jsx("span",{children:"Strict"}),e.jsx("span",{children:"Loose"})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-slate-600",children:"Use payment aliases"}),e.jsx("button",{onClick:()=>l({...s,fuzzyMatching:{...s.fuzzyMatching,enableAliases:!s.fuzzyMatching.enableAliases}}),className:`relative inline-flex h-5 w-9 items-center rounded-full transition-colors duration-200 ${s.fuzzyMatching.enableAliases?"bg-emerald-500":"bg-slate-300"}`,children:e.jsx("span",{className:`inline-block h-3 w-3 transform rounded-full bg-white transition-transform duration-200 shadow-sm ${s.fuzzyMatching.enableAliases?"translate-x-5":"translate-x-1"}`})})]}),e.jsxs("div",{className:"text-xs text-blue-700 bg-blue-50 border border-blue-200 rounded-lg p-2",children:[e.jsx("span",{className:"text-blue-800 font-medium",children:"💡 Tip:"})," Fuzzy matching helps catch AirTM payment methods with slight variations in naming."]})]})]}),e.jsx("div",{className:"pt-4 border-t border-slate-200/60",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-8 h-8 gradient-primary rounded-xl flex items-center justify-center shadow-sm",children:e.jsx(re,{className:"w-4 h-4 text-white"})}),e.jsxs("div",{children:[e.jsxs("span",{className:"text-sm font-bold text-slate-800",children:["Status: ",s.monitoring?"Active":"Paused"]}),e.jsx("div",{className:"text-xs text-slate-600",children:s.monitoring?"Real-time monitoring enabled":"Monitoring is stopped"})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:`status-dot ${s.monitoring?"status-online":"status-offline"} animate-pulse`}),e.jsx("span",{className:"text-sm font-semibold text-slate-700",children:s.monitoring?"Monitoring":"Stopped"})]})]})})]})]})},qe=({stats:s,filteredCount:l,isFiltered:n})=>{const j=[{label:"Total",value:s.total,icon:F,color:"text-blue-400",bgColor:"bg-blue-500/10",borderColor:"border-blue-500/20"},{label:"New",value:s.new,icon:we,color:"text-emerald-400",bgColor:"bg-emerald-500/10",borderColor:"border-emerald-500/20"},{label:"Accepted",value:s.accepted,icon:be,color:"text-green-400",bgColor:"bg-green-500/10",borderColor:"border-green-500/20"},{label:"Rejected",value:s.rejected,icon:Ne,color:"text-red-400",bgColor:"bg-red-500/10",borderColor:"border-red-500/20"}];return e.jsxs("div",{className:"space-y-4",children:[n&&e.jsxs("div",{className:"flex items-center justify-between bg-emerald-50/80 border border-emerald-200/60 rounded-xl px-4 py-3 backdrop-blur-sm",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-8 h-8 bg-emerald-500 rounded-lg flex items-center justify-center shadow-sm",children:e.jsx(L,{className:"w-4 h-4 text-white"})}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm font-semibold text-emerald-700",children:"Filter Active"}),e.jsx("p",{className:"text-xs text-emerald-600",children:"Custom criteria applied"})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"text-lg font-bold text-emerald-700",children:l}),e.jsxs("div",{className:"text-xs text-emerald-600",children:["of ",s.total," offers"]})]})]}),e.jsx("div",{className:"grid grid-cols-2 gap-4",children:j.map((a,u)=>{const f=a.icon;return e.jsxs("div",{className:"bg-white/60 backdrop-blur-sm border border-slate-200/60 rounded-xl p-4 hover:bg-white/80 hover:border-slate-300/60 transition-all duration-300 hover:shadow-lg hover:-translate-y-0.5 group",style:{animationDelay:`${u*100}ms`},children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsx("div",{className:`w-10 h-10 ${a.bgColor} rounded-xl flex items-center justify-center shadow-sm group-hover:scale-110 transition-transform duration-300`,children:e.jsx(f,{className:`w-5 h-5 ${a.color}`})}),e.jsx("div",{className:`w-3 h-3 ${a.color.replace("text-","bg-")} rounded-full opacity-60 group-hover:opacity-100 transition-opacity duration-300`})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:`text-2xl font-bold ${a.color} leading-none`,children:a.value.toLocaleString()}),e.jsx("div",{className:"text-sm text-slate-600 font-medium",children:a.label})]}),e.jsx("div",{className:"mt-3 h-2 bg-slate-200/60 rounded-full overflow-hidden",children:e.jsx("div",{className:`h-full ${a.color.replace("text-","bg-")} transition-all duration-1000 ease-out rounded-full`,style:{width:s.total>0?`${a.value/s.total*100}%`:"0%",animationDelay:`${u*200+500}ms`}})})]},a.label)})}),e.jsx("div",{className:"bg-slate-50/60 backdrop-blur-sm border border-slate-200/60 rounded-xl p-4",children:e.jsxs("div",{className:"flex items-center justify-between text-sm",children:[e.jsxs("div",{className:"flex items-center space-x-2 text-slate-600",children:[e.jsx(ee,{className:"w-4 h-4"}),e.jsxs("span",{className:"font-medium",children:["Last updated: ",new Date().toLocaleTimeString()]})]}),e.jsx("div",{className:"flex items-center space-x-4 text-slate-700",children:s.total>0&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-lg font-bold text-green-600",children:[Math.round(s.accepted/(s.accepted+s.rejected)*100)||0,"%"]}),e.jsx("div",{className:"text-xs text-slate-500",children:"Success Rate"})]}),e.jsx("div",{className:"w-px h-8 bg-slate-300"}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-lg font-bold text-blue-600",children:s.total-s.accepted-s.rejected}),e.jsx("div",{className:"text-xs text-slate-500",children:"Pending"})]})]})})]})})]})},Ye=()=>e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),Xe=()=>e.jsxs("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]}),Je=()=>e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})}),Qe=()=>e.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})});function Ze(){const[s,l]=g.useState([]),[n,j]=g.useState(null),[a,u]=g.useState(null),[f,h]=g.useState(!0),[m,o]=g.useState(null),[p,b]=g.useState(null);g.useEffect(()=>{y(),d()},[]);const y=async()=>{var r;h(!0);try{console.log("Popup: Loading initial data...");const t=await chrome.runtime.sendMessage({type:"GET_POPUP_DATA"});console.log("Popup: Received response:",t),t&&t.success&&t.data?(console.log("Popup: Setting offers:",((r=t.data.offers)==null?void 0:r.length)||0),console.log("Popup: Setting settings:",t.data.settings),console.log("Popup: Setting stats:",t.data.stats),l(t.data.offers||[]),j(t.data.settings||null),u(t.data.stats||{totalOffers:0,newOffers:0,averageRate:0})):(console.error("Popup: Invalid response from background:",t),o((t==null?void 0:t.error)||"Failed to load data from background script"))}catch(t){console.error("Popup: Error loading initial data:",t),o("Failed to communicate with extension")}finally{h(!1)}},d=()=>{const r=t=>{t.type==="OFFERS_UPDATED"&&(l(t.offers),u(t.stats))};return chrome.runtime.onMessage.addListener(r),()=>chrome.runtime.onMessage.removeListener(r)},v=async r=>{try{const t=await chrome.runtime.sendMessage({type:"ACCEPT_OFFER",offerId:r.id});t.success||o(t.error||"Failed to accept offer")}catch{o("Failed to accept offer")}},k=async r=>{try{const t=await chrome.runtime.sendMessage({type:"REJECT_OFFER",offerId:r.id});t.success||o(t.error||"Failed to reject offer")}catch{o("Failed to reject offer")}},c=async r=>{try{const t=await chrome.runtime.sendMessage({type:"UPDATE_SETTINGS",settings:r});t.success?j(r):o(t.error||"Failed to update settings")}catch{o("Failed to update settings")}},C=()=>{chrome.runtime.openOptionsPage()},M=()=>{y()};return f?e.jsxs("div",{className:"popup-container",children:[e.jsx("div",{className:"popup-background",children:e.jsx("div",{className:"popup-pattern"})}),e.jsx("div",{className:"popup-content",children:e.jsxs("div",{className:"loading-section",children:[e.jsxs("div",{className:"logo-container",children:[e.jsx("div",{className:"logo-icon",children:e.jsx("span",{className:"logo-text",children:"A"})}),e.jsx("div",{className:"logo-pulse"})]}),e.jsxs("div",{className:"spinner-container",children:[e.jsx("div",{className:"spinner-ring spinner-ring-1"}),e.jsx("div",{className:"spinner-ring spinner-ring-2"}),e.jsx("div",{className:"spinner-ring spinner-ring-3"})]}),e.jsxs("div",{className:"loading-text",children:[e.jsx("h2",{className:"app-title",children:"Airtm Monitor Pro"}),e.jsx("p",{className:"loading-message",children:"Initializing dashboard..."}),e.jsxs("div",{className:"loading-dots",children:[e.jsx("div",{className:"dot dot-1"}),e.jsx("div",{className:"dot dot-2"}),e.jsx("div",{className:"dot dot-3"})]})]})]})})]}):e.jsxs("div",{className:"popup-container",children:[e.jsx("div",{className:"popup-background",children:e.jsx("div",{className:"popup-pattern"})}),e.jsxs("div",{className:"popup-content",children:[e.jsx("header",{className:"popup-header",children:e.jsxs("div",{className:"header-content",children:[e.jsxs("div",{className:"header-brand",children:[e.jsxs("div",{className:"brand-logo",children:[e.jsx("div",{className:"logo-icon",children:e.jsx("span",{className:"logo-text",children:"A"})}),e.jsx("div",{className:`status-indicator ${n!=null&&n.monitoring?"status-active":"status-inactive"}`})]}),e.jsxs("div",{className:"brand-info",children:[e.jsx("h1",{className:"app-title",children:"Airtm Monitor Pro"}),e.jsx("p",{className:"app-subtitle",children:"Real-time Trading Dashboard"})]})]}),e.jsxs("div",{className:"header-actions",children:[e.jsxs("button",{onClick:M,className:"action-btn action-btn-secondary",title:"Refresh Data",children:[e.jsx(Ye,{}),e.jsx("span",{children:"Refresh"})]}),e.jsxs("button",{onClick:C,className:"action-btn action-btn-primary",title:"Open Settings",children:[e.jsx(Xe,{}),e.jsx("span",{children:"Settings"})]})]})]})}),m&&e.jsx("div",{className:"error-container",children:e.jsxs("div",{className:"error-alert",children:[e.jsxs("div",{className:"error-content",children:[e.jsx("div",{className:"error-icon",children:e.jsx(Qe,{})}),e.jsxs("div",{className:"error-text",children:[e.jsx("h4",{className:"error-title",children:"Error"}),e.jsx("p",{className:"error-message",children:m})]})]}),e.jsx("button",{onClick:()=>o(null),className:"error-close",title:"Dismiss Error",children:e.jsx(Je,{})})]})}),e.jsx("section",{className:"stats-section",children:e.jsx("div",{className:"stats-container",children:e.jsx(qe,{stats:a?{total:a.totalOffers,new:a.newOffers,accepted:a.acceptedOffers,rejected:a.rejectedOffers}:{total:0,new:0,accepted:0,rejected:0}})})}),e.jsx("main",{className:"main-content",children:e.jsxs("div",{className:"content-container",children:[e.jsxs("div",{className:"offers-header",children:[e.jsxs("div",{className:"offers-title",children:[e.jsx("div",{className:"title-icon",children:e.jsx("div",{className:"icon-shape"})}),e.jsx("h3",{className:"section-title",children:"Available Offers"})]}),e.jsxs("div",{className:"offers-meta",children:[e.jsxs("div",{className:"live-indicator",children:[e.jsx("div",{className:"pulse-dot"}),e.jsx("span",{className:"live-text",children:"Live Updates"})]}),e.jsxs("div",{className:"offers-count",children:[s.length," offers"]})]})]}),e.jsx("div",{className:"offers-content",children:e.jsx(We,{offers:s,selectedOffer:p,onOfferSelect:b,settings:n||{monitoring:!1,telegramBotToken:"",telegramChatId:"",webhookUrl:"",soundEnabled:!1,notificationsEnabled:!1,autoAccept:!1,minAmount:0,maxAmount:999999,preferredCurrencies:[],keywords:[],blacklistKeywords:[],paymentMethods:[],countries:[],hotkeys:{accept_offer:{key:"Enter",description:"Accept selected offer"},reject_offer:{key:"Delete",description:"Reject selected offer"},open_offer_details:{key:"Ctrl+Shift+D",description:"Open offer details"},cycle_offers:{key:"Ctrl+Shift+C",description:"Cycle offers"}},fuzzyMatching:{enabled:!1,threshold:.8,enableAliases:!0,customAliases:{}}}})})]})}),e.jsx("footer",{className:"control-section",children:e.jsx("div",{className:"control-container",children:e.jsx(Ue,{settings:n||{monitoring:!1,telegramBotToken:"",telegramChatId:"",webhookUrl:"",soundEnabled:!1,notificationsEnabled:!1,autoAccept:!1,minAmount:0,maxAmount:999999,preferredCurrencies:[],keywords:[],blacklistKeywords:[],paymentMethods:[],countries:[],hotkeys:{accept_offer:{key:"Enter",description:"Accept selected offer"},reject_offer:{key:"Delete",description:"Reject selected offer"},open_offer_details:{key:"Ctrl+Shift+D",description:"Open offer details"},cycle_offers:{key:"Ctrl+Shift+C",description:"Cycle offers"}},fuzzyMatching:{enabled:!1,threshold:.8,enableAliases:!0,customAliases:{}}},onSettingsUpdate:c,onAcceptOffer:r=>{const t=s.find(w=>w.id===r);t&&v(t)},onRejectOffer:r=>{const t=s.find(w=>w.id===r);t&&k(t)},selectedOffer:p})})})]})]})}function D(){const s=document.documentElement,l=document.body,n=`
    width: 600px !important;
    height: 850px !important;
    min-width: 600px !important;
    min-height: 850px !important;
    max-width: 600px !important;
    max-height: 850px !important;
    margin: 0 !important;
    padding: 0 !important;
    overflow: hidden !important;
  `;s.style.cssText=n,l.style.cssText=n,l.className="popup",console.log("Popup dimensions enforced:",{htmlSize:`${s.offsetWidth}x${s.offsetHeight}`,bodySize:`${l.offsetWidth}x${l.offsetHeight}`,windowSize:`${window.innerWidth}x${window.innerHeight}`})}D();document.addEventListener("DOMContentLoaded",D);window.addEventListener("load",D);const O=document.getElementById("root");if(!O)throw new Error("Root element not found");O.style.cssText=`
  width: 600px !important;
  height: 850px !important;
  min-width: 600px !important;
  min-height: 850px !important;
  max-width: 600px !important;
  max-height: 850px !important;
  display: flex !important;
  flex-direction: column !important;
`;const es=te(O);es.render(e.jsx(Ze,{}));
