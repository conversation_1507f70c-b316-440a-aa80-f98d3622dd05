var X=Object.defineProperty;var ee=(e,t,r)=>t in e?X(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r;var $=(e,t,r)=>(ee(e,typeof t!="symbol"?t+"":t,r),r);import{i as W,a as j}from"./extension-context-ed07ef7d.js";const v={monitoring:!1,telegramBotToken:"",telegramChatId:"",webhookUrl:"",soundEnabled:!0,notificationsEnabled:!0,autoAccept:!1,minAmount:0,maxAmount:1e4,preferredCurrencies:["USD","EUR"],keywords:[],blacklistKeywords:[],paymentMethods:[],countries:[],fuzzyMatching:{enabled:!0,threshold:.7,enableAliases:!0,customAliases:{}},hotkeys:{accept_offer:{key:"Ctrl+Shift+A",description:"Accept offer"},reject_offer:{key:"Ctrl+Shift+R",description:"Reject offer"},open_offer_details:{key:"Ctrl+Shift+D",description:"Open details"},cycle_offers:{key:"Ctrl+Shift+C",description:"Cycle offers"}}},S=class S{constructor(){$(this,"activeNotifications",new Map);$(this,"settings",{autoCloseDelay:3e4,playSound:!0})}static getInstance(){return S.instance||(S.instance=new S),S.instance}updateSettings(t){this.settings={...this.settings,...t}}async showNotification(t,r){const o={...this.settings,...r};try{await this.showChromeNotification(t,o),await this.highlightOfferOnPage(t),o.playSound&&await this.playNotificationSound()}catch(n){console.error("Error showing notification:",n)}}async showChromeNotification(t,r){if(typeof chrome>"u"||!chrome.notifications)throw new Error("Chrome notifications not available");const o=`offer_${t.id}_${Date.now()}`,n=`New ${t.operationType} Offer`,a=`${t.grossAmount} ${t.currency?.symbol||t.walletCurrency?.symbol}`;await chrome.notifications.create(o,{type:"basic",iconUrl:"icons/icon48.png",title:n,message:a,contextMessage:t.peer?`From: ${t.peer.firstName} ${t.peer.lastName}`:void 0,buttons:[{title:"Accept"},{title:"Ignore"}],requireInteraction:!0}),this.activeNotifications.set(t.id,o),setTimeout(()=>{this.closeNotification(t.id)},r.autoCloseDelay)}async highlightOfferOnPage(t){try{const r=await chrome.tabs.query({active:!0,currentWindow:!0});r[0]?.id&&await chrome.tabs.sendMessage(r[0].id,{type:"HIGHLIGHT_OFFER",data:{offer:t}})}catch(r){console.log("Could not highlight offer on page:",r)}}closeNotification(t){const r=this.activeNotifications.get(t);if(r){try{typeof r=="string"&&r.startsWith("offer_")&&chrome.notifications.clear(r)}catch(o){console.error("Error closing notification:",o)}this.activeNotifications.delete(t)}}closeAllNotifications(){for(const[t]of this.activeNotifications)this.closeNotification(t)}async playNotificationSound(){try{if(typeof chrome<"u"&&chrome.offscreen){try{await chrome.offscreen.createDocument({url:"sounds/beep.html",reasons:["AUDIO_PLAYBACK"],justification:"Play notification sound"})}catch(t){if(!t.message?.includes("Only a single offscreen document"))throw t}chrome.runtime.sendMessage({type:"PLAY_SOUND"})}}catch(t){console.error("Error playing notification sound:",t)}}handleMessage(t,r){switch(t.type){case"ACCEPT_OFFER":this.handleOfferAction("accept",t.data?.offerId);break;case"IGNORE_OFFER":this.handleOfferAction("ignore",t.data?.offerId);break}}async handleOfferAction(t,r){if(r)try{if(this.closeNotification(r),typeof chrome<"u"&&chrome.runtime){const o=t==="accept"?"ACCEPT_OFFER":"REJECT_OFFER";await chrome.runtime.sendMessage({type:o,data:{offerId:r}})}}catch(o){console.error(`Error handling ${t} action:`,o)}}static isSupported(){return typeof chrome<"u"&&!!chrome.notifications}getAvailableMethods(){const t=[];return typeof chrome<"u"&&chrome.notifications&&t.push("chrome"),t}};$(S,"instance");let k=S;const M=k.getInstance();class te{constructor(t={}){$(this,"config");$(this,"defaultAliases",{paypal:["paypal","pp","pay pal"],zelle:["zelle","zell","zele"],venmo:["venmo","venm","vemo"],cashapp:["cashapp","cash app","cash-app","$cashtag"],wise:["wise","transferwise","transfer wise"],revolut:["revolut","revolt","revol"],skrill:["skrill","skril","skrilla"],neteller:["neteller","neteler","netelr"],perfectmoney:["perfectmoney","perfect money","pm","perfect-money"],webmoney:["webmoney","web money","wm","web-money"],bitcoin:["bitcoin","btc","bit coin"],ethereum:["ethereum","eth","ether"],usdt:["usdt","tether","usd-t"],binance:["binance","bnb","binance pay"],bank:["bank","banking","bank transfer","wire"],credit:["credit","credit card","cc","visa","mastercard"],debit:["debit","debit card","dc"]});this.config={threshold:.7,enableAliases:!0,customAliases:{},...t}}normalizePaymentMethod(t){return t?t.replace(/^CATEGORY_TREE:AIRTM_/i,"").replace(/^E_TRANSFER_/i,"").replace(/^AIRTM_/i,"").replace(/[_-]/g," ").toLowerCase().trim().replace(/\s+/g," "):""}levenshteinDistance(t,r){const o=Array(r.length+1).fill(null).map(()=>Array(t.length+1).fill(null));for(let n=0;n<=t.length;n++)o[0][n]=n;for(let n=0;n<=r.length;n++)o[n][0]=n;for(let n=1;n<=r.length;n++)for(let a=1;a<=t.length;a++){const i=t[a-1]===r[n-1]?0:1;o[n][a]=Math.min(o[n][a-1]+1,o[n-1][a]+1,o[n-1][a-1]+i)}return o[r.length][t.length]}calculateSimilarity(t,r){if(t===r)return 1;if(!t||!r)return 0;const o=Math.max(t.length,r.length);return 1-this.levenshteinDistance(t,r)/o}wordBasedMatch(t,r){const o=t.split(" ").filter(i=>i.length>0),n=r.split(" ").filter(i=>i.length>0);if(n.length===0)return 0;let a=0;for(const i of n)o.some(c=>c.includes(i)||i.includes(c)||this.calculateSimilarity(c,i)>.8)&&a++;return a/n.length}getAliases(t){const r=this.normalizePaymentMethod(t),o=[r];if(this.config.enableAliases){for(const[,n]of Object.entries(this.config.customAliases))if(n.some(a=>this.normalizePaymentMethod(a)===r)){o.push(...n.map(a=>this.normalizePaymentMethod(a)));break}for(const[,n]of Object.entries(this.defaultAliases))if(n.some(a=>this.normalizePaymentMethod(a)===r)){o.push(...n.map(a=>this.normalizePaymentMethod(a)));break}}return[...new Set(o)]}matchPaymentMethod(t,r){const o=this.normalizePaymentMethod(t),n=this.getAliases(r);let a=0,i="";for(const c of n){if(o===c)return{isMatch:!0,score:1,matchedAlias:c};if(o.includes(c)||c.includes(o)){const u=Math.max(c.length/o.length,o.length/c.length)*.9;u>a&&(a=u,i=c)}const p=this.wordBasedMatch(o,c)*.85;p>a&&(a=p,i=c);const g=this.calculateSimilarity(o,c)*.8;g>a&&(a=g,i=c)}return{isMatch:a>=this.config.threshold,score:a,matchedAlias:a>=this.config.threshold?i:void 0}}matchAnyPaymentMethod(t,r){let o={isMatch:!1,score:0};for(const n of r){const a=this.matchPaymentMethod(t,n);if(a.score>o.score&&(o=a),a.score===1)break}return o}updateConfig(t){this.config={...this.config,...t}}getConfig(){return{...this.config}}}let s=v;const K=new te,h={totalOffers:0,newOffers:0,acceptedOffers:0,rejectedOffers:0,averageRate:0,lastUpdate:new Date().toISOString()};let _=new Map,z=Date.now();const oe=2*60*1e3,re=60*60*1e3;async function ne(){try{const e=await chrome.storage.local.get("notified_offers");e.notified_offers?(_=new Map(Object.entries(e.notified_offers)),console.log(`Loaded ${_.size} notified offers from storage`)):(_=new Map,console.log("No previously notified offers found in storage"))}catch(e){console.error("Error loading notified offers:",e),_=new Map}}async function Z(){try{const e=Object.fromEntries(_);await chrome.storage.local.set({notified_offers:e})}catch(e){console.error("Error saving notified offers:",e)}}let Y=!1;async function F(){if(Y){console.log("Background service worker already initialized, skipping...");return}console.log("Airtm Monitor Pro: Background service worker initializing...");try{if(typeof chrome>"u"||!chrome.runtime)throw new Error("Chrome runtime APIs not available");await ne(),await Re(),ae(),$e(),Pe(),await Ie(),q(),Y=!0,console.log("Background service worker initialized successfully"),ye()}catch(e){console.error("Error initializing background service worker:",e);try{chrome.action?.setBadgeText({text:"!"}),chrome.action?.setBadgeBackgroundColor({color:"#ff0000"})}catch(t){console.error("Could not set error badge:",t)}}}function ae(){chrome.runtime.onMessage.hasListeners()&&chrome.runtime.onMessage.removeListener,chrome.runtime.onMessage.addListener((e,t,r)=>{if(console.log("Background received message:",e.type,"from:",t.tab?.url||"popup/options"),!e||typeof e.type!="string"){console.error("Invalid message received:",e),r({success:!1,error:"Invalid message format"});return}try{switch(e.type){case"OFFERS_UPDATE":return de(e.data).then(()=>r({success:!0})).catch(o=>{console.error("Error handling offers update:",o),r({success:!1,error:o.message})}),!0;case"SETTINGS_UPDATE":return Ne(e.data).then(()=>r({success:!0})).catch(o=>{console.error("Error handling settings update:",o),r({success:!1,error:o.message})}),!0;case"ACCEPT_OFFER":return x(e.data?.offerId).then(o=>r({success:!0,result:o})).catch(o=>{console.error("Error handling accept offer:",o),r({success:!1,error:o.message})}),!0;case"REJECT_OFFER":return V(e.data?.offerId).then(o=>r({success:!0,result:o})).catch(o=>{console.error("Error handling reject offer:",o),r({success:!1,error:o.message})}),!0;case"OPERATION_DETAILS_UPDATE":return we(e.data).then(()=>r({success:!0})).catch(o=>{console.error("Error handling operation details update:",o),r({success:!1,error:o.message})}),!0;case"OPERATION_ACCEPTED":return Ee(e.data).then(()=>r({success:!0})).catch(o=>{console.error("Error handling operation accepted:",o),r({success:!1,error:o.message})}),!0;case"OPERATION_NOT_AVAILABLE":return _e(e.data).then(()=>r({success:!0})).catch(o=>{console.error("Error handling operation not available:",o),r({success:!1,error:o.message})}),!0;case"OPERATION_ACCEPT_ERROR":return Te(e.data).then(()=>r({success:!0})).catch(o=>{console.error("Error handling operation accept error:",o),r({success:!1,error:o.message})}),!0;case"SEND_TELEGRAM_OPERATION_UPDATE":return Se(e.data).then(()=>r({success:!0})).catch(o=>{console.error("Error sending Telegram operation update:",o),r({success:!1,error:o.message})}),!0;case"OPERATION_DECLINED":return Oe(e.data).then(()=>r({success:!0})).catch(o=>{console.error("Error handling operation declined:",o),r({success:!1,error:o.message})}),!0;case"OPERATION_DECLINE_ERROR":return be(e.data).then(()=>r({success:!0})).catch(o=>{console.error("Error handling operation decline error:",o),r({success:!1,error:o.message})}),!0;case"GET_POPUP_DATA":return se().then(o=>r({success:!0,data:o})).catch(o=>{console.error("Error handling get popup data:",o),r({success:!1,error:o.message})}),!0;case"PING":console.log("Ping received, responding with alive status"),r({success:!0,message:"Background script is alive",timestamp:new Date().toISOString()});break;default:console.warn("Unknown message type:",e.type),r({success:!1,error:"Unknown message type: "+e.type})}}catch(o){console.error("Error in message listener:",o),r({success:!1,error:"Internal error: "+(o instanceof Error?o.message:"Unknown error")})}}),chrome.runtime.onConnect.addListener(e=>{console.log("Port connected:",e.name),e.onDisconnect.addListener(()=>{console.log("Port disconnected:",e.name),chrome.runtime.lastError&&console.error("Port disconnect error:",chrome.runtime.lastError)})}),chrome.commands&&chrome.commands.onCommand&&chrome.commands.onCommand.addListener(e=>{console.log("Chrome command received:",e),chrome.tabs.query({url:"*://app.airtm.com/peer-transfers/available*"},t=>{t.length>0&&t[0].id?chrome.tabs.sendMessage(t[0].id,{type:"EXECUTE_COMMAND",command:e}).catch(r=>{console.log("Could not send command to content script:",r)}):console.log("No active Airtm tab found for command:",e)})})}async function se(){try{const t=(await chrome.storage.local.get("current_offers")).current_offers||[];return console.log("Sending popup data:",{offersCount:t.length,settings:s,stats:h}),{offers:t,settings:s,stats:h}}catch(e){throw console.error("Error getting popup data:",e),e}}function ie(){const e=Date.now();if(e-z<oe)return;const t=e-re;let r=0;for(const[o,n]of _.entries())n<t&&(_.delete(o),r++);z=e,r>0&&(console.log(`🧹 Cleaned up ${r} old notified offers (older than 1 hour)`),Z())}function ce(e){return _.has(e)}function le(e){_.set(e,Date.now()),Z()}async function de(e){try{const{offers:t,timestamp:r}=e;if(!Array.isArray(t))throw new Error("Invalid offers data");if(console.log("Processing "+t.length+" offers"),await chrome.storage.local.set({current_offers:t}),ie(),!s.monitoring){console.log("Monitoring disabled, skipping offer processing"),G(t),h.lastUpdate=r||new Date().toISOString();try{chrome.runtime.sendMessage({type:"OFFERS_PROCESSED",data:{offers:t,stats:h}}).catch(a=>{console.log("Could not notify popup (popup may be closed):",a.message)})}catch(a){console.log("Error sending OFFERS_PROCESSED message:",a)}return}G(t);const o=ue(t);console.log(o.length+" offers passed filters");const n=o.filter(a=>!ce(a.id));console.log(n.length+" new offers (not previously notified)"),n.length>0&&s.soundEnabled&&s.notificationsEnabled&&(console.log("Playing sound notification once for "+n.length+" new offers"),await J());for(const a of n)le(a.id),await ge(a,!1);h.lastUpdate=r||new Date().toISOString();try{chrome.runtime.sendMessage({type:"OFFERS_PROCESSED",data:{offers:t,stats:h}}).catch(a=>{console.log("Could not notify popup (popup may be closed):",a.message)})}catch(a){console.log("Error sending OFFERS_PROCESSED message:",a)}}catch(t){console.error("Error handling offers update:",t)}}function ue(e){return e.filter(t=>{const r=typeof t.grossAmount=="string"?parseFloat(t.grossAmount):t.grossAmount;if(r<s.minAmount||r>s.maxAmount)return!1;if(s.preferredCurrencies.length>0){const o=t.currency?.symbol||t.walletCurrency?.symbol||"";if(!s.preferredCurrencies.includes(o))return!1}if(s.paymentMethods.length>0){const o=t.makerPaymentMethod?.version?.category?.translationTag||"";if(s.fuzzyMatching.enabled){const n=K.matchAnyPaymentMethod(o,s.paymentMethods);if(n.isMatch)console.log(`Offer ${t.id} matched payment method "${o}" with alias "${n.matchedAlias}" (score: ${n.score.toFixed(2)})`);else return console.log(`Offer ${t.id} filtered out: payment method "${o}" doesn't match any configured methods (best score: ${n.score.toFixed(2)})`),!1}else{let n=o;if(n.startsWith("CATEGORY_TREE:AIRTM_")&&(n=n.replace("CATEGORY_TREE:AIRTM_","")),n.startsWith("E_TRANSFER_")&&(n=n.replace("E_TRANSFER_","")),!s.paymentMethods.some(a=>n.includes(a)))return console.log(`Offer ${t.id} filtered out: payment method "${n}" doesn't match any configured methods (legacy matching)`),!1}}if(s.countries.length>0){const o=t.peer?.country||"";if(!s.countries.includes(o))return!1}if(s.keywords.length>0){const o=JSON.stringify(t).toLowerCase();if(!s.keywords.some(n=>o.includes(n.toLowerCase())))return!1}if(s.blacklistKeywords.length>0){const o=JSON.stringify(t).toLowerCase();if(s.blacklistKeywords.some(n=>o.includes(n.toLowerCase())))return console.log(`Offer ${t.id} filtered out by blacklist keyword`),!1}return!0})}async function me(e){try{const t=await chrome.tabs.query({url:"*://app.airtm.com/peer-transfers/available*"});if(t.length===0){console.log("No Airtm tab found, opening new tab");const o=await chrome.tabs.create({url:"https://app.airtm.com/peer-transfers/available",active:!0});if(await new Promise(n=>setTimeout(n,3e3)),o.windowId)try{await chrome.windows.update(o.windowId,{focused:!0,state:"maximized"}),console.log("✅ New window maximized and focused")}catch(n){console.warn("⚠️ Failed to maximize new window:",n);try{await chrome.windows.update(o.windowId,{focused:!0}),console.log("✅ New window focused (without maximizing)")}catch(a){console.error("❌ Failed to focus new window:",a)}}return}const r=t[0];if(r.windowId){try{await chrome.windows.update(r.windowId,{focused:!0,state:"maximized"}),console.log("✅ Window maximized and focused for offer:",e.id)}catch(n){console.warn("⚠️ Failed to maximize window:",n);try{await chrome.windows.update(r.windowId,{focused:!0}),console.log("✅ Window focused (without maximizing) for offer:",e.id)}catch(a){console.error("❌ Failed to focus window:",a)}}let o=!1;for(let n=1;n<=3;n++)try{await chrome.tabs.update(r.id,{active:!0}),console.log(`✅ Tab activated (attempt ${n}) for offer:`,e.id),o=!0;break}catch(a){console.warn(`⚠️ Tab activation attempt ${n} failed:`,a),n<3&&await new Promise(i=>setTimeout(i,500))}o||console.error("❌ Failed to activate tab after 3 attempts"),await new Promise(n=>setTimeout(n,1e3));try{await chrome.tabs.sendMessage(r.id,{type:"FOCUS_OFFER",data:{offerId:e.id}}),console.log("✅ Focus message sent to content script for offer:",e.id)}catch(n){console.log("⚠️ Could not send focus message to content script:",n)}}else console.error("❌ No window ID found for Airtm tab")}catch(t){console.error("❌ Error maximizing window and focusing offer:",t)}}async function ge(e,t=!0){try{console.log("Processing offer "+e.id),await me(e),s.notificationsEnabled&&await fe(e,t),console.log("Checking Telegram settings:",{botToken:s.telegramBotToken?"***SET***":"NOT SET",chatId:s.telegramChatId?"***SET***":"NOT SET"}),s.telegramBotToken&&s.telegramChatId?(console.log("Sending Telegram message for offer:",e.id),await N(e)):console.log("Telegram not configured - skipping message"),s.webhookUrl?(console.log("Sending webhook message for offer:",e.id),await pe(e)):console.log("Webhook not configured - skipping webhook"),s.autoAccept&&await x(e.id)}catch(r){console.error("Error processing offer "+e.id+":",r)}}async function fe(e,t=!0){try{M.updateSettings({autoCloseDelay:3e4,playSound:s.soundEnabled&&t}),await M.showNotification(e)}catch(r){console.error("Error sending notification:",r);try{const o=R(e),n="New "+e.operationType+" Offer";let a=`${o.amount} ${o.currency}`;o.conversionNote&&(a+=` ${o.conversionNote}`),await chrome.notifications.create({type:"basic",iconUrl:"icons/icon48.png",title:n,message:a,contextMessage:"From: "+(e.peer?.firstName||"")+" "+(e.peer?.lastName||""),buttons:[{title:"Accept"},{title:"Reject"}]}),s.soundEnabled&&t&&await J()}catch(o){console.error("Fallback notification also failed:",o)}}}async function N(e){try{const t=typeof e=="string"?e:D(e),r="https://api.telegram.org/bot"+s.telegramBotToken+"/sendMessage";console.log("Sending Telegram message to:",s.telegramChatId),console.log("Message content:",t);const o=await fetch(r,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({chat_id:s.telegramChatId,text:t,parse_mode:"Markdown"})}),n=await o.json();o.ok?console.log("✅ Telegram message sent successfully:",n):console.error("❌ Telegram API error:",n)}catch(t){console.error("❌ Error sending Telegram message:",t)}}async function pe(e){try{const t={timestamp:new Date().toISOString(),offer:e,event:"new_offer",metadata:{extensionVersion:"1.0.0",source:"airtm-monitor-pro"}};console.log("Sending webhook to:",s.webhookUrl),console.log("Webhook payload:",t);const r=await fetch(s.webhookUrl,{method:"POST",headers:{"Content-Type":"application/json","User-Agent":"Airtm-Monitor-Pro/1.0.0"},body:JSON.stringify(t)});r.ok?console.log("✅ Webhook sent successfully:",r.status):console.error("❌ Webhook failed with status:",r.status,await r.text())}catch(t){console.error("❌ Error sending webhook:",t)}}function he(e){return{USA:"🇺🇸",US:"🇺🇸",IND:"🇮🇳",IN:"🇮🇳",GBR:"🇬🇧",GB:"🇬🇧",UK:"🇬🇧",CAN:"🇨🇦",CA:"🇨🇦",AUS:"🇦🇺",AU:"🇦🇺",DEU:"🇩🇪",DE:"🇩🇪",GER:"🇩🇪",FRA:"🇫🇷",FR:"🇫🇷",ESP:"🇪🇸",ES:"🇪🇸",ITA:"🇮🇹",IT:"🇮🇹",BRA:"🇧🇷",BR:"🇧🇷",MEX:"🇲🇽",MX:"🇲🇽",ARG:"🇦🇷",AR:"🇦🇷",COL:"🇨🇴",CO:"🇨🇴",VEN:"🇻🇪",VE:"🇻🇪",PER:"🇵🇪",PE:"🇵🇪",CHL:"🇨🇱",CL:"🇨🇱",URY:"🇺🇾",UY:"🇺🇾",ECU:"🇪🇨",EC:"🇪🇨",BOL:"🇧🇴",BO:"🇧🇴",PRY:"🇵🇾",PY:"🇵🇾",CHN:"🇨🇳",CN:"🇨🇳",JPN:"🇯🇵",JP:"🇯🇵",KOR:"🇰🇷",KR:"🇰🇷",RUS:"🇷🇺",RU:"🇷🇺",TUR:"🇹🇷",TR:"🇹🇷",EGY:"🇪🇬",EG:"🇪🇬",ZAF:"🇿🇦",ZA:"🇿🇦",NGA:"🇳🇬",NG:"🇳🇬",KEN:"🇰🇪",KE:"🇰🇪",GHA:"🇬🇭",GH:"🇬🇭",MAR:"🇲🇦",MA:"🇲🇦",TUN:"🇹🇳",TN:"🇹🇳",DZA:"🇩🇿",DZ:"🇩🇿",MOZ:"🇲🇿",MZ:"🇲🇿",PAN:"🇵🇦",PA:"🇵🇦",IDN:"🇮🇩",ID:"🇮🇩",THA:"🇹🇭",TH:"🇹🇭",VNM:"🇻🇳",VN:"🇻🇳",PHL:"🇵🇭",PH:"🇵🇭",MYS:"🇲🇾",MY:"🇲🇾",SGP:"🇸🇬",SG:"🇸🇬",PAK:"🇵🇰",PK:"🇵🇰",BGD:"🇧🇩",BD:"🇧🇩",LKA:"🇱🇰",LK:"🇱🇰",NPL:"🇳🇵",NP:"🇳🇵",AFG:"🇦🇫",AF:"🇦🇫",IRN:"🇮🇷",IR:"🇮🇷",IRQ:"🇮🇶",IQ:"🇮🇶",SAU:"🇸🇦",SA:"🇸🇦",ARE:"🇦🇪",AE:"🇦🇪",QAT:"🇶🇦",QA:"🇶🇦",KWT:"🇰🇼",KW:"🇰🇼",BHR:"🇧🇭",BH:"🇧🇭",OMN:"🇴🇲",OM:"🇴🇲",JOR:"🇯🇴",JO:"🇯🇴",LBN:"🇱🇧",LB:"🇱🇧",SYR:"🇸🇾",SY:"🇸🇾",ISR:"🇮🇱",IL:"🇮🇱",PSE:"🇵🇸",PS:"🇵🇸"}[e.toUpperCase()]||e}function H(e){const t=e.walletCurrency,r=e.currency,o=e.makerPaymentMethod||e.takerPaymentMethod,n=o?.categoryId?.toLowerCase().includes("usdc")||o?.version?.category?.translationTag?.toLowerCase().includes("usdc"),a=e.metadata?.walletCurrencyPrecision||t?.precision,i=e.metadata?.localCurrencyPrecision||r?.precision,c=a===6||i===6,p=e.metadata?.isForThirdPartyPaymentMethod===!1,g=o?.categoryId?.toLowerCase()||"",u=o?.version?.category?.translationTag?.toLowerCase()||"",T=g.includes("gift-card")||u.includes("gift_card"),l=g.includes("bank")||u.includes("bank"),d=g.includes("card")||u.includes("card"),m=g.includes("transfer")||u.includes("transfer"),f=T||l||d||m;let y=null,A=null;return e.operationType==="BUY"&&f?(y="withdrawal",A="wallet",{isUSDC:!1,usdcField:A,operationType:y}):e.operationType==="SELL"&&f?(y="deposit",A="wallet",{isUSDC:!1,usdcField:A,operationType:y}):n||c||p?(e.operationType==="SELL"&&a===6?(y="withdrawal",A="wallet"):e.operationType==="BUY"&&i===6?(y="deposit",A="local"):(a===6||i===6)&&(y="exchange",A=a===6?"wallet":"local"),{isUSDC:!0,usdcField:A,operationType:y}):{isUSDC:!1,usdcField:null,operationType:null}}function R(e){try{const t=H(e);let r=e.walletCurrency?.symbol||"$",o=e.currency?.symbol||e.walletCurrency?.symbol||"Unknown";if(t.isUSDC&&(t.usdcField==="wallet"?r="USDC":t.usdcField==="local"&&(o="USDC")),(r===o||!e.currency)&&!t.isUSDC){const d=parseFloat(e.grossAmount||"0");return{amount:w(d),currency:r}}const n=e.rateInfo;if(n&&n.fundsToSendTaker&&n.fundsToReceiveTaker){let d,m,f="";return e.operationType==="BUY"?(d=parseFloat(n.fundsToReceiveTaker),m=parseFloat(n.fundsToSendTaker),t.operationType==="deposit"&&(f="(USDC deposit)")):(d=parseFloat(n.fundsToSendTaker),m=parseFloat(n.fundsToReceiveTaker),t.operationType==="withdrawal"&&(f=`(withdrawal, fees: $${(m-d).toFixed(2)})`)),console.log(`Using rateInfo amounts for ${e.operationType}: ${m} ${r} ↔ ${d} ${o} (from rateInfo)${t.isUSDC?" [USDC operation]":""}`),t.operationType==="withdrawal"?{amount:w(m),currency:r,originalAmount:w(d),originalCurrency:o,exchangeRate:(d/m).toFixed(4),conversionNote:f,operationType:"withdrawal"}:{amount:w(d),currency:o,originalAmount:w(m),originalCurrency:r,exchangeRate:(d/m).toFixed(4),conversionNote:f,operationType:t.operationType}}let a,i=null,c="";e.netAmount&&parseFloat(e.netAmount)>0?a=parseFloat(e.netAmount):a=parseFloat(e.grossAmount||"0");const p=e.metadata,g=e.takerPaymentMethod;if(e.rate?(i=parseFloat(e.rate),c="operation.rate"):e.displayRate?.rate?(i=parseFloat(e.displayRate.rate),c="displayRate.rate"):n&&n.exchangeRate?(i=parseFloat(n.exchangeRate),c="rateInfo.exchangeRate"):p?.displayRateInfo?.exchangeRate?(i=parseFloat(p.displayRateInfo.exchangeRate),c="metadata.displayRateInfo.exchangeRate"):g?.rateInfo?.exchangeRate&&(i=parseFloat(g.rateInfo.exchangeRate),c="takerPaymentMethod.rateInfo.exchangeRate"),(!i||i<=0)&&t.isUSDC&&(i=1,c="usdc_parity_default"),!i||i<=0)return{amount:w(a),currency:r,conversionNote:"(rate pending)"};let u;const T=e.displayRate?.direction||"TO_LOCAL_CURRENCY";T==="TO_LOCAL_CURRENCY"?u=a*i:T==="FROM_LOCAL_CURRENCY"?u=a/i:(e.operationType,u=a*i),console.log(`Currency conversion: ${a} ${r} → ${u} ${o} (rate: ${i}, source: ${c}, direction: ${T})${t.isUSDC?" [USDC operation]":""}`);const l={amount:w(u),currency:o,originalAmount:w(a),originalCurrency:r,exchangeRate:i.toString()};return t.isUSDC&&(l.operationType=t.operationType,t.operationType==="withdrawal"?(l.amount=w(a),l.currency=r,l.originalAmount=w(u),l.originalCurrency=o,l.conversionNote=`(withdrawal to ${o})`):t.operationType==="deposit"?l.conversionNote=`(deposit from ${r})`:t.operationType==="exchange"&&(l.conversionNote="(USDC exchange)")),l}catch(t){console.error("Error in currency conversion:",t);const r=parseFloat(e.grossAmount||"0"),o=e.currency?.symbol||e.walletCurrency?.symbol||"Unknown";return{amount:w(r),currency:o,conversionNote:"(conversion error)"}}}function w(e){return e>=1e3?e.toLocaleString("en-US",{minimumFractionDigits:0,maximumFractionDigits:2}):e.toFixed(2).replace(/\.?0+$/,"")}function ye(){console.log("🧪 Testing currency conversion with afteraccepting.txt example...");const e={id:"0d77a20b-f2ab-4dd6-8924-9fd102a37652",hash:"F2ABQP4DD6DK8924",operationType:"SELL",status:"ACCEPTED",isMine:!1,createdAt:"2025-06-12T16:23:51.645Z",updatedAt:"2025-06-12T16:24:00.386Z",grossAmount:"11",netAmount:"10.38",metadata:{isForThirdPartyPaymentMethod:!0,localCurrencyPrecision:2,walletCurrencyPrecision:2},walletCurrency:{symbol:"$",id:"USD",name:"US Dollar",precision:2,__typename:"Catalogs__Currency"},currency:{symbol:"£",id:"EGP",name:"Egyptian Pound",precision:2,__typename:"Catalogs__Currency"},peer:{id:"90e670af-de7f-45ba-8de2-e2698558271d",firstName:"احمد",lastName:"محمد السيد عبدالحافظ",createdAt:"2022-04-23T18:23:27.468Z",country:"EGY",countryInfo:{id:"EGY",__typename:"Catalogs__Country"},numbers:{id:"90e670af-de7f-45ba-8de2-e2698558271d",score:4.98,completedOperations:162,__typename:"Numbers__User"},__typename:"Auth__OperationUser"},rate:"49.57",displayRate:{rate:"47.42396313364055299539",direction:"TO_LOCAL_CURRENCY",__typename:"Operations__DisplayRate"},rateInfo:{fundsToSendTaker:"514.55",fundsToReceiveTaker:"10.85",grossAmount:"545.28",netAmount:"514.55",exchangeRate:"49.5709"},__typename:"Operations__Sell"},t=R(e);console.log("📊 Currency Conversion Test Results:"),console.log(`   Input: ${e.grossAmount} ${e.walletCurrency?.symbol} (gross)`),console.log(`   Input: ${e.netAmount} ${e.walletCurrency?.symbol} (net)`),console.log("   Expected Local: 514.55 £"),console.log("   Expected Wallet: 10.85 $"),console.log(`   Actual Result: ${t.amount} ${t.currency}`),console.log(`   Original Amount: ${t.originalAmount} ${t.originalCurrency}`),console.log(`   Exchange Rate: ${t.exchangeRate}`),console.log(`   Conversion Note: ${t.conversionNote||"None"}`);const r=514.55,o=parseFloat(t.amount.replace(/,/g,"")),n=Math.abs(o-r)<.01;console.log(`✅ Test Result: ${n?"PASSED":"FAILED"}`),n||console.log(`   Expected: ${r}, Got: ${o}`);const a=D(e);console.log("📱 Telegram Message:"),console.log(a),console.log(`
🧪 Testing same-currency scenario (USD to USD)...`);const i={id:"055c2bee-d063-4ced-acac-27b623954fa5",hash:"D063YU4CEDVGACAC",operationType:"SELL",status:"CREATED",isMine:!1,createdAt:"2025-06-14T13:19:58.291Z",updatedAt:null,grossAmount:"62.16",netAmount:"58.87",metadata:{isForThirdPartyPaymentMethod:!0,localCurrencyPrecision:2,walletCurrencyPrecision:2},walletCurrency:{symbol:"$",id:"USD",name:"US Dollar",precision:2,__typename:"Catalogs__Currency"},currency:{symbol:"$",id:"USD",name:"US Dollar",precision:2,__typename:"Catalogs__Currency"},peer:{id:"8021567e-1d57-446e-ae65-ffe04f622241",firstName:"IRIS DANIELA",lastName:"S.",createdAt:"2025-03-17T00:20:06.523Z",country:"PAN",countryInfo:{id:"PAN",__typename:"Catalogs__Country"},numbers:{id:"8021567e-1d57-446e-ae65-ffe04f622241",score:4.47,completedOperations:32,__typename:"Numbers__User"},__typename:"Auth__OperationUser"},rate:"1",rateInfo:{fundsToReceiveTaker:"61.23",fundsToSendTaker:"58.86"},displayRate:{direction:"TO_WALLET_CURRENCY",rate:"1.04026503567787971458",__typename:"Operations__DisplayRate"},makerPaymentMethod:{version:{category:{translationTag:"CATEGORY_TREE:AIRTM_MOBILE_YAPPY"}}},__typename:"Operations__Sell"},c=R(i);console.log("📊 Same Currency Test Results:"),console.log("   Expected: 62.16 $ (no conversion)"),console.log(`   Actual Result: ${c.amount} ${c.currency}`),console.log(`   Original Amount: ${c.originalAmount||"None"}`),console.log(`   Conversion Note: ${c.conversionNote||"None"}`);const p=D(i);console.log("📱 Same Currency Telegram Message:"),console.log(p),console.log(`
🧪 Testing BUY operation scenario (CNY to USD)...`);const g={id:"8d0a8483-3ea0-43b9-9a93-c48f22abe919",hash:"3EA0YE43B9EC9A93",operationType:"BUY",status:"CREATED",isMine:!1,createdAt:"2025-06-14T13:49:43.184Z",updatedAt:null,grossAmount:"278.97",netAmount:"269.84",metadata:{isForThirdPartyPaymentMethod:null,localCurrencyPrecision:2,walletCurrencyPrecision:2},walletCurrency:{symbol:"$",id:"USD",name:"US Dollar",precision:2,__typename:"Catalogs__Currency"},currency:{symbol:"¥",id:"CNY",name:"Chinese Yuan",precision:2,__typename:"Catalogs__Currency"},peer:{id:"d37923bd-23c8-4f2c-99fc-15c665e090ea",firstName:"ALY ADRIANO",lastName:"S.",createdAt:"2025-02-11T17:11:19.413Z",country:"MOZ",countryInfo:{id:"MOZ",__typename:"Catalogs__Country"},numbers:{id:"d37923bd-23c8-4f2c-99fc-15c665e090ea",score:4.28,completedOperations:82,__typename:"Numbers__User"},__typename:"Auth__OperationUser"},rate:"7.1692",rateInfo:{fundsToReceiveTaker:"2000",fundsToSendTaker:"273.75",netAmount:"1934.55",grossAmount:"2000"},displayRate:{direction:"TO_LOCAL_CURRENCY",rate:"7.30593607305936073059",__typename:"Operations__DisplayRate"},makerPaymentMethod:{version:{category:{translationTag:"CATEGORY_TREE:AIRTM_E_TRANSFER_ALIPAY"}}},__typename:"Operations__Buy"},u=R(g);console.log("📊 BUY Operation Test Results:"),console.log("   Expected: 2,000 ¥ (273.75 $)"),console.log(`   Actual Result: ${u.amount} ${u.currency}`),console.log(`   Original Amount: ${u.originalAmount} ${u.originalCurrency}`),console.log(`   Exchange Rate: ${u.exchangeRate}`);const T=D(g);console.log("📱 BUY Operation Telegram Message:"),console.log(T),console.log(`
🧪 Testing USDC withdrawal scenario...`);const l={id:"withdrawal-001",hash:"WITHDRAWAL001",operationType:"SELL",status:"CREATED",isMine:!0,createdAt:"2025-06-14T15:30:00.000Z",updatedAt:null,grossAmount:"14.23",netAmount:"13.85",metadata:{isForThirdPartyPaymentMethod:!1,walletCurrencyPrecision:6,localCurrencyPrecision:2},walletCurrency:{symbol:"$",id:"USD",name:"US Dollar",precision:6,__typename:"Catalogs__Currency"},currency:{symbol:"$",id:"USD",name:"US Dollar",precision:2,__typename:"Catalogs__Currency"},peer:{id:"withdrawal-peer",firstName:"Bank",lastName:"Withdrawal",createdAt:"2025-01-01T00:00:00.000Z",country:"USA",countryInfo:{id:"USA",__typename:"Catalogs__Country"},numbers:{id:"withdrawal-peer",score:5,completedOperations:1e3,__typename:"Numbers__User"},__typename:"Auth__OperationUser"},rate:"1.0",rateInfo:{fundsToReceiveTaker:"14.23",fundsToSendTaker:"13.50",grossAmount:"14.23",netAmount:"13.85"},displayRate:{direction:"TO_LOCAL_CURRENCY",rate:"1.0",__typename:"Operations__DisplayRate"},makerPaymentMethod:{categoryId:"airtm:bank:withdrawal",version:{category:{translationTag:"CATEGORY_TREE:AIRTM_BANK_WITHDRAWAL"}}},__typename:"Operations__Sell"},d=R(l);console.log("📊 USDC Withdrawal Test Results:"),console.log("   Expected: -14.23 USDC → 13.50 USD (fees: $0.73)"),console.log(`   Actual Result: ${d.amount} ${d.currency}`),console.log(`   Original Amount: ${d.originalAmount} ${d.originalCurrency}`),console.log(`   Operation Type: ${d.operationType}`),console.log(`   Conversion Note: ${d.conversionNote||"None"}`);const m=D(l);console.log("📱 USDC Withdrawal Telegram Message:"),console.log(m),console.log(`
🧪 Currency conversion tests completed.`)}function D(e){const t=R(e),r=H(e),o=e.makerPaymentMethod?.version?.image?.urls?.logo||e.makerPaymentMethod?.version?.image?.urls?.medium||"",n=e.peer?.preferences?.profile?.avatar||"",a=e.peer?.countryInfo?.image?.urls?.avatar||"",i=((e.peer?.firstName||"")+" "+(e.peer?.lastName||"")).trim(),c=e.peer?.numbers?.score||0,p=e.peer?.numbers?.completedOperations||0,g=e.peer?.country||"Unknown",u=he(g);let l=e.makerPaymentMethod?.version?.category?.translationTag||"Unknown";l.startsWith("CATEGORY_TREE:AIRTM_")&&(l=l.replace("CATEGORY_TREE:AIRTM_","")),l.startsWith("E_TRANSFER_")&&(l=l.replace("E_TRANSFER_","")),l.startsWith("GIFT_CARD_")&&(l=l.replace("GIFT_CARD_","")),l=l.replace(/_/g," ").toLowerCase().replace(/\b\w/g,b=>b.toUpperCase());const d=e.makerPaymentMethod?.categoryId||"";if(d.includes("gift-card")){const b=d.split(":");if(b.length>2){let E=b[b.length-1].replace(/[-_]/g," ").replace(/\b\w/g,I=>I.toUpperCase());E.toLowerCase()==="ebay"&&(E="eBay"),E.toLowerCase()==="paypal"&&(E="PayPal"),E.toLowerCase()==="amazon"&&(E="Amazon"),l=`${E} Gift Card`}}let m="";r.operationType==="withdrawal"?(m=`-${t.amount} ${t.currency}`,t.originalAmount&&t.originalCurrency&&(m+=` → ${t.originalAmount} ${t.originalCurrency}`)):(m=`${t.amount} ${t.currency}`,t.originalAmount&&t.originalCurrency&&t.originalCurrency!==t.currency&&(m+=` (${t.originalAmount} ${t.originalCurrency})`)),t.conversionNote&&(m+=` ${t.conversionNote}`);let f="";const y=d.includes("gift-card"),A=d.includes("bank")||l.includes("bank"),Q=d.includes("card")||l.includes("card");r.operationType==="withdrawal"?y?f="🎁 ":f="💸 ":r.operationType==="deposit"?A?f="🏦 ":Q?f="💳 ":f="💰 ":r.isUSDC&&(f="🪙 ");const L=new Date(e.createdAt).toLocaleString("en-US",{month:"short",day:"numeric",year:"numeric",hour:"numeric",minute:"2-digit",hour12:!0}),B=new Date(e.createdAt).toLocaleDateString("en-US",{month:"short",year:"numeric"});let C="";if(o&&(C+=`🖼️ [Service Logo](${o})
`),C+=`📋 ${l}

`,C+=`${f}${m} : ${l}

`,n?C+=`👤 [Avatar](${n}) `:C+="👤 ",C+=`${i} ${c}⭐ (${p} trades)
`,a?C+=`🏳️ [Flag](${a}) ${g} | ${B} | ${L}
`:C+=`${u} ${g} | ${B} | ${L}
`,e.displayRate?.rate){const b=parseFloat(e.displayRate.rate).toFixed(4),E=r.usdcField==="wallet"?"USDC":e.walletCurrency?.symbol||"$",I=r.usdcField==="local"?"USDC":e.currency?.symbol||"$";C+=`💱 $1 ${E} = $${b} ${I}`}return C}let O=!1,P=null;async function J(){return P||(P=(async()=>{try{if(!O)try{await chrome.offscreen.createDocument({url:"sounds/beep.html",reasons:[chrome.offscreen.Reason.AUDIO_PLAYBACK],justification:"Play notification sound for new offers"}),O=!0}catch(e){if(e.message?.includes("Only a single offscreen document may be created"))O=!0;else throw e}try{await chrome.runtime.sendMessage({type:"PLAY_SOUND"})}catch(e){throw console.log("Message to offscreen document failed:",e),O=!1,e}}catch(e){console.error("Error playing notification sound:",e),O=!1}finally{try{O&&(await chrome.offscreen.closeDocument(),O=!1)}catch{O=!1}P=null}})(),P)}async function x(e){try{if(!W())throw console.warn("⚠️ Extension context invalid during offer acceptance, cannot proceed"),new Error("Extension context is invalid. Please reload the extension.");const t=await chrome.tabs.query({url:"*://app.airtm.com/peer-transfers/available*"});if(t.length===0)throw new Error("Airtm tab not found");const r=await chrome.tabs.sendMessage(t[0].id,{type:"ACCEPT_OFFER",data:{offerId:e}});if(r?.success)return h.acceptedOffers++,console.log("Offer "+e+" accepted successfully"),Ae(e,t[0].id),!0;throw new Error(r?.error||"Failed to accept offer")}catch(t){if(console.error("Error accepting offer "+e+":",t),j(t)&&(console.warn("⚠️ Connection error detected, likely due to extension context invalidation"),s.telegramBotToken&&s.telegramChatId))try{await N(`⚠️ Extension Context Issue

Failed to accept offer ${e}
Reason: Extension context lost

Please reload the extension or refresh the page.`)}catch(r){console.error("Failed to send Telegram notification about context issue:",r)}throw t}}async function we(e){try{console.log("📋 Processing operation details update:",e);const{operation:t,timestamp:r,source:o}=e;if(!t||!t.id){console.warn("Invalid operation data received");return}await chrome.storage.local.set({[`operation_${t.id}`]:{...t,lastUpdated:r,source:o}}),s.telegramBotToken&&s.telegramChatId&&await Ce(t),console.log(`✅ Operation ${t.id} details updated - Status: ${t.status}`)}catch(t){console.error("❌ Error handling operation details update:",t)}}function Ae(e,t){console.log(`🔍 Starting URL monitoring for accepted offer: ${e}`);let r=0;const o=30,n=setInterval(async()=>{try{r++;const i=(await chrome.tabs.get(t)).url;if(console.log(`🔍 URL check ${r}/${o}: ${i}`),i&&/https:\/\/app\.airtm\.com\/operations\/[A-Z0-9]+/.test(i)){console.log("✅ Operation URL detected:",i);const p=i.split("/operations/")[1];s.telegramBotToken&&s.telegramChatId&&await N(`🎯 Offer ${e} accepted successfully!
📋 Operation ID: ${p}
🔗 URL: ${i}
⏰ Status: Accepted`),await chrome.storage.local.set({[`accepted_operation_${e}`]:{operationId:p,url:i,timestamp:new Date().toISOString(),status:"accepted"}}),clearInterval(n);return}r>=o&&(console.log("⚠️ Operation URL not detected within timeout"),s.telegramBotToken&&s.telegramChatId&&await N(`❌ Offer ${e} acceptance failed or timed out
⏰ Status: Not Available`),await chrome.storage.local.set({[`failed_operation_${e}`]:{timestamp:new Date().toISOString(),status:"not_available",reason:"url_not_detected"}}),clearInterval(n))}catch(a){console.error("❌ Error during URL monitoring:",a),clearInterval(n)}},100)}async function Ce(e){try{const t=e.metadata?.displayRateInfo,r={id:e.id||"",hash:e.hash||"",operationType:e.operationType||"UNKNOWN",status:e.status||"UNKNOWN",isMine:e.isMine||!1,createdAt:e.createdAt||"",updatedAt:e.updatedAt||null,grossAmount:e.grossAmount||"0",netAmount:e.netAmount||"0",metadata:{...e.metadata,displayRateInfo:t},walletCurrency:e.walletCurrency||{symbol:"$"},peer:e.secondParty||e.peer||{firstName:"Unknown",lastName:""},rate:e.rate,displayRate:e.displayRate,rateInfo:{...e.rateInfo},currency:e.currency,makerPaymentMethod:e.makerPaymentMethod,__typename:e.__typename||"Operations__Unknown"},o=R(r),n=e.secondParty?`${e.secondParty.firstName||""} ${e.secondParty.lastName||""}`.trim():e.peer?`${e.peer.firstName||""} ${e.peer.lastName||""}`.trim():"Unknown";let a=`${o.amount} ${o.currency}`;o.conversionNote&&(a+=` ${o.conversionNote}`),o.originalAmount&&o.originalCurrency&&o.originalCurrency!==o.currency&&(a+=` (${o.originalAmount} ${o.originalCurrency})`);const i=`📋 Operation Update
🆔 ID: ${e.hash||e.id}
📊 Status: ${e.status}
💰 Amount: ${a}
🔄 Type: ${e.operationType}
👤 Partner: ${n}
⏰ Updated: ${new Date().toLocaleString()}`;await N(i)}catch(t){console.error("❌ Error sending Telegram operation update:",t)}}async function Ee(e){try{console.log("✅ Processing operation acceptance:",e),await chrome.storage.local.set({[`accepted_operation_${e.operationId}`]:{...e,processedAt:new Date().toISOString()}}),h.acceptedOffers++,console.log("✅ Operation acceptance processed successfully")}catch(t){console.error("❌ Error handling operation accepted:",t)}}async function _e(e){try{console.log("🚫 Processing operation not available:",e),await chrome.storage.local.set({[`unavailable_operation_${e.operationId||"unknown"}`]:{...e,processedAt:new Date().toISOString()}}),console.log("🚫 Operation not available processed successfully")}catch(t){console.error("❌ Error handling operation not available:",t)}}async function Te(e){try{console.log("⚠️ Processing operation accept error:",e),await chrome.storage.local.set({[`error_operation_${Date.now()}`]:{...e,processedAt:new Date().toISOString()}}),console.log("⚠️ Operation accept error processed successfully")}catch(t){console.error("❌ Error handling operation accept error:",t)}}async function Oe(e){try{console.log("🚫 Processing operation decline:",e),await chrome.storage.local.set({[`declined_operation_${Date.now()}`]:{...e,processedAt:new Date().toISOString()}}),h.rejectedOffers++,console.log("🚫 Operation decline processed successfully")}catch(t){console.error("❌ Error handling operation declined:",t)}}async function be(e){try{console.log("⚠️ Processing operation decline error:",e),await chrome.storage.local.set({[`decline_error_operation_${Date.now()}`]:{...e,processedAt:new Date().toISOString()}}),console.log("⚠️ Operation decline error processed successfully")}catch(t){console.error("❌ Error handling operation decline error:",t)}}async function Se(e){try{console.log("📱 Sending Telegram operation status update:",e);const{operationId:t,status:r}=e;let o;r==="accepted"?o=`✅ *Operation Accepted*

🆔 Operation ID: ${t}
📊 Status: Successfully accepted
⏰ Time: ${new Date().toLocaleString()}

🎉 Great! The operation was accepted successfully.`:r==="Not Available"?o=`🚫 *Operation Not Available*

🆔 Operation ID: ${t}
📊 Status: No longer available
⏰ Time: ${new Date().toLocaleString()}

😔 The operation was taken by someone else or expired.`:o=`📋 *Operation Update*

🆔 Operation ID: ${t}
📊 Status: ${r}
⏰ Time: ${new Date().toLocaleString()}`,await N(o),console.log("📱 Telegram operation status update sent successfully")}catch(t){console.error("❌ Error sending Telegram operation status update:",t)}}async function V(e){try{if(!W())throw console.warn("⚠️ Extension context invalid during offer rejection, cannot proceed"),new Error("Extension context is invalid. Please reload the extension.");const t=await chrome.tabs.query({url:"*://app.airtm.com/peer-transfers/available*"});if(t.length===0)throw new Error("Airtm tab not found");const r=await chrome.tabs.sendMessage(t[0].id,{type:"REJECT_OFFER",data:{offerId:e}});if(r?.success)return h.rejectedOffers++,console.log("Offer "+e+" rejected successfully"),!0;throw new Error(r?.error||"Failed to reject offer")}catch(t){if(console.error("Error rejecting offer "+e+":",t),j(t)&&(console.warn("⚠️ Connection error detected, likely due to extension context invalidation"),s.telegramBotToken&&s.telegramChatId))try{await N(`⚠️ Extension Context Issue

Failed to reject offer ${e}
Reason: Extension context lost

Please reload the extension or refresh the page.`)}catch(r){console.error("Failed to send Telegram notification about context issue:",r)}throw t}}function G(e){h.totalOffers=e.length,h.newOffers=e.filter(t=>{const r=new Date(t.createdAt),o=new Date(Date.now()-5*60*1e3);return r>o}).length,h.averageRate=0}async function Re(){try{const e=await chrome.storage.sync.get("settings");s={...v,...e.settings},s.monitoring=!0,U(),await chrome.storage.sync.set({settings:s}),console.log("Settings loaded and monitoring enabled:",s)}catch(e){console.error("Error loading settings:",e),s={...v,monitoring:!0},U()}}async function Ne(e){try{s={...s,...e},e.fuzzyMatching&&U(),await chrome.storage.sync.set({settings:s}),console.log("Settings updated:",s),q()}catch(t){throw console.error("Error updating settings:",t),t}}function q(){s.monitoring?(chrome.action.setBadgeText({text:"ON"}),chrome.action.setBadgeBackgroundColor({color:"#10b981"})):(chrome.action.setBadgeText({text:"OFF"}),chrome.action.setBadgeBackgroundColor({color:"#6b7280"}))}function U(){try{const e=s.fuzzyMatching||{enabled:!1,threshold:.8,enableAliases:!0,customAliases:{}};K.updateConfig({threshold:e.threshold,enableAliases:e.enableAliases,customAliases:e.customAliases}),console.log("Fuzzy matcher configuration updated:",e)}catch(e){console.error("Error updating fuzzy matcher configuration:",e)}}function $e(){chrome.alarms.onAlarm.addListener(e=>{switch(console.log("Alarm triggered:",e.name),e.name){case"monitoring-check":break;case"cleanup-storage":De();break}}),chrome.alarms.create("cleanup-storage",{periodInMinutes:60})}function Pe(){chrome.notifications.onButtonClicked.addListener(async(e,t)=>{try{const r=e.match(/offer_(.+)_\d+/),o=r?r[1]:e;t===0?await x(o):t===1&&await V(o),chrome.notifications.clear(e)}catch(r){console.error("Error handling notification button click:",r)}}),chrome.runtime.onMessage.addListener((e,t,r)=>{(e.type==="ACCEPT_OFFER"||e.type==="REJECT_OFFER"||e.type==="IGNORE_OFFER")&&M.handleMessage(e,t)})}async function De(){try{console.log("Cleaning up storage...");const t=(await chrome.storage.local.get(["airtm_offers_timestamp"])).airtm_offers_timestamp;if(t){const r=Date.now()-new Date(t).getTime(),o=24*60*60*1e3;r>o&&(await chrome.storage.local.remove(["airtm_offers","airtm_offers_timestamp"]),console.log("Old offers cleaned up"))}}catch(e){console.error("Error cleaning up storage:",e)}}async function Ie(){try{console.log("🔍 Checking for existing Airtm tabs...");const e=await chrome.tabs.query({url:"*://app.airtm.com/peer-transfers/available*"});if(e.length===0){console.log("📭 No existing Airtm tabs found");return}console.log(`🎯 Found ${e.length} existing Airtm tab(s)`),console.log("ℹ️ Content scripts will be automatically injected by Chrome based on manifest.json");for(const t of e)t.id&&t.url&&console.log(`📋 Found Airtm tab ${t.id}: ${t.url}`)}catch(e){console.error("❌ Error checking existing tabs:",e)}}function ve(){const e=()=>{try{chrome?.runtime?.getPlatformInfo&&chrome.runtime.getPlatformInfo(()=>{chrome.runtime.lastError&&console.warn("Keep alive error:",chrome.runtime.lastError)})}catch(t){console.warn("Keep alive failed:",t)}};e(),setInterval(e,2e4)}F().then(()=>{ve()});chrome.runtime.onStartup.addListener(()=>{console.log("Service worker starting up..."),F()});chrome.runtime.onInstalled.addListener(e=>{console.log("Extension installed/updated:",e.reason),F()});
