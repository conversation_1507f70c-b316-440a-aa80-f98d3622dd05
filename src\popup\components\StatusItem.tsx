import React from 'react';
import { StatusItemProps } from '../types';

const StatusItem: React.FC<StatusItemProps> = ({ value, label }) => {
  return (
    <div className="status-item">
      <div className="status-value">
        {typeof value === 'string' || typeof value === 'number' ? value : value}
      </div>
      <div className="status-label">{label}</div>
    </div>
  );
};

export default StatusItem;
