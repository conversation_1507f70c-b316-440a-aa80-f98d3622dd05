import { useState, useEffect } from 'react'
import OffersList from '../components/OffersList'
import ControlPanel from '../components/ControlPanel'
import StatsPanel from '../components/StatsPanel'
import type { Offer, ExtensionSettings, OfferStats } from '../types'

// Modern Icons as React Components
const RefreshIcon = () => (
  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
  </svg>
)

const SettingsIcon = () => (
  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
  </svg>
)

const CloseIcon = () => (
  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
  </svg>
)

const AlertIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
  </svg>
)

export function Popup() {
  const [offers, setOffers] = useState<Offer[]>([])
  const [settings, setSettings] = useState<ExtensionSettings | null>(null)
  const [stats, setStats] = useState<OfferStats | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedOffer, setSelectedOffer] = useState<Offer | null>(null)

  useEffect(() => {
    loadInitialData()
    setupMessageListener()
  }, [])

  const loadInitialData = async () => {
    setIsLoading(true)
    try {
      console.log('Popup: Loading initial data...')
      const response = await chrome.runtime.sendMessage({ type: 'GET_POPUP_DATA' })
      console.log('Popup: Received response:', response)
      
      if (response && response.success && response.data) {
        console.log('Popup: Setting offers:', response.data.offers?.length || 0)
        console.log('Popup: Setting settings:', response.data.settings)
        console.log('Popup: Setting stats:', response.data.stats)
        
        setOffers(response.data.offers || [])
        setSettings(response.data.settings || null)
        setStats(response.data.stats || { totalOffers: 0, newOffers: 0, averageRate: 0 })
      } else {
        console.error('Popup: Invalid response from background:', response)
        setError(response?.error || 'Failed to load data from background script')
      }
    } catch (err) {
      console.error('Popup: Error loading initial data:', err)
      setError('Failed to communicate with extension')
    } finally {
      setIsLoading(false)
    }
  }

  const setupMessageListener = () => {
    const handleMessage = (message: any) => {
      if (message.type === 'OFFERS_UPDATED') {
        setOffers(message.offers)
        setStats(message.stats)
      }
    }

    chrome.runtime.onMessage.addListener(handleMessage)
    return () => chrome.runtime.onMessage.removeListener(handleMessage)
  }

  const handleAcceptOffer = async (offer: Offer) => {
    try {
      const response = await chrome.runtime.sendMessage({
        type: 'ACCEPT_OFFER',
        offerId: offer.id
      })
      
      if (!response.success) {
        setError(response.error || 'Failed to accept offer')
      }
    } catch (err) {
      setError('Failed to accept offer')
    }
  }

  const handleRejectOffer = async (offer: Offer) => {
    try {
      const response = await chrome.runtime.sendMessage({
        type: 'REJECT_OFFER',
        offerId: offer.id
      })
      
      if (!response.success) {
        setError(response.error || 'Failed to reject offer')
      }
    } catch (err) {
      setError('Failed to reject offer')
    }
  }

  const handleSettingsUpdate = async (newSettings: ExtensionSettings) => {
    try {
      const response = await chrome.runtime.sendMessage({
        type: 'UPDATE_SETTINGS',
        settings: newSettings
      })
      
      if (response.success) {
        setSettings(newSettings)
      } else {
        setError(response.error || 'Failed to update settings')
      }
    } catch (err) {
      setError('Failed to update settings')
    }
  }

  const handleOpenOptions = () => {
    chrome.runtime.openOptionsPage()
  }

  const handleRefresh = () => {
    loadInitialData()
  }

  if (isLoading) {
    return (
      <div className="popup-container">
        <div className="popup-background">
          <div className="popup-pattern" />
        </div>

        <div className="popup-content">
          <div className="loading-section">
            {/* Modern Logo */}
            <div className="logo-container">
              <div className="logo-icon">
                <span className="logo-text">A</span>
              </div>
              <div className="logo-pulse" />
            </div>

            {/* Loading Spinner */}
            <div className="spinner-container">
              <div className="spinner-ring spinner-ring-1" />
              <div className="spinner-ring spinner-ring-2" />
              <div className="spinner-ring spinner-ring-3" />
            </div>

            {/* Loading Text */}
            <div className="loading-text">
              <h2 className="app-title">Airtm Monitor Pro</h2>
              <p className="loading-message">Initializing dashboard...</p>
              <div className="loading-dots">
                <div className="dot dot-1" />
                <div className="dot dot-2" />
                <div className="dot dot-3" />
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="popup-container">
      <div className="popup-background">
        <div className="popup-pattern" />
      </div>

      <div className="popup-content">
        {/* Modern Header */}
        <header className="popup-header">
          <div className="header-content">
            {/* Logo and Title */}
            <div className="header-brand">
              <div className="brand-logo">
                <div className="logo-icon">
                  <span className="logo-text">A</span>
                </div>
                <div className={`status-indicator ${settings?.monitoring ? 'status-active' : 'status-inactive'}`} />
              </div>
              <div className="brand-info">
                <h1 className="app-title">Airtm Monitor Pro</h1>
                <p className="app-subtitle">Real-time Trading Dashboard</p>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="header-actions">
              <button
                onClick={handleRefresh}
                className="action-btn action-btn-secondary"
                title="Refresh Data"
              >
                <RefreshIcon />
                <span>Refresh</span>
              </button>

              <button
                onClick={handleOpenOptions}
                className="action-btn action-btn-primary"
                title="Open Settings"
              >
                <SettingsIcon />
                <span>Settings</span>
              </button>
            </div>
          </div>
        </header>

        {/* Error Alert */}
        {error && (
          <div className="error-container">
            <div className="error-alert">
              <div className="error-content">
                <div className="error-icon">
                  <AlertIcon />
                </div>
                <div className="error-text">
                  <h4 className="error-title">Error</h4>
                  <p className="error-message">{error}</p>
                </div>
              </div>
              <button
                onClick={() => setError(null)}
                className="error-close"
                title="Dismiss Error"
              >
                <CloseIcon />
              </button>
            </div>
          </div>
        )}

        {/* Stats Panel */}
        <section className="stats-section">
          <div className="stats-container">
            <StatsPanel stats={stats ? {
              total: stats.totalOffers,
              new: stats.newOffers,
              accepted: stats.acceptedOffers,
              rejected: stats.rejectedOffers
            } : {
              total: 0,
              new: 0,
              accepted: 0,
              rejected: 0
            }} />
          </div>
        </section>

        {/* Main Content Area */}
        <main className="main-content">
          <div className="content-container">
            {/* Offers List Header */}
            <div className="offers-header">
              <div className="offers-title">
                <div className="title-icon">
                  <div className="icon-shape" />
                </div>
                <h3 className="section-title">Available Offers</h3>
              </div>
              <div className="offers-meta">
                <div className="live-indicator">
                  <div className="pulse-dot" />
                  <span className="live-text">Live Updates</span>
                </div>
                <div className="offers-count">
                  {offers.length} offers
                </div>
              </div>
            </div>

            {/* Offers List Content */}
            <div className="offers-content">
              <div className="offers-scroll">
                <OffersList
                  offers={offers}
                  selectedOffer={selectedOffer}
                  onOfferSelect={setSelectedOffer}
                  settings={settings || {
                    monitoring: false,
                    telegramBotToken: '',
                    telegramChatId: '',
                    webhookUrl: '',
                    soundEnabled: false,
                    notificationsEnabled: false,
                    autoAccept: false,
                    minAmount: 0,
                    maxAmount: 999999,
                    preferredCurrencies: [],
                    keywords: [],
                    blacklistKeywords: [],
                    paymentMethods: [],
                    countries: [],
                    hotkeys: {
                      accept_offer: { key: 'Enter', description: 'Accept selected offer' },
                      reject_offer: { key: 'Delete', description: 'Reject selected offer' },
                      open_offer_details: { key: 'Ctrl+Shift+D', description: 'Open offer details' },
                      cycle_offers: { key: 'Ctrl+Shift+C', description: 'Cycle offers' }
                    },
                    fuzzyMatching: {
                      enabled: false,
                      threshold: 0.8,
                      enableAliases: true,
                      customAliases: {}
                    }
                  }}
                />
              </div>
            </div>
          </div>
        </main>

        {/* Control Panel */}
        <footer className="control-section">
          <div className="control-container">
            <ControlPanel
              settings={settings || {
                monitoring: false,
                telegramBotToken: '',
                telegramChatId: '',
                webhookUrl: '',
                soundEnabled: false,
                notificationsEnabled: false,
                autoAccept: false,
                minAmount: 0,
                maxAmount: 999999,
                preferredCurrencies: [],
                keywords: [],
                blacklistKeywords: [],
                paymentMethods: [],
                countries: [],
                hotkeys: {
                  accept_offer: { key: 'Enter', description: 'Accept selected offer' },
                  reject_offer: { key: 'Delete', description: 'Reject selected offer' },
                  open_offer_details: { key: 'Ctrl+Shift+D', description: 'Open offer details' },
                  cycle_offers: { key: 'Ctrl+Shift+C', description: 'Cycle offers' }
                },
                fuzzyMatching: {
                  enabled: false,
                  threshold: 0.8,
                  enableAliases: true,
                  customAliases: {}
                }
              }}
              onSettingsUpdate={handleSettingsUpdate}
              onAcceptOffer={(offerId) => {
                const offer = offers.find(o => o.id === offerId)
                if (offer) handleAcceptOffer(offer)
              }}
              onRejectOffer={(offerId) => {
                const offer = offers.find(o => o.id === offerId)
                if (offer) handleRejectOffer(offer)
              }}
              selectedOffer={selectedOffer}
            />
          </div>
        </footer>
      </div>
    </div>
  )
}