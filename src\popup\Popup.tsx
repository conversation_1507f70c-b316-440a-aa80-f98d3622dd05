import { useState, useEffect, useCallback, useMemo } from 'react'
import type { Offer, ExtensionSettings, OfferStats } from '../types'
import { isExtensionContextValid, safeRuntimeSendMessage } from '../utils/extension-context'

// ===== MODERN POPUP ARCHITECTURE =====
// Clean, semantic, performance-optimized popup interface

interface PopupState {
  offers: Offer[]
  settings: ExtensionSettings | null
  stats: OfferStats | null
  selectedOffer: Offer | null
  isLoading: boolean
  error: string | null
  isConnected: boolean
}

// Enhanced offer display utilities
interface OfferDisplayInfo {
  amount: string
  currency: string
  originalAmount?: string
  originalCurrency?: string
  isUSDC: boolean
  isWithdrawal: boolean
  operationType?: string
  conversionNote?: string
}

interface PopupActions {
  refresh: () => void
  openSettings: () => void
  selectOffer: (offer: Offer | null) => void
  updateSettings: (settings: ExtensionSettings) => void
  acceptOffer: (offer: Offer) => void
  rejectOffer: (offer: Offer) => void
  dismissError: () => void
}

// Modern Icon Components (Optimized)
const Icon = ({ d, className = "w-4 h-4" }: { d: string; className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={d} />
  </svg>
)

const Icons = {
  refresh: "M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15",
  settings: "M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z M15 12a3 3 0 11-6 0 3 3 0 016 0z",
  close: "M6 18L18 6M6 6l12 12",
  alert: "M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z",
  play: "M14.828 14.828a4 4 0 01-5.656 0M9 10h1.586a1 1 0 01.707.293l2.414 2.414a1 1 0 00.707.293H15",
  pause: "M10 9v6m4-6v6",
  check: "M5 13l4 4L19 7",
  search: "M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z",
  withdrawal: "M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z",
  crypto: "M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"
}

// USDC Detection and Display Utilities
function detectUSDCOperation(offer: Offer): OfferDisplayInfo {
  const walletCurrency = offer.walletCurrency
  const localCurrency = offer.currency
  const paymentMethod = offer.makerPaymentMethod || (offer as any).takerPaymentMethod

  // Check for USDC indicators
  const hasUSDCPaymentMethod = paymentMethod?.categoryId?.toLowerCase().includes('usdc') ||
                              paymentMethod?.version?.category?.translationTag?.toLowerCase().includes('usdc')

  // Check precision (USDC = 6 decimals, fiat = 2 decimals)
  const walletPrecision = (offer.metadata as any)?.walletCurrencyPrecision || walletCurrency?.precision
  const localPrecision = (offer.metadata as any)?.localCurrencyPrecision || localCurrency?.precision

  const hasUSDCPrecision = walletPrecision === 6 || localPrecision === 6
  const isInternalOperation = (offer.metadata as any)?.isForThirdPartyPaymentMethod === false

  // Determine if this is USDC and operation type
  const isUSDC = hasUSDCPaymentMethod || hasUSDCPrecision || isInternalOperation
  let isWithdrawal = false
  let operationType = ''
  let displayCurrency = localCurrency?.symbol || '$'
  let displayAmount = offer.grossAmount || '0'

  if (isUSDC) {
    if (offer.operationType === 'SELL' && walletPrecision === 6) {
      // SELL with USDC wallet = withdrawal
      isWithdrawal = true
      operationType = 'withdrawal'
      displayCurrency = 'USDC'
      displayAmount = offer.grossAmount || '0'
    } else if (offer.operationType === 'BUY' && localPrecision === 6) {
      // BUY with USDC local = deposit
      operationType = 'deposit'
      displayCurrency = 'USDC'
    } else {
      operationType = 'exchange'
      if (walletPrecision === 6) {
        displayCurrency = 'USDC'
      }
    }
  }

  return {
    amount: displayAmount,
    currency: displayCurrency,
    isUSDC,
    isWithdrawal,
    operationType
  }
}

export function Popup() {
  // Centralized state management
  const [state, setState] = useState<PopupState>({
    offers: [],
    settings: null,
    stats: null,
    selectedOffer: null,
    isLoading: true,
    error: null,
    isConnected: false
  })

  // Modern data loading with proper error handling
  const loadData = useCallback(async () => {
    setState(prev => ({ ...prev, isLoading: true, error: null }))

    try {
      // Check if extension context is available
      if (!isExtensionContextValid()) {
        throw new Error('Extension context not available')
      }

      const response = await safeRuntimeSendMessage({ type: 'GET_POPUP_DATA' })

      if (response?.success && response.data) {
        setState(prev => ({
          ...prev,
          offers: response.data.offers || [],
          settings: response.data.settings || null,
          stats: response.data.stats || { totalOffers: 0, newOffers: 0, averageRate: 0 },
          isConnected: true,
          isLoading: false,
          error: null
        }))
      } else {
        throw new Error(response?.error || 'Invalid response from background script')
      }
    } catch (err) {
      console.error('Popup data loading error:', err)
      setState(prev => ({
        ...prev,
        isLoading: false,
        isConnected: false,
        error: err instanceof Error ? err.message : 'Failed to load extension data'
      }))
    }
  }, [])

  // Message listener for real-time updates
  const setupMessageListener = useCallback(() => {
    const handleMessage = (message: any) => {
      if (message.type === 'OFFERS_UPDATED') {
        setState(prev => ({ ...prev, offers: message.offers || [] }))
      } else if (message.type === 'SETTINGS_UPDATED') {
        setState(prev => ({ ...prev, settings: message.settings }))
      } else if (message.type === 'STATS_UPDATED') {
        setState(prev => ({ ...prev, stats: message.stats }))
      }
    }

    if (isExtensionContextValid()) {
      chrome.runtime?.onMessage?.addListener(handleMessage)
      return () => chrome.runtime?.onMessage?.removeListener(handleMessage)
    }
    return () => {}
  }, [])

  useEffect(() => {
    loadData()
    const cleanup = setupMessageListener()
    return cleanup
  }, [])

  // Modern action handlers with proper error handling
  const actions: PopupActions = useMemo(() => ({
    refresh: () => loadData(),

    openSettings: () => {
      try {
        if (!isExtensionContextValid()) {
          throw new Error('Extension context not available')
        }
        chrome.runtime.openOptionsPage()
      } catch (err) {
        console.error('Failed to open settings:', err)
        setState(prev => ({ ...prev, error: 'Failed to open settings page' }))
      }
    },

    selectOffer: (offer: Offer | null) => {
      setState(prev => ({ ...prev, selectedOffer: offer }))
    },

    updateSettings: async (newSettings: ExtensionSettings) => {
      try {
        const response = await safeRuntimeSendMessage({
          type: 'SETTINGS_UPDATE',  // Fixed: Use correct message type
          data: newSettings  // Fixed: Use 'data' field instead of 'settings'
        })

        if (response?.success) {
          setState(prev => ({ ...prev, settings: newSettings }))
        } else {
          throw new Error(response?.error || 'Failed to update settings')
        }
      } catch (err) {
        console.error('Settings update error:', err)
        setState(prev => ({
          ...prev,
          error: err instanceof Error ? err.message : 'Failed to update settings'
        }))
      }
    },

    acceptOffer: async (offer: Offer) => {
      try {
        const response = await safeRuntimeSendMessage({
          type: 'ACCEPT_OFFER',
          offerId: offer.id || offer.hash  // Use id first, fallback to hash
        })

        if (!response?.success) {
          throw new Error(response?.error || 'Failed to accept offer')
        }
      } catch (err) {
        console.error('Accept offer error:', err)
        setState(prev => ({
          ...prev,
          error: err instanceof Error ? err.message : 'Failed to accept offer'
        }))
      }
    },

    rejectOffer: async (offer: Offer) => {
      try {
        const response = await safeRuntimeSendMessage({
          type: 'REJECT_OFFER',
          offerId: offer.id || offer.hash  // Use id first, fallback to hash
        })

        if (!response?.success) {
          throw new Error(response?.error || 'Failed to reject offer')
        }
      } catch (err) {
        console.error('Reject offer error:', err)
        setState(prev => ({
          ...prev,
          error: err instanceof Error ? err.message : 'Failed to reject offer'
        }))
      }
    },

    dismissError: () => {
      setState(prev => ({ ...prev, error: null }))
    }
  }), [loadData])



  // Loading state with modern design
  if (state.isLoading) {
    return (
      <div className="modern-popup">
        <div className="loading-state">
          <div className="loading-logo">
            <div className="logo-circle">A</div>
            <div className="loading-pulse" />
          </div>
          <div className="loading-content">
            <h2 className="loading-title">Airtm Monitor Pro</h2>
            <p className="loading-text">Connecting to extension...</p>
            <div className="loading-progress">
              <div className="progress-bar" />
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Main popup interface with modern architecture
  return (
    <div className="modern-popup">
      {/* Header Section */}
      <header className="popup-header">
        <div className="header-brand">
          <div className="brand-logo">
            <div className="logo-circle">A</div>
            <div className={`status-dot ${state.settings?.monitoring ? 'active' : 'inactive'}`} />
          </div>
          <div className="brand-text">
            <h1 className="brand-title">Airtm Monitor Pro</h1>
            <p className="brand-subtitle">
              {state.isConnected ? 'Connected' : 'Disconnected'} •
              {state.offers.length} offers
            </p>
          </div>
        </div>

        <div className="header-actions">
          <button
            onClick={actions.refresh}
            className="action-button secondary"
            title="Refresh data"
          >
            <Icon d={Icons.refresh} />
          </button>

          <button
            onClick={actions.openSettings}
            className="action-button primary"
            title="Open settings"
          >
            <Icon d={Icons.settings} />
          </button>
        </div>
      </header>

      {/* Error Alert */}
      {state.error && (
        <div className="error-banner">
          <div className="error-content">
            <Icon d={Icons.alert} className="error-icon" />
            <span className="error-message">{state.error}</span>
          </div>
          <button
            onClick={actions.dismissError}
            className="error-dismiss"
            title="Dismiss error"
          >
            <Icon d={Icons.close} />
          </button>
        </div>
      )}

      {/* Stats Section */}
      <section className="stats-section">
        <div className="stats-grid">
          <div className="stat-card">
            <div className="stat-value">{state.stats?.totalOffers || 0}</div>
            <div className="stat-label">Total Offers</div>
          </div>
          <div className="stat-card">
            <div className="stat-value">{state.stats?.newOffers || 0}</div>
            <div className="stat-label">New Today</div>
          </div>
          <div className="stat-card">
            <div className="stat-value">{state.stats?.acceptedOffers || 0}</div>
            <div className="stat-label">Accepted</div>
          </div>
          <div className="stat-card">
            <div className="stat-value">{state.stats?.rejectedOffers || 0}</div>
            <div className="stat-label">Rejected</div>
          </div>
        </div>
      </section>

      {/* Main Content - Offers List */}
      <main className="main-content">
        <div className="content-header">
          <h3 className="content-title">Available Offers</h3>
          <div className="content-meta">
            <div className="live-status">
              <div className="pulse-indicator" />
              <span>Live</span>
            </div>
          </div>
        </div>

        <div className="offers-container">
          {state.offers.length === 0 ? (
            <div className="empty-state">
              <Icon d={Icons.search} className="empty-icon" />
              <h4 className="empty-title">No offers available</h4>
              <p className="empty-text">
                {state.settings?.monitoring
                  ? 'Monitoring is active. New offers will appear here.'
                  : 'Start monitoring to see offers.'
                }
              </p>
            </div>
          ) : (
            <div className="offers-list">
              {state.offers.map((offer) => {
                const displayInfo = detectUSDCOperation(offer)
                return (
                  <div
                    key={offer.id || offer.hash}
                    className={`offer-item ${(state.selectedOffer?.id || state.selectedOffer?.hash) === (offer.id || offer.hash) ? 'selected' : ''} ${displayInfo.isWithdrawal ? 'withdrawal' : ''} ${displayInfo.isUSDC ? 'usdc-operation' : ''}`}
                    onClick={() => actions.selectOffer(offer)}
                  >
                    <div className="offer-header">
                      <div className="offer-currency">
                        {displayInfo.currency}
                        {displayInfo.isUSDC && (
                          <span className="crypto-badge">
                            <Icon d={Icons.crypto} className="w-3 h-3" />
                          </span>
                        )}
                      </div>
                      <div className="offer-type">
                        {offer.operationType}
                        {displayInfo.isWithdrawal && (
                          <span className="withdrawal-badge">
                            <Icon d={Icons.withdrawal} className="w-3 h-3" />
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="offer-details">
                      <div className="offer-amount">
                        {displayInfo.isWithdrawal ? '-' : ''}
                        {displayInfo.currency === 'USDC' ? '' : '$'}
                        {parseFloat(displayInfo.amount).toFixed(displayInfo.isUSDC ? 6 : 2)}
                        {displayInfo.currency === 'USDC' ? ' USDC' : ''}
                      </div>
                      <div className="offer-rate">
                        Rate: {offer.rate || offer.displayRate?.rate || 'N/A'}
                      </div>
                      {displayInfo.isWithdrawal && (
                        <div className="withdrawal-note">
                          <span className="text-orange-600 text-xs">
                            Wallet deduction
                          </span>
                        </div>
                      )}
                    </div>
                    <div className="offer-peer">
                      {offer.peer ? `${offer.peer.firstName} ${offer.peer.lastName}` : 'Unknown'}
                      {displayInfo.operationType && (
                        <span className="operation-type-badge">
                          {displayInfo.operationType}
                        </span>
                      )}
                    </div>
                  </div>
                )
              })}
            </div>
          )}
        </div>
      </main>

      {/* Control Panel */}
      <footer className="control-panel">
        {/* Selected Offer Actions */}
        {state.selectedOffer && (
          <div className="selected-offer">
            <div className="selected-info">
              <span className="selected-label">Selected:</span>
              <span className="selected-details">
                {(() => {
                  const displayInfo = detectUSDCOperation(state.selectedOffer)
                  return (
                    <>
                      {displayInfo.currency} •
                      {displayInfo.isWithdrawal ? '-' : ''}
                      {displayInfo.currency === 'USDC' ? '' : '$'}
                      {parseFloat(displayInfo.amount).toFixed(displayInfo.isUSDC ? 6 : 2)}
                      {displayInfo.currency === 'USDC' ? ' USDC' : ''}
                      {displayInfo.isWithdrawal && (
                        <span className="text-orange-600 ml-1">(withdrawal)</span>
                      )}
                      {displayInfo.isUSDC && !displayInfo.isWithdrawal && (
                        <span className="text-blue-600 ml-1">(USDC)</span>
                      )}
                    </>
                  )
                })()}
              </span>
            </div>
            <div className="selected-actions">
              <button
                onClick={() => actions.acceptOffer(state.selectedOffer!)}
                className="action-button success"
                title="Accept offer"
              >
                <Icon d={Icons.check} />
                Accept
              </button>
              <button
                onClick={() => actions.rejectOffer(state.selectedOffer!)}
                className="action-button danger"
                title="Reject offer"
              >
                <Icon d={Icons.close} />
                Reject
              </button>
            </div>
          </div>
        )}

        {/* Main Controls */}
        <div className="main-controls">
          <button
            onClick={() => actions.updateSettings({
              ...state.settings!,
              monitoring: !state.settings?.monitoring
            })}
            className={`control-button ${state.settings?.monitoring ? 'active' : 'inactive'}`}
            title={state.settings?.monitoring ? 'Stop monitoring' : 'Start monitoring'}
          >
            <Icon d={state.settings?.monitoring ? Icons.pause : Icons.play} />
            <span>{state.settings?.monitoring ? 'Stop' : 'Start'}</span>
          </button>

          <button
            onClick={() => actions.updateSettings({
              ...state.settings!,
              autoAccept: !state.settings?.autoAccept
            })}
            className={`control-button ${state.settings?.autoAccept ? 'active' : 'inactive'}`}
            title="Toggle auto-accept"
          >
            <Icon d={Icons.check} />
            <span>Auto Accept</span>
          </button>
        </div>
      </footer>
    </div>
  )
}