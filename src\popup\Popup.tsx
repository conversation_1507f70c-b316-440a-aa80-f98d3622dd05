import React, { useState, useEffect, useCallback } from 'react';
import { Settings, Stats, DEFAULT_SETTINGS, DEFAULT_STATS, ChromeAPI, ChromeMessage } from './types';
import PopupHeader from './components/PopupHeader';
import StatusCards from './components/StatusCards';
import QuickActions from './components/QuickActions';
import PopupFooter from './components/PopupFooter';

// Use chrome from global scope
const chromeAPI = (window as any).chrome as ChromeAPI | undefined;

const Popup: React.FC = () => {
  const [settings, setSettings] = useState<Settings>(DEFAULT_SETTINGS);
  const [stats, setStats] = useState<Stats>(DEFAULT_STATS);
  const [isLoading, setIsLoading] = useState(true);

  // Load settings from Chrome storage
  const loadSettings = useCallback(async () => {
    try {
      if (chromeAPI?.storage?.sync) {
        const result = await chromeAPI.storage.sync.get('settings');
        if (result.settings) {
          setSettings(prev => ({ ...prev, ...result.settings }));
        }
      }
    } catch (error) {
      console.warn('Could not load settings:', error);
    }
  }, []);

  // Load stats from Chrome storage
  const loadStats = useCallback(async () => {
    try {
      if (chromeAPI?.storage?.local) {
        const result = await chromeAPI.storage.local.get(['stats', 'monitoringStartTime']);
        if (result.stats) {
          setStats(prev => ({ ...prev, ...result.stats }));
        }
        if (result.monitoringStartTime) {
          setStats(prev => ({ ...prev, monitoringStartTime: result.monitoringStartTime }));
        }
      }
    } catch (error) {
      console.warn('Could not load stats:', error);
    }
  }, []);

  // Save settings to Chrome storage
  const saveSettings = useCallback(async (newSettings: Settings) => {
    try {
      if (chromeAPI?.storage?.sync) {
        await chromeAPI.storage.sync.set({ settings: newSettings });
      }
    } catch (error) {
      console.error('Failed to save settings:', error);
    }
  }, []);

  // Save stats to Chrome storage
  const saveStats = useCallback(async (newStats: Stats) => {
    try {
      if (chromeAPI?.storage?.local) {
        await chromeAPI.storage.local.set({
          stats: newStats,
          monitoringStartTime: newStats.monitoringStartTime
        });
      }
    } catch (error) {
      console.error('Failed to save stats:', error);
    }
  }, []);

  // Send message to background script
  const sendMessageToBackground = useCallback((action: string, data: any = {}) => {
    try {
      if (chromeAPI?.runtime?.sendMessage) {
        const message: ChromeMessage = { action, ...data };
        chromeAPI.runtime.sendMessage(message);
      }
    } catch (error) {
      console.warn('Could not send message to background:', error);
    }
  }, []);

  // Handle monitoring toggle
  const handleMonitoringToggle = useCallback((enabled: boolean) => {
    const newSettings = { ...settings, monitoring: enabled };
    const newStats = { ...stats };

    if (enabled) {
      // Start monitoring
      newStats.monitoringStartTime = Date.now();
      sendMessageToBackground('startMonitoring');
    } else {
      // Stop monitoring
      newStats.monitoringStartTime = null;
      sendMessageToBackground('stopMonitoring');
    }

    setSettings(newSettings);
    setStats(newStats);
    saveSettings(newSettings);
    saveStats(newStats);
  }, [settings, stats, saveSettings, saveStats, sendMessageToBackground]);

  // Open tab helper
  const openTab = useCallback((url: string) => {
    try {
      if (chromeAPI?.tabs?.create) {
        chromeAPI.tabs.create({ url });
        window.close(); // Close popup after opening tab
      }
    } catch (error) {
      console.error('Could not open tab:', error);
    }
  }, []);

  // Quick action handlers
  const handleOpenAirtm = useCallback(() => {
    openTab('https://app.airtm.com/peer-transfers/available');
  }, [openTab]);

  const handleViewOffers = useCallback(() => {
    openTab('https://app.airtm.com/peer-transfers/available');
  }, [openTab]);

  const handleOpenSettings = useCallback(() => {
    try {
      if (chromeAPI?.runtime?.openOptionsPage) {
        chromeAPI.runtime.openOptionsPage();
      } else {
        // Fallback
        const optionsUrl = chromeAPI?.runtime?.getURL('src/options/index.html');
        if (optionsUrl) {
          openTab(optionsUrl);
        }
      }
    } catch (error) {
      console.error('Could not open settings:', error);
    }
  }, [openTab]);

  const handleHelpClick = useCallback(() => {
    handleOpenSettings(); // Open settings and navigate to help section
  }, [handleOpenSettings]);

  const handleAboutClick = useCallback(() => {
    handleOpenSettings(); // Open settings and navigate to about section
  }, [handleOpenSettings]);

  // Initialize popup
  useEffect(() => {
    const initializePopup = async () => {
      try {
        await Promise.all([loadSettings(), loadStats()]);
        setIsLoading(false);
        console.log('✅ Popup initialized successfully');
      } catch (error) {
        console.error('❌ Failed to initialize popup:', error);
        setIsLoading(false);
      }
    };

    initializePopup();
  }, [loadSettings, loadStats]);

  // Show loading state
  if (isLoading) {
    return (
      <div className="popup-container">
        <div className="loading">
          <div className="spinner"></div>
          <span>Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="popup-container">
      <PopupHeader isConnected={stats.isConnected} />
      
      <div className="popup-content">
        <StatusCards
          stats={stats}
          settings={settings}
          onMonitoringToggle={handleMonitoringToggle}
        />
        
        <QuickActions
          onOpenAirtm={handleOpenAirtm}
          onViewOffers={handleViewOffers}
          onOpenSettings={handleOpenSettings}
        />
      </div>

      <PopupFooter
        onHelpClick={handleHelpClick}
        onAboutClick={handleAboutClick}
      />
    </div>
  );
};

export default Popup;
