import { useState, useEffect } from 'react'
import OffersList from '../components/OffersList'
import ControlPanel from '../components/ControlPanel'
import StatsPanel from '../components/StatsPanel'
import type { Offer, ExtensionSettings, OfferStats } from '../types'

export function Popup() {
  const [offers, setOffers] = useState<Offer[]>([])
  const [settings, setSettings] = useState<ExtensionSettings | null>(null)
  const [stats, setStats] = useState<OfferStats | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedOffer, setSelectedOffer] = useState<Offer | null>(null)

  useEffect(() => {
    loadInitialData()
    setupMessageListener()
  }, [])

  const loadInitialData = async () => {
    setIsLoading(true)
    try {
      console.log('Popup: Loading initial data...')
      const response = await chrome.runtime.sendMessage({ type: 'GET_POPUP_DATA' })
      console.log('Popup: Received response:', response)
      
      if (response && response.success && response.data) {
        console.log('Popup: Setting offers:', response.data.offers?.length || 0)
        console.log('Popup: Setting settings:', response.data.settings)
        console.log('Popup: Setting stats:', response.data.stats)
        
        setOffers(response.data.offers || [])
        setSettings(response.data.settings || null)
        setStats(response.data.stats || { totalOffers: 0, newOffers: 0, averageRate: 0 })
      } else {
        console.error('Popup: Invalid response from background:', response)
        setError(response?.error || 'Failed to load data from background script')
      }
    } catch (err) {
      console.error('Popup: Error loading initial data:', err)
      setError('Failed to communicate with extension')
    } finally {
      setIsLoading(false)
    }
  }

  const setupMessageListener = () => {
    const handleMessage = (message: any) => {
      if (message.type === 'OFFERS_UPDATED') {
        setOffers(message.offers)
        setStats(message.stats)
      }
    }

    chrome.runtime.onMessage.addListener(handleMessage)
    return () => chrome.runtime.onMessage.removeListener(handleMessage)
  }

  const handleAcceptOffer = async (offer: Offer) => {
    try {
      const response = await chrome.runtime.sendMessage({
        type: 'ACCEPT_OFFER',
        offerId: offer.id
      })
      
      if (!response.success) {
        setError(response.error || 'Failed to accept offer')
      }
    } catch (err) {
      setError('Failed to accept offer')
    }
  }

  const handleRejectOffer = async (offer: Offer) => {
    try {
      const response = await chrome.runtime.sendMessage({
        type: 'REJECT_OFFER',
        offerId: offer.id
      })
      
      if (!response.success) {
        setError(response.error || 'Failed to reject offer')
      }
    } catch (err) {
      setError('Failed to reject offer')
    }
  }

  const handleSettingsUpdate = async (newSettings: ExtensionSettings) => {
    try {
      const response = await chrome.runtime.sendMessage({
        type: 'UPDATE_SETTINGS',
        settings: newSettings
      })
      
      if (response.success) {
        setSettings(newSettings)
      } else {
        setError(response.error || 'Failed to update settings')
      }
    } catch (err) {
      setError('Failed to update settings')
    }
  }

  const handleOpenOptions = () => {
    chrome.runtime.openOptionsPage()
  }

  const handleRefresh = () => {
    loadInitialData()
  }

  if (isLoading) {
    return (
      <div className="w-[520px] h-[680px] bg-gradient-to-br from-white via-gray-50 to-blue-50 flex items-center justify-center relative overflow-hidden">
        {/* Subtle background pattern */}
        <div className="absolute inset-0 opacity-30">
          <div className="absolute inset-0" style={{
            backgroundImage: `
              radial-gradient(circle at 25% 25%, rgba(99, 102, 241, 0.08) 0%, transparent 50%),
              radial-gradient(circle at 75% 75%, rgba(168, 85, 247, 0.08) 0%, transparent 50%)
            `
          }} />
        </div>
        
        {/* Glass overlay */}
        <div className="absolute inset-0 bg-white/50 backdrop-blur-sm" />
        
        <div className="relative z-10 text-center">
          {/* Logo */}
          <div className="w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-3xl flex items-center justify-center shadow-xl mb-6 mx-auto border border-white/30">
            <span className="text-white font-bold text-2xl">A</span>
          </div>
          
          {/* Loading animation */}
          <div className="relative mb-6">
            <div className="w-20 h-20 mx-auto">
              <div className="absolute inset-0 rounded-full border-4 border-gray-200/60"></div>
              <div className="absolute inset-0 rounded-full border-4 border-transparent border-t-indigo-500 animate-spin"></div>
              <div className="absolute inset-2 rounded-full border-3 border-transparent border-t-purple-400 animate-spin" style={{animationDirection: 'reverse', animationDuration: '2s'}}></div>
            </div>
          </div>
          
          {/* Loading text */}
          <div className="space-y-3">
            <h2 className="text-xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
              Airtm Monitor Pro
            </h2>
            <p className="text-gray-600 font-medium animate-pulse">Loading offers...</p>
            <div className="flex items-center justify-center space-x-2 mt-4">
              <div className="w-2 h-2 bg-indigo-500 rounded-full animate-bounce"></div>
              <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="w-[600px] h-[850px] bg-gradient-to-br from-slate-50 via-blue-50 to-emerald-50 relative overflow-hidden">
      {/* Modern background pattern */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.08),transparent_50%),radial-gradient(circle_at_80%_80%,rgba(16,185,129,0.08),transparent_50%),radial-gradient(circle_at_40%_40%,rgba(99,102,241,0.05),transparent_50%)]" />
        <div className="absolute inset-0 bg-gradient-to-br from-white/90 via-white/60 to-white/80" />
      </div>

      <div className="relative z-10 h-full flex flex-col">
        {/* Modern Header */}
        <div className="bg-white/90 backdrop-blur-xl border-b border-slate-200/50 shadow-sm">
          <div className="px-6 py-4">
            <div className="flex items-center justify-between">
              {/* Logo and title */}
              <div className="flex items-center space-x-4">
                <div className="relative">
                  <div className="w-12 h-12 bg-gradient-to-br from-indigo-600 via-purple-600 to-pink-600 rounded-2xl flex items-center justify-center shadow-lg border border-white/20">
                    <span className="text-white font-bold text-xl">A</span>
                  </div>
                  <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white shadow-sm animate-pulse" />
                </div>
                <div>
                  <h1 className="text-xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">
                    Airtm Monitor Pro
                  </h1>
                  <p className="text-sm text-slate-500 font-medium">Real-time Trading Dashboard</p>
                </div>
              </div>
              
              {/* Action buttons */}
              <div className="flex items-center space-x-3">
                <button 
                  onClick={handleRefresh}
                  className="group relative px-4 py-2.5 bg-white/80 border border-slate-200/80 rounded-xl hover:bg-white hover:border-indigo-300/60 transition-all duration-300 shadow-sm hover:shadow-md"
                >
                  <div className="flex items-center space-x-2">
                    <svg className="w-4 h-4 text-indigo-600 group-hover:rotate-180 transition-transform duration-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    <span className="text-sm font-medium text-slate-700">Refresh</span>
                  </div>
                </button>
                
                <button 
                  onClick={handleOpenOptions}
                  className="group relative px-4 py-2.5 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-xl hover:from-indigo-700 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                >
                  <div className="flex items-center space-x-2">
                    <svg className="w-4 h-4 group-hover:rotate-90 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    <span className="text-sm font-medium">Settings</span>
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Error alert */}
        {error && (
          <div className="mx-6 mt-4 animate-in slide-in-from-top duration-300">
            <div className="bg-red-50/90 backdrop-blur-sm border border-red-200/60 rounded-xl p-4 shadow-sm">
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3">
                  <div className="w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center shadow-sm flex-shrink-0 mt-0.5">
                    <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div className="flex-1">
                    <h4 className="text-sm font-semibold text-red-800 mb-1">Error</h4>
                    <p className="text-sm text-red-700">{error}</p>
                  </div>
                </div>
                <button 
                  onClick={() => setError(null)}
                  className="text-red-400 hover:text-red-600 transition-colors duration-200 p-1 rounded-lg hover:bg-red-100/50"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Stats Panel */}
        <div className="px-6 py-4">
          <div className="bg-white/70 backdrop-blur-xl border border-slate-200/50 rounded-2xl shadow-sm">
            <div className="p-4">
              <StatsPanel stats={stats ? {
                total: stats.totalOffers,
                new: stats.newOffers,
                accepted: stats.acceptedOffers,
                rejected: stats.rejectedOffers
              } : {
                total: 0,
                new: 0,
                accepted: 0,
                rejected: 0
              }} />
            </div>
          </div>
        </div>

        {/* Main Content Area - Fixed Height for Better Layout */}
        <div className="px-6 pb-2" style={{ height: '380px' }}>
          <div className="h-full card-glass overflow-hidden animate-fade-in">
            <div className="h-full flex flex-col">
              {/* Offers List Header */}
              <div className="px-6 py-3 border-b border-slate-200/60 gradient-surface flex-shrink-0">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 gradient-secondary rounded-xl flex items-center justify-center shadow-sm">
                      <div className="w-4 h-4 bg-white rounded-sm" />
                    </div>
                    <h3 className="text-lg font-bold text-slate-800">Available Offers</h3>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center space-x-2 px-3 py-1.5 glass-effect rounded-full border border-white/40">
                      <div className="status-online animate-pulse" />
                      <span className="text-sm font-semibold text-slate-700">Live Updates</span>
                    </div>
                    <div className="px-3 py-1.5 badge badge-primary">
                      {offers.length} offers
                    </div>
                  </div>
                </div>
              </div>

              {/* Offers List Content - Scrollable Area */}
              <div className="flex-1 overflow-hidden">
                <div className="h-full overflow-y-auto scrollbar-modern">
                  <div className="p-4">
                    <OffersList
                      offers={offers}
                      selectedOffer={selectedOffer}
                      onOfferSelect={setSelectedOffer}
                      settings={settings || {
                        monitoring: false,
                        telegramBotToken: '',
                        telegramChatId: '',
                        webhookUrl: '',
                        soundEnabled: false,
                        notificationsEnabled: false,
                        autoAccept: false,
                        minAmount: 0,
                        maxAmount: 999999,
                        preferredCurrencies: [],
                        keywords: [],
                        blacklistKeywords: [],
                        paymentMethods: [],
                        countries: [],
                        hotkeys: {
                          accept_offer: { key: 'Enter', description: 'Accept selected offer' },
                          reject_offer: { key: 'Delete', description: 'Reject selected offer' },
                          open_offer_details: { key: 'Ctrl+Shift+D', description: 'Open offer details' },
                          cycle_offers: { key: 'Ctrl+Shift+C', description: 'Cycle offers' }
                        },
                        fuzzyMatching: {
                          enabled: false,
                          threshold: 0.8,
                          enableAliases: true,
                          customAliases: {}
                        }
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Control Panel - Fixed Position at Bottom */}
        <div className="px-6 py-2 flex-shrink-0">
          <ControlPanel
                settings={settings || {
                  monitoring: false,
                  telegramBotToken: '',
                  telegramChatId: '',
                  webhookUrl: '',
                  soundEnabled: false,
                  notificationsEnabled: false,
                  autoAccept: false,
                  minAmount: 0,
                  maxAmount: 999999,
                  preferredCurrencies: [],
                  keywords: [],
                  blacklistKeywords: [],
                  paymentMethods: [],
                  countries: [],
                  hotkeys: {
                    accept_offer: { key: 'Enter', description: 'Accept selected offer' },
                    reject_offer: { key: 'Delete', description: 'Reject selected offer' },
                    open_offer_details: { key: 'Ctrl+Shift+D', description: 'Open offer details' },
                    cycle_offers: { key: 'Ctrl+Shift+C', description: 'Cycle offers' }
                  },
                  fuzzyMatching: {
                    enabled: false,
                    threshold: 0.8,
                    enableAliases: true,
                    customAliases: {}
                  }
                }}
                onSettingsUpdate={handleSettingsUpdate}
                onAcceptOffer={(offerId) => {
                  const offer = offers.find(o => o.id === offerId)
                  if (offer) handleAcceptOffer(offer)
                }}
                onRejectOffer={(offerId) => {
                  const offer = offers.find(o => o.id === offerId)
                  if (offer) handleRejectOffer(offer)
                }}
                selectedOffer={selectedOffer}
              />
        </div>
      </div>
    </div>
  )
}