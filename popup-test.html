<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Popup Test - Airtm Monitor Pro</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: #f8fafc;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        
        .popup-container {
            width: 600px;
            height: 850px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            background: white;
            position: relative;
        }
        
        .popup-frame {
            width: 100%;
            height: 100%;
            border: none;
            display: block;
        }
        
        .instructions {
            text-align: center;
            margin-bottom: 24px;
            color: #64748b;
        }
        
        .title {
            background: linear-gradient(135deg, #3b82f6, #10b981);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .subtitle {
            color: #64748b;
            font-size: 1.1rem;
            margin-bottom: 32px;
        }
        
        .note {
            background: linear-gradient(135deg, #eff6ff, #ecfdf5);
            border: 1px solid #bfdbfe;
            border-radius: 12px;
            padding: 16px;
            margin-top: 24px;
            font-size: 14px;
            color: #1e40af;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .note strong {
            color: #1e3a8a;
        }
        
        .dimensions-info {
            background: #f1f5f9;
            border: 1px solid #cbd5e1;
            border-radius: 8px;
            padding: 12px;
            margin-top: 16px;
            font-family: monospace;
            font-size: 12px;
            color: #475569;
        }
    </style>
</head>
<body>
    <div style="max-width: 800px; padding: 20px;">
        <div style="text-align: center;">
            <h1 class="title">Popup Dimension Test</h1>
            <p class="subtitle">Testing 600x850px popup dimensions</p>
        </div>
        
        <div class="popup-container">
            <iframe 
                src="./dist/src/popup/index.html" 
                class="popup-frame"
                title="Popup Test">
            </iframe>
        </div>
        
        <div class="note">
            <strong>🔍 Dimension Test:</strong> This iframe simulates the browser extension popup environment 
            with exact 600x850px dimensions. If the popup appears correctly here but not in the extension, 
            the issue is with browser extension popup window sizing.
        </div>
        
        <div class="dimensions-info">
            Expected Dimensions: 600px × 850px<br>
            Container Size: <span id="container-size">Calculating...</span><br>
            Iframe Size: <span id="iframe-size">Calculating...</span>
        </div>
    </div>

    <script>
        function updateDimensions() {
            const container = document.querySelector('.popup-container');
            const iframe = document.querySelector('.popup-frame');
            
            const containerRect = container.getBoundingClientRect();
            const iframeRect = iframe.getBoundingClientRect();
            
            document.getElementById('container-size').textContent = 
                `${Math.round(containerRect.width)}px × ${Math.round(containerRect.height)}px`;
            document.getElementById('iframe-size').textContent = 
                `${Math.round(iframeRect.width)}px × ${Math.round(iframeRect.height)}px`;
        }
        
        window.addEventListener('load', updateDimensions);
        window.addEventListener('resize', updateDimensions);
        
        // Update dimensions every second to catch any changes
        setInterval(updateDimensions, 1000);
    </script>
</body>
</html>
