/**
 * Schema Validation Tests
 * Tests for corrected GraphQL schema types
 */

import type { CurrencyData, UserData, AirtmOperation } from '../types/airtm-schema'

/**
 * Test CurrencyData interface matches actual API
 */
export function testCurrencyDataSchema(): void {
  console.log('Testing CurrencyData schema...')

  // Test valid CurrencyData
  const validCurrency: CurrencyData = {
    id: 'usd',
    symbol: '$',
    precision: 2,
    __typename: 'Catalogs__Currency'
  }

  // Verify required fields exist
  console.assert(typeof validCurrency.id === 'string', 'CurrencyData.id should be string')
  console.assert(typeof validCurrency.symbol === 'string', 'CurrencyData.symbol should be string')
  console.assert(typeof validCurrency.precision === 'number', 'CurrencyData.precision should be number')
  console.assert(validCurrency.__typename === 'Catalogs__Currency', 'CurrencyData.__typename should be Catalogs__Currency')

  // Verify removed fields don't exist in type (compile-time check)
  // These variables test that removed fields can still be added at runtime
  // but are not part of the TypeScript interface
  const _invalidCurrency1 = { ...validCurrency, name: 'US Dollar' }
  const _invalidCurrency2 = { ...validCurrency, code: 'USD' }
  const _invalidCurrency3 = { ...validCurrency, type: 'FIAT' }
  const _invalidCurrency4 = { ...validCurrency, image: 'url' }

  // Suppress unused variable warnings for test cases
  void _invalidCurrency1;
  void _invalidCurrency2;
  void _invalidCurrency3;
  void _invalidCurrency4;

  console.log('✅ CurrencyData schema validation passed')
}

/**
 * Test UserData interface with corrected country field
 */
export function testUserDataSchema(): void {
  console.log('Testing UserData schema...')

  // Test valid UserData
  const validUser: UserData = {
    id: 'user123',
    firstName: 'John',
    lastName: 'Doe',
    createdAt: '2024-01-01T00:00:00Z',
    country: 'US', // Should be simple string
    countryInfo: {
      id: 'US',
      image: {
        id: 'img1',
        urls: {
          avatar: 'https://example.com/flag-avatar.png',
          logo: 'https://example.com/flag-logo.png',
          medium: 'https://example.com/flag-medium.png'
        },
        __typename: 'Files__File'
      },
      __typename: 'Catalogs__Country'
    },
    securityHub: {
      id: 'sec1',
      tierLevel: 2,
      facialVerified: true,
      documentVerified: true,
      verificationStatusName: 'TIER_VERIFIED',
      __typename: 'SecurityHub__User'
    },
    numbers: {
      id: 'num1',
      score: 4.8,
      completedOperations: 150,
      __typename: 'UserNumbers'
    },
    preferences: {
      id: 'pref1',
      profile: {
        id: 'prof1',
        avatar: 'https://example.com/avatar.jpg',
        language: 'en',
        __typename: 'UserProfile'
      },
      __typename: 'UserPreferences'
    },
    __typename: 'Auth__OperationUser'
  }

  // Verify country is string only
  console.assert(typeof validUser.country === 'string', 'UserData.country should be string')
  console.assert(validUser.country === 'US', 'UserData.country should contain country code')

  // Verify nested structures exist
  console.assert(typeof validUser.countryInfo === 'object', 'UserData.countryInfo should be object')
  console.assert(typeof validUser.securityHub === 'object', 'UserData.securityHub should be object')
  console.assert(typeof validUser.numbers === 'object', 'UserData.numbers should be object')
  console.assert(typeof validUser.preferences === 'object', 'UserData.preferences should be object')

  // Test that country cannot be assigned CountryInfo object
  // This would cause a TypeScript error if uncommented:
  // const invalidUser: UserData = {
  //   ...validUser,
  //   country: validUser.countryInfo // This should cause TypeScript error
  // }

  console.log('✅ UserData schema validation passed')
}

/**
 * Test complete AirtmOperation structure
 */
export function testAirtmOperationSchema(): void {
  console.log('Testing AirtmOperation schema...')

  const validOperation: AirtmOperation = {
    id: 'op123',
    hash: 'hash123',
    operationType: 'BUY',
    status: 'CREATED',
    isMine: false,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:01:00Z',
    grossAmount: '100.50',
    netAmount: '95.25',
    metadata: { isForThirdPartyPaymentMethod: false, source: 'test' },
    walletCurrency: {
      id: 'usd',
      symbol: '$',
      precision: 2,
      __typename: 'Catalogs__Currency'
    },
    peer: {
      id: 'user123',
      firstName: 'John',
      lastName: 'Doe',
      createdAt: '2024-01-01T00:00:00Z',
      country: 'US',
      countryInfo: {
        id: 'US',
        image: { id: 'img1', urls: { avatar: '', logo: '', medium: '' }, __typename: 'Files__File' },
        __typename: 'Catalogs__Country'
      },
      securityHub: {
        id: 'sec1',
        tierLevel: 2,
        facialVerified: true,
        documentVerified: false,
        verificationStatusName: 'TIER_VERIFIED',
        __typename: 'SecurityHub__User'
      },
      numbers: {
        id: 'num1',
        score: 4.5,
        completedOperations: 25,
        __typename: 'UserNumbers'
      },
      preferences: {
        id: 'pref1',
        profile: {
          id: 'prof1',
          avatar: '',
          language: 'en',
          __typename: 'UserProfile'
        },
        __typename: 'UserPreferences'
      },
      __typename: 'Auth__User'
    },

    rateInfo: {
      id: 'rate1',
      netAmount: '95.25',
      grossAmount: '100.50',
      fundsToReceiveMaker: '95.25',
      fundsToReceiveTaker: '90.00',
      fundsToSendMaker: '100.50',
      fundsToSendTaker: '100.50',
      __typename: 'Operations__RateInfo'
    },
    displayRate: {
      direction: 'TO_LOCAL_CURRENCY',
  
      __typename: 'Operations__DisplayRate'
    },
    currency: {
      id: 'eur',
      symbol: '€',
      precision: 2,
      __typename: 'Catalogs__Currency'
    },
    makerPaymentMethod: {
      id: 'pm1',
      data: { accountNumber: '****1234' },
      categoryId: 'bank',
      isThirdPartyInstance: false,
      version: {
        id: 'v1',
        image: { id: 'img2', urls: { avatar: '', logo: '', medium: '' }, __typename: 'Files__File' },
        category: {
          id: 'bank_transfer',
          translationTag: 'payment.bank_transfer',
          ancestor: {
            id: 'traditional',
            translationTag: 'category.traditional',
            __typename: 'Category'
          },
          __typename: 'Category'
        },
        __typename: 'PaymentMethodVersion'
      },
      __typename: 'PaymentMethodInstance'
    },
    __typename: 'Operations__Buy'
  }

  // Verify required fields
  console.assert(typeof validOperation.id === 'string', 'AirtmOperation.id should be string')
  console.assert(validOperation.operationType === 'BUY' || validOperation.operationType === 'SELL', 'AirtmOperation.operationType should be BUY or SELL')
  console.assert(typeof validOperation.grossAmount === 'number', 'AirtmOperation.grossAmount should be number')
  console.assert(typeof validOperation.peer.country === 'string', 'AirtmOperation.peer.country should be string')

  // Verify optional fields

  console.assert(validOperation.rateInfo !== undefined, 'AirtmOperation.rateInfo should be present for BUY/SELL')

  console.log('✅ AirtmOperation schema validation passed')
}

/**
 * Test mock GraphQL response parsing
 */
export function testMockGraphQLResponse(): void {
  console.log('Testing mock GraphQL response...')

  const mockResponse = {
    data: {
      availableOperations: [
        {
          id: 'op_buy_001',
          hash: 'abc123def456',
          operationType: 'BUY',
          status: 'CREATED',
          isMine: false,
          createdAt: '2024-01-15T10:30:00Z',
          updatedAt: '2024-01-15T10:31:00Z',
          grossAmount: '105.50',
      netAmount: '100.00',
          metadata: { isForThirdPartyPaymentMethod: false },
          walletCurrency: {
            id: 'eur',
            symbol: '€',
            precision: 2,
            __typename: 'Catalogs__Currency'
          },
          peer: {
            id: 'user_123',
            firstName: 'John',
            lastName: 'Doe',
            createdAt: '2024-01-01T00:00:00Z',
            country: 'US',
            countryInfo: {
              id: 'US',
              image: { id: 'flag_us', urls: { avatar: 'https://example.com/us-avatar.png', logo: 'https://example.com/us-logo.png', medium: 'https://example.com/us-medium.png' }, __typename: 'Files__File' },
              __typename: 'Catalogs__Country'
            },
            securityHub: {
              id: 'sec_123',
              tierLevel: 2,
              facialVerified: true,
              documentVerified: true,
              verificationStatusName: 'TIER_VERIFIED',
              __typename: 'SecurityHub__User'
            },
            numbers: {
              id: 'num_123',
              score: 4.8,
              completedOperations: 150,
              __typename: 'UserNumbers'
            },
            preferences: {
              id: 'pref_123',
              profile: {
                id: 'prof_123',
                avatar: 'https://example.com/avatar.jpg',
                language: 'en',
                __typename: 'UserProfile'
              },
              __typename: 'UserPreferences'
            },
            __typename: 'Auth__OperationUser'
          },
      
          rateInfo: {
            id: 'rate_123',
            netAmount: '100.00',
             grossAmount: '105.50',
            fundsToReceiveMaker: '100.00',
             fundsToReceiveTaker: '94.75',
             fundsToSendMaker: '105.50',
             fundsToSendTaker: '105.50',
            __typename: 'Operations__RateInfo'
          },
          displayRate: {
          direction: 'TO_LOCAL_CURRENCY',
      
            __typename: 'Operations__DisplayRate'
          },
          currency: {
            id: 'usd',
            symbol: '$',
            precision: 2,
            __typename: 'Catalogs__Currency'
          },
          makerPaymentMethod: {
            id: 'pm_001',
            data: { accountNumber: '****1234', bankName: 'Chase Bank' },
            categoryId: 'bank_transfer',
            isThirdPartyInstance: false,
            version: {
              id: 'v1',
              image: { id: 'bank_img', urls: { avatar: 'https://example.com/bank-avatar.png', logo: 'https://example.com/bank-logo.png', medium: 'https://example.com/bank-medium.png' }, __typename: 'Files__File' },
              category: {
                id: 'bank_transfer',
                translationTag: 'payment_method.bank_transfer',
                ancestor: {
                  id: 'traditional_banking',
                  translationTag: 'category.traditional_banking',
                  __typename: 'Category'
                },
                __typename: 'Category'
              },
              __typename: 'PaymentMethodVersion'
            },
            __typename: 'PaymentMethodInstance'
          },
          __typename: 'Operations__Buy'
        }
      ]
    }
  }

  // Verify structure matches our types
  const operation = mockResponse.data.availableOperations[0]
  console.assert(typeof operation.peer.country === 'string', 'Mock response peer.country should be string')
  console.assert(operation.currency.symbol === '$', 'Mock response currency should have symbol')
  console.assert(!('name' in operation.currency), 'Mock response currency should not have name field')
  console.assert(!('code' in operation.currency), 'Mock response currency should not have code field')

  console.log('✅ Mock GraphQL response validation passed')
}

/**
 * Run all schema validation tests
 */
export function runAllSchemaTests(): void {
  console.log('🧪 Running Schema Validation Tests...')
  
  try {
    testCurrencyDataSchema()
    testUserDataSchema()
    testAirtmOperationSchema()
    testMockGraphQLResponse()
    
    console.log('🎉 All schema validation tests passed!')
  } catch (error) {
    console.error('❌ Schema validation tests failed:', error)
    throw error
  }
}

// Run tests if this file is executed directly
if (typeof window !== 'undefined' && window.location?.href?.includes('test')) {
  runAllSchemaTests()
}
