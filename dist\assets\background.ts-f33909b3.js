var Y=Object.defineProperty;var K=(e,t,o)=>t in e?Y(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o;var E=(e,t,o)=>(K(e,typeof t!="symbol"?t+"":t,o),o);import{D as O}from"./index-357a2da0.js";import{A as W}from"./AirTMPaymentMethodMatcher-d2f6c65f.js";const h=class h{constructor(){E(this,"activeNotifications",new Map);E(this,"settings",{autoCloseDelay:3e4,playSound:!0})}static getInstance(){return h.instance||(h.instance=new h),h.instance}updateSettings(t){this.settings={...this.settings,...t}}async showNotification(t,o){const n={...this.settings,...o};try{await this.showChromeNotification(t,n),await this.highlightOfferOnPage(t),n.playSound&&await this.playNotificationSound()}catch(s){console.error("Error showing notification:",s)}}async showChromeNotification(t,o){var r,f;if(typeof chrome>"u"||!chrome.notifications)throw new Error("Chrome notifications not available");const n=`offer_${t.id}_${Date.now()}`,s=`New ${t.operationType} Offer`,c=`${t.grossAmount} ${((r=t.currency)==null?void 0:r.symbol)||((f=t.walletCurrency)==null?void 0:f.symbol)}`;await chrome.notifications.create(n,{type:"basic",iconUrl:"icons/icon48.png",title:s,message:c,contextMessage:t.peer?`From: ${t.peer.firstName} ${t.peer.lastName}`:void 0,buttons:[{title:"Accept"},{title:"Ignore"}],requireInteraction:!0}),this.activeNotifications.set(t.id,n),setTimeout(()=>{this.closeNotification(t.id)},o.autoCloseDelay)}async highlightOfferOnPage(t){var o;try{const n=await chrome.tabs.query({active:!0,currentWindow:!0});(o=n[0])!=null&&o.id&&await chrome.tabs.sendMessage(n[0].id,{type:"HIGHLIGHT_OFFER",data:{offer:t}})}catch(n){console.log("Could not highlight offer on page:",n)}}closeNotification(t){const o=this.activeNotifications.get(t);if(o){try{typeof o=="string"&&o.startsWith("offer_")&&chrome.notifications.clear(o)}catch(n){console.error("Error closing notification:",n)}this.activeNotifications.delete(t)}}closeAllNotifications(){for(const[t]of this.activeNotifications)this.closeNotification(t)}async playNotificationSound(){var t;try{if(typeof chrome<"u"&&chrome.offscreen){try{await chrome.offscreen.createDocument({url:"sounds/beep.html",reasons:["AUDIO_PLAYBACK"],justification:"Play notification sound"})}catch(o){if(!((t=o.message)!=null&&t.includes("Only a single offscreen document")))throw o}chrome.runtime.sendMessage({type:"PLAY_SOUND"})}}catch(o){console.error("Error playing notification sound:",o)}}handleMessage(t,o){var n,s;switch(t.type){case"ACCEPT_OFFER":this.handleOfferAction("accept",(n=t.data)==null?void 0:n.offerId);break;case"IGNORE_OFFER":this.handleOfferAction("ignore",(s=t.data)==null?void 0:s.offerId);break}}async handleOfferAction(t,o){if(o)try{if(this.closeNotification(o),typeof chrome<"u"&&chrome.runtime){const n=t==="accept"?"ACCEPT_OFFER":"REJECT_OFFER";await chrome.runtime.sendMessage({type:n,data:{offerId:o}})}}catch(n){console.error(`Error handling ${t} action:`,n)}}static isSupported(){return typeof chrome<"u"&&!!chrome.notifications}getAvailableMethods(){const t=[];return typeof chrome<"u"&&chrome.notifications&&t.push("chrome"),t}};E(h,"instance");let A=h;const b=A.getInstance();let a=O;const L=new W,u={totalOffers:0,newOffers:0,acceptedOffers:0,rejectedOffers:0,averageRate:0,lastUpdate:new Date().toISOString()};let g=new Map,F=Date.now();const H=2*60*1e3,J=60*60*1e3;async function q(){try{const e=await chrome.storage.local.get("notified_offers");e.notified_offers?(g=new Map(Object.entries(e.notified_offers)),console.log(`Loaded ${g.size} notified offers from storage`)):(g=new Map,console.log("No previously notified offers found in storage"))}catch(e){console.error("Error loading notified offers:",e),g=new Map}}async function B(){try{const e=Object.fromEntries(g);await chrome.storage.local.set({notified_offers:e})}catch(e){console.error("Error saving notified offers:",e)}}let U=!1;async function S(){var e,t;if(U){console.log("Background service worker already initialized, skipping...");return}console.log("Airtm Monitor Pro: Background service worker initializing...");try{if(typeof chrome>"u"||!chrome.runtime)throw new Error("Chrome runtime APIs not available");await q(),await ye(),V(),Oe(),Ae(),await Te(),G(),U=!0,console.log("Background service worker initialized successfully")}catch(o){console.error("Error initializing background service worker:",o);try{(e=chrome.action)==null||e.setBadgeText({text:"!"}),(t=chrome.action)==null||t.setBadgeBackgroundColor({color:"#ff0000"})}catch(n){console.error("Could not set error badge:",n)}}}function V(){chrome.runtime.onMessage.hasListeners()&&chrome.runtime.onMessage.removeListener,chrome.runtime.onMessage.addListener((e,t,o)=>{var n,s,c;if(console.log("Background received message:",e.type,"from:",((n=t.tab)==null?void 0:n.url)||"popup/options"),!e||typeof e.type!="string"){console.error("Invalid message received:",e),o({success:!1,error:"Invalid message format"});return}try{switch(e.type){case"OFFERS_UPDATE":return te(e.data).then(()=>o({success:!0})).catch(r=>{console.error("Error handling offers update:",r),o({success:!1,error:r.message})}),!0;case"SETTINGS_UPDATE":return Ee(e.data).then(()=>o({success:!0})).catch(r=>{console.error("Error handling settings update:",r),o({success:!1,error:r.message})}),!0;case"ACCEPT_OFFER":return C((s=e.data)==null?void 0:s.offerId).then(r=>o({success:!0,result:r})).catch(r=>{console.error("Error handling accept offer:",r),o({success:!1,error:r.message})}),!0;case"REJECT_OFFER":return z((c=e.data)==null?void 0:c.offerId).then(r=>o({success:!0,result:r})).catch(r=>{console.error("Error handling reject offer:",r),o({success:!1,error:r.message})}),!0;case"OPERATION_DETAILS_UPDATE":return le(e.data).then(()=>o({success:!0})).catch(r=>{console.error("Error handling operation details update:",r),o({success:!1,error:r.message})}),!0;case"OPERATION_ACCEPTED":return fe(e.data).then(()=>o({success:!0})).catch(r=>{console.error("Error handling operation accepted:",r),o({success:!1,error:r.message})}),!0;case"OPERATION_NOT_AVAILABLE":return ge(e.data).then(()=>o({success:!0})).catch(r=>{console.error("Error handling operation not available:",r),o({success:!1,error:r.message})}),!0;case"OPERATION_ACCEPT_ERROR":return me(e.data).then(()=>o({success:!0})).catch(r=>{console.error("Error handling operation accept error:",r),o({success:!1,error:r.message})}),!0;case"SEND_TELEGRAM_OPERATION_UPDATE":return we(e.data).then(()=>o({success:!0})).catch(r=>{console.error("Error sending Telegram operation update:",r),o({success:!1,error:r.message})}),!0;case"OPERATION_DECLINED":return he(e.data).then(()=>o({success:!0})).catch(r=>{console.error("Error handling operation declined:",r),o({success:!1,error:r.message})}),!0;case"OPERATION_DECLINE_ERROR":return pe(e.data).then(()=>o({success:!0})).catch(r=>{console.error("Error handling operation decline error:",r),o({success:!1,error:r.message})}),!0;case"GET_POPUP_DATA":return Z().then(r=>o({success:!0,data:r})).catch(r=>{console.error("Error handling get popup data:",r),o({success:!1,error:r.message})}),!0;case"PING":console.log("Ping received, responding with alive status"),o({success:!0,message:"Background script is alive",timestamp:new Date().toISOString()});break;default:console.warn("Unknown message type:",e.type),o({success:!1,error:"Unknown message type: "+e.type})}}catch(r){console.error("Error in message listener:",r),o({success:!1,error:"Internal error: "+(r instanceof Error?r.message:"Unknown error")})}}),chrome.runtime.onConnect.addListener(e=>{console.log("Port connected:",e.name),e.onDisconnect.addListener(()=>{console.log("Port disconnected:",e.name),chrome.runtime.lastError&&console.error("Port disconnect error:",chrome.runtime.lastError)})}),chrome.commands&&chrome.commands.onCommand&&chrome.commands.onCommand.addListener(e=>{console.log("Chrome command received:",e),chrome.tabs.query({url:"*://app.airtm.com/peer-transfers/available*"},t=>{t.length>0&&t[0].id?chrome.tabs.sendMessage(t[0].id,{type:"EXECUTE_COMMAND",command:e}).catch(o=>{console.log("Could not send command to content script:",o)}):console.log("No active Airtm tab found for command:",e)})})}async function Z(){try{const t=(await chrome.storage.local.get("current_offers")).current_offers||[];return console.log("Sending popup data:",{offersCount:t.length,settings:a,stats:u}),{offers:t,settings:a,stats:u}}catch(e){throw console.error("Error getting popup data:",e),e}}function Q(){const e=Date.now();if(e-F<H)return;const t=e-J;let o=0;for(const[n,s]of g.entries())s<t&&(g.delete(n),o++);F=e,o>0&&(console.log(`🧹 Cleaned up ${o} old notified offers (older than 1 hour)`),B())}function X(e){return g.has(e)}function ee(e){g.set(e,Date.now()),B()}async function te(e){try{const{offers:t,timestamp:o}=e;if(!Array.isArray(t))throw new Error("Invalid offers data");if(console.log("Processing "+t.length+" offers"),await chrome.storage.local.set({current_offers:t}),Q(),!a.monitoring){console.log("Monitoring disabled, skipping offer processing"),$(t),u.lastUpdate=o||new Date().toISOString();try{chrome.runtime.sendMessage({type:"OFFERS_PROCESSED",data:{offers:t,stats:u}}).catch(c=>{console.log("Could not notify popup (popup may be closed):",c.message)})}catch(c){console.log("Error sending OFFERS_PROCESSED message:",c)}return}$(t);const n=oe(t);console.log(n.length+" offers passed filters");const s=n.filter(c=>!X(c.id));console.log(s.length+" new offers (not previously notified)"),s.length>0&&a.soundEnabled&&a.notificationsEnabled&&(console.log("Playing sound notification once for "+s.length+" new offers"),await x());for(const c of s)ee(c.id),await ne(c,!1);u.lastUpdate=o||new Date().toISOString();try{chrome.runtime.sendMessage({type:"OFFERS_PROCESSED",data:{offers:t,stats:u}}).catch(c=>{console.log("Could not notify popup (popup may be closed):",c.message)})}catch(c){console.log("Error sending OFFERS_PROCESSED message:",c)}}catch(t){console.error("Error handling offers update:",t)}}function oe(e){return e.filter(t=>{var n,s,c,r,f,l;const o=typeof t.grossAmount=="string"?parseFloat(t.grossAmount):t.grossAmount;if(o<a.minAmount||o>a.maxAmount)return!1;if(a.preferredCurrencies.length>0){const d=((n=t.currency)==null?void 0:n.symbol)||((s=t.walletCurrency)==null?void 0:s.symbol)||"";if(!a.preferredCurrencies.includes(d))return!1}if(a.paymentMethods.length>0){const d=((f=(r=(c=t.makerPaymentMethod)==null?void 0:c.version)==null?void 0:r.category)==null?void 0:f.translationTag)||"";if(a.fuzzyMatching.enabled){const i=L.matchAnyPaymentMethod(d,a.paymentMethods);if(i.isMatch)console.log(`Offer ${t.id} matched payment method "${d}" with alias "${i.matchedAlias}" (score: ${i.score.toFixed(2)})`);else return console.log(`Offer ${t.id} filtered out: payment method "${d}" doesn't match any configured methods (best score: ${i.score.toFixed(2)})`),!1}else{let i=d;if(i.startsWith("CATEGORY_TREE:AIRTM_")&&(i=i.replace("CATEGORY_TREE:AIRTM_","")),i.startsWith("E_TRANSFER_")&&(i=i.replace("E_TRANSFER_","")),!a.paymentMethods.some(y=>i.includes(y)))return console.log(`Offer ${t.id} filtered out: payment method "${i}" doesn't match any configured methods (legacy matching)`),!1}}if(a.countries.length>0){const d=((l=t.peer)==null?void 0:l.country)||"";if(!a.countries.includes(d))return!1}if(a.keywords.length>0){const d=JSON.stringify(t).toLowerCase();if(!a.keywords.some(i=>d.includes(i.toLowerCase())))return!1}if(a.blacklistKeywords.length>0){const d=JSON.stringify(t).toLowerCase();if(a.blacklistKeywords.some(i=>d.includes(i.toLowerCase())))return console.log(`Offer ${t.id} filtered out by blacklist keyword`),!1}return!0})}async function re(e){try{const t=await chrome.tabs.query({url:"*://app.airtm.com/peer-transfers/available*"});if(t.length===0){console.log("No Airtm tab found, opening new tab");const n=await chrome.tabs.create({url:"https://app.airtm.com/peer-transfers/available",active:!0});if(await new Promise(s=>setTimeout(s,3e3)),n.windowId)try{await chrome.windows.update(n.windowId,{focused:!0,state:"maximized"}),console.log("✅ New window maximized and focused")}catch(s){console.warn("⚠️ Failed to maximize new window:",s);try{await chrome.windows.update(n.windowId,{focused:!0}),console.log("✅ New window focused (without maximizing)")}catch(c){console.error("❌ Failed to focus new window:",c)}}return}const o=t[0];if(o.windowId){try{await chrome.windows.update(o.windowId,{focused:!0,state:"maximized"}),console.log("✅ Window maximized and focused for offer:",e.id)}catch(s){console.warn("⚠️ Failed to maximize window:",s);try{await chrome.windows.update(o.windowId,{focused:!0}),console.log("✅ Window focused (without maximizing) for offer:",e.id)}catch(c){console.error("❌ Failed to focus window:",c)}}let n=!1;for(let s=1;s<=3;s++)try{await chrome.tabs.update(o.id,{active:!0}),console.log(`✅ Tab activated (attempt ${s}) for offer:`,e.id),n=!0;break}catch(c){console.warn(`⚠️ Tab activation attempt ${s} failed:`,c),s<3&&await new Promise(r=>setTimeout(r,500))}n||console.error("❌ Failed to activate tab after 3 attempts"),await new Promise(s=>setTimeout(s,1e3));try{await chrome.tabs.sendMessage(o.id,{type:"FOCUS_OFFER",data:{offerId:e.id}}),console.log("✅ Focus message sent to content script for offer:",e.id)}catch(s){console.log("⚠️ Could not send focus message to content script:",s)}}else console.error("❌ No window ID found for Airtm tab")}catch(t){console.error("❌ Error maximizing window and focusing offer:",t)}}async function ne(e,t=!0){try{console.log("Processing offer "+e.id),await re(e),a.notificationsEnabled&&await ae(e,t),console.log("Checking Telegram settings:",{botToken:a.telegramBotToken?"***SET***":"NOT SET",chatId:a.telegramChatId?"***SET***":"NOT SET"}),a.telegramBotToken&&a.telegramChatId?(console.log("Sending Telegram message for offer:",e.id),await w(e)):console.log("Telegram not configured - skipping message"),a.webhookUrl?(console.log("Sending webhook message for offer:",e.id),await se(e)):console.log("Webhook not configured - skipping webhook"),a.autoAccept&&await C(e.id)}catch(o){console.error("Error processing offer "+e.id+":",o)}}async function ae(e,t=!0){var o,n,s,c;try{b.updateSettings({autoCloseDelay:3e4,playSound:a.soundEnabled&&t}),await b.showNotification(e)}catch(r){console.error("Error sending notification:",r);try{const f="New "+e.operationType+" Offer",l=e.grossAmount+" "+(((o=e.currency)==null?void 0:o.symbol)||((n=e.walletCurrency)==null?void 0:n.symbol));await chrome.notifications.create({type:"basic",iconUrl:"icons/icon48.png",title:f,message:l,contextMessage:"From: "+(((s=e.peer)==null?void 0:s.firstName)||"")+" "+(((c=e.peer)==null?void 0:c.lastName)||""),buttons:[{title:"Accept"},{title:"Reject"}]}),a.soundEnabled&&t&&await x()}catch(f){console.error("Fallback notification also failed:",f)}}}async function w(e){try{const t=typeof e=="string"?e:ie(e),o="https://api.telegram.org/bot"+a.telegramBotToken+"/sendMessage";console.log("Sending Telegram message to:",a.telegramChatId),console.log("Message content:",t);const n=await fetch(o,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({chat_id:a.telegramChatId,text:t,parse_mode:"Markdown"})}),s=await n.json();n.ok?console.log("✅ Telegram message sent successfully:",s):console.error("❌ Telegram API error:",s)}catch(t){console.error("❌ Error sending Telegram message:",t)}}async function se(e){try{const t={timestamp:new Date().toISOString(),offer:e,event:"new_offer",metadata:{extensionVersion:"1.0.0",source:"airtm-monitor-pro"}};console.log("Sending webhook to:",a.webhookUrl),console.log("Webhook payload:",t);const o=await fetch(a.webhookUrl,{method:"POST",headers:{"Content-Type":"application/json","User-Agent":"Airtm-Monitor-Pro/1.0.0"},body:JSON.stringify(t)});o.ok?console.log("✅ Webhook sent successfully:",o.status):console.error("❌ Webhook failed with status:",o.status,await o.text())}catch(t){console.error("❌ Error sending webhook:",t)}}function ce(e){return{USA:"🇺🇸",US:"🇺🇸",IND:"🇮🇳",IN:"🇮🇳",GBR:"🇬🇧",GB:"🇬🇧",UK:"🇬🇧",CAN:"🇨🇦",CA:"🇨🇦",AUS:"🇦🇺",AU:"🇦🇺",DEU:"🇩🇪",DE:"🇩🇪",GER:"🇩🇪",FRA:"🇫🇷",FR:"🇫🇷",ESP:"🇪🇸",ES:"🇪🇸",ITA:"🇮🇹",IT:"🇮🇹",BRA:"🇧🇷",BR:"🇧🇷",MEX:"🇲🇽",MX:"🇲🇽",ARG:"🇦🇷",AR:"🇦🇷",COL:"🇨🇴",CO:"🇨🇴",VEN:"🇻🇪",VE:"🇻🇪",PER:"🇵🇪",PE:"🇵🇪",CHL:"🇨🇱",CL:"🇨🇱",URY:"🇺🇾",UY:"🇺🇾",ECU:"🇪🇨",EC:"🇪🇨",BOL:"🇧🇴",BO:"🇧🇴",PRY:"🇵🇾",PY:"🇵🇾",CHN:"🇨🇳",CN:"🇨🇳",JPN:"🇯🇵",JP:"🇯🇵",KOR:"🇰🇷",KR:"🇰🇷",RUS:"🇷🇺",RU:"🇷🇺",TUR:"🇹🇷",TR:"🇹🇷",EGY:"🇪🇬",EG:"🇪🇬",ZAF:"🇿🇦",ZA:"🇿🇦",NGA:"🇳🇬",NG:"🇳🇬",KEN:"🇰🇪",KE:"🇰🇪",GHA:"🇬🇭",GH:"🇬🇭",MAR:"🇲🇦",MA:"🇲🇦",TUN:"🇹🇳",TN:"🇹🇳",DZA:"🇩🇿",DZ:"🇩🇿",IDN:"🇮🇩",ID:"🇮🇩",THA:"🇹🇭",TH:"🇹🇭",VNM:"🇻🇳",VN:"🇻🇳",PHL:"🇵🇭",PH:"🇵🇭",MYS:"🇲🇾",MY:"🇲🇾",SGP:"🇸🇬",SG:"🇸🇬",PAK:"🇵🇰",PK:"🇵🇰",BGD:"🇧🇩",BD:"🇧🇩",LKA:"🇱🇰",LK:"🇱🇰",NPL:"🇳🇵",NP:"🇳🇵",AFG:"🇦🇫",AF:"🇦🇫",IRN:"🇮🇷",IR:"🇮🇷",IRQ:"🇮🇶",IQ:"🇮🇶",SAU:"🇸🇦",SA:"🇸🇦",ARE:"🇦🇪",AE:"🇦🇪",QAT:"🇶🇦",QA:"🇶🇦",KWT:"🇰🇼",KW:"🇰🇼",BHR:"🇧🇭",BH:"🇧🇭",OMN:"🇴🇲",OM:"🇴🇲",JOR:"🇯🇴",JO:"🇯🇴",LBN:"🇱🇧",LB:"🇱🇧",SYR:"🇸🇾",SY:"🇸🇾",ISR:"🇮🇱",IL:"🇮🇱",PSE:"🇵🇸",PS:"🇵🇸"}[e.toUpperCase()]||e}function ie(e){var d,i,y,N,_,I,P,D,k,M,R,v;const t=((d=e.currency)==null?void 0:d.symbol)||((i=e.walletCurrency)==null?void 0:i.symbol)||"Unknown",o=((((y=e.peer)==null?void 0:y.firstName)||"")+" "+(((N=e.peer)==null?void 0:N.lastName)||"")).trim(),n=((I=(_=e.peer)==null?void 0:_.numbers)==null?void 0:I.score)||0,s=((D=(P=e.peer)==null?void 0:P.numbers)==null?void 0:D.completedOperations)||0,c=((k=e.peer)==null?void 0:k.country)||"Unknown",r=ce(c);let l=((v=(R=(M=e.makerPaymentMethod)==null?void 0:M.version)==null?void 0:R.category)==null?void 0:v.translationTag)||"Unknown";return l.startsWith("CATEGORY_TREE:AIRTM_")&&(l=l.replace("CATEGORY_TREE:AIRTM_","")),l.startsWith("E_TRANSFER_")&&(l=l.replace("E_TRANSFER_","")),l=l.replace(/_/g," ").toLowerCase().replace(/\b\w/g,j=>j.toUpperCase()),`${e.grossAmount} ${t} : ${l} (${r})
👤 User: ${o} ${n}⭐(${s} trades)`}let m=!1,p=null;async function x(){return p||(p=(async()=>{var e;try{if(!m)try{await chrome.offscreen.createDocument({url:"sounds/beep.html",reasons:[chrome.offscreen.Reason.AUDIO_PLAYBACK],justification:"Play notification sound for new offers"}),m=!0}catch(t){if((e=t.message)!=null&&e.includes("Only a single offscreen document may be created"))m=!0;else throw t}try{await chrome.runtime.sendMessage({type:"PLAY_SOUND"})}catch(t){throw console.log("Message to offscreen document failed:",t),m=!1,t}}catch(t){console.error("Error playing notification sound:",t),m=!1}finally{try{m&&(await chrome.offscreen.closeDocument(),m=!1)}catch{m=!1}p=null}})(),p)}async function C(e){try{const t=await chrome.tabs.query({url:"*://app.airtm.com/peer-transfers/available*"});if(t.length===0)throw new Error("Airtm tab not found");const o=await chrome.tabs.sendMessage(t[0].id,{type:"ACCEPT_OFFER",data:{offerId:e}});if(o!=null&&o.success)return u.acceptedOffers++,console.log("Offer "+e+" accepted successfully"),de(e,t[0].id),!0;throw new Error((o==null?void 0:o.error)||"Failed to accept offer")}catch(t){throw console.error("Error accepting offer "+e+":",t),t}}async function le(e){try{console.log("📋 Processing operation details update:",e);const{operation:t,timestamp:o,source:n}=e;if(!t||!t.id){console.warn("Invalid operation data received");return}await chrome.storage.local.set({[`operation_${t.id}`]:{...t,lastUpdated:o,source:n}}),a.telegramBotToken&&a.telegramChatId&&await ue(t),console.log(`✅ Operation ${t.id} details updated - Status: ${t.status}`)}catch(t){console.error("❌ Error handling operation details update:",t)}}function de(e,t){console.log(`🔍 Starting URL monitoring for accepted offer: ${e}`);let o=0;const n=30,s=setInterval(async()=>{try{o++;const r=(await chrome.tabs.get(t)).url;if(console.log(`🔍 URL check ${o}/${n}: ${r}`),r&&/https:\/\/app\.airtm\.com\/operations\/[A-Z0-9]+/.test(r)){console.log("✅ Operation URL detected:",r);const l=r.split("/operations/")[1];a.telegramBotToken&&a.telegramChatId&&await w(`🎯 Offer ${e} accepted successfully!
📋 Operation ID: ${l}
🔗 URL: ${r}
⏰ Status: Accepted`),await chrome.storage.local.set({[`accepted_operation_${e}`]:{operationId:l,url:r,timestamp:new Date().toISOString(),status:"accepted"}}),clearInterval(s);return}o>=n&&(console.log("⚠️ Operation URL not detected within timeout"),a.telegramBotToken&&a.telegramChatId&&await w(`❌ Offer ${e} acceptance failed or timed out
⏰ Status: Not Available`),await chrome.storage.local.set({[`failed_operation_${e}`]:{timestamp:new Date().toISOString(),status:"not_available",reason:"url_not_detected"}}),clearInterval(s))}catch(c){console.error("❌ Error during URL monitoring:",c),clearInterval(s)}},100)}async function ue(e){try{const t=`📋 Operation Update
🆔 ID: ${e.id}
📊 Status: ${e.status}
💰 Amount: ${e.grossAmount||"N/A"}
🔄 Type: ${e.operationType||"N/A"}
⏰ Updated: ${new Date().toLocaleString()}`;await w(t)}catch(t){console.error("❌ Error sending Telegram operation update:",t)}}async function fe(e){try{console.log("✅ Processing operation acceptance:",e),await chrome.storage.local.set({[`accepted_operation_${e.operationId}`]:{...e,processedAt:new Date().toISOString()}}),u.acceptedOffers++,console.log("✅ Operation acceptance processed successfully")}catch(t){console.error("❌ Error handling operation accepted:",t)}}async function ge(e){try{console.log("🚫 Processing operation not available:",e),await chrome.storage.local.set({[`unavailable_operation_${e.operationId||"unknown"}`]:{...e,processedAt:new Date().toISOString()}}),console.log("🚫 Operation not available processed successfully")}catch(t){console.error("❌ Error handling operation not available:",t)}}async function me(e){try{console.log("⚠️ Processing operation accept error:",e),await chrome.storage.local.set({[`error_operation_${Date.now()}`]:{...e,processedAt:new Date().toISOString()}}),console.log("⚠️ Operation accept error processed successfully")}catch(t){console.error("❌ Error handling operation accept error:",t)}}async function he(e){try{console.log("🚫 Processing operation decline:",e),await chrome.storage.local.set({[`declined_operation_${Date.now()}`]:{...e,processedAt:new Date().toISOString()}}),u.rejectedOffers++,console.log("🚫 Operation decline processed successfully")}catch(t){console.error("❌ Error handling operation declined:",t)}}async function pe(e){try{console.log("⚠️ Processing operation decline error:",e),await chrome.storage.local.set({[`decline_error_operation_${Date.now()}`]:{...e,processedAt:new Date().toISOString()}}),console.log("⚠️ Operation decline error processed successfully")}catch(t){console.error("❌ Error handling operation decline error:",t)}}async function we(e){try{console.log("📱 Sending Telegram operation status update:",e);const{operationId:t,status:o}=e;let n;o==="accepted"?n=`✅ *Operation Accepted*

🆔 Operation ID: ${t}
📊 Status: Successfully accepted
⏰ Time: ${new Date().toLocaleString()}

🎉 Great! The operation was accepted successfully.`:o==="Not Available"?n=`🚫 *Operation Not Available*

🆔 Operation ID: ${t}
📊 Status: No longer available
⏰ Time: ${new Date().toLocaleString()}

😔 The operation was taken by someone else or expired.`:n=`📋 *Operation Update*

🆔 Operation ID: ${t}
📊 Status: ${o}
⏰ Time: ${new Date().toLocaleString()}`,await w(n),console.log("📱 Telegram operation status update sent successfully")}catch(t){console.error("❌ Error sending Telegram operation status update:",t)}}async function z(e){try{const t=await chrome.tabs.query({url:"*://app.airtm.com/peer-transfers/available*"});if(t.length===0)throw new Error("Airtm tab not found");const o=await chrome.tabs.sendMessage(t[0].id,{type:"REJECT_OFFER",data:{offerId:e}});if(o!=null&&o.success)return u.rejectedOffers++,console.log("Offer "+e+" rejected successfully"),!0;throw new Error((o==null?void 0:o.error)||"Failed to reject offer")}catch(t){throw console.error("Error rejecting offer "+e+":",t),t}}function $(e){u.totalOffers=e.length,u.newOffers=e.filter(t=>{const o=new Date(t.createdAt),n=new Date(Date.now()-5*60*1e3);return o>n}).length,u.averageRate=0}async function ye(){try{const e=await chrome.storage.sync.get("settings");a={...O,...e.settings},a.monitoring=!0,T(),await chrome.storage.sync.set({settings:a}),console.log("Settings loaded and monitoring enabled:",a)}catch(e){console.error("Error loading settings:",e),a={...O,monitoring:!0},T()}}async function Ee(e){try{a={...a,...e},e.fuzzyMatching&&T(),await chrome.storage.sync.set({settings:a}),console.log("Settings updated:",a),G()}catch(t){throw console.error("Error updating settings:",t),t}}function G(){a.monitoring?(chrome.action.setBadgeText({text:"ON"}),chrome.action.setBadgeBackgroundColor({color:"#10b981"})):(chrome.action.setBadgeText({text:"OFF"}),chrome.action.setBadgeBackgroundColor({color:"#6b7280"}))}function T(){try{const e=a.fuzzyMatching||{enabled:!1,threshold:.8,enableAliases:!0,customAliases:{}};L.updateConfig({threshold:e.threshold,enableAliases:e.enableAliases,customAliases:e.customAliases}),console.log("Fuzzy matcher configuration updated:",e)}catch(e){console.error("Error updating fuzzy matcher configuration:",e)}}function Oe(){chrome.alarms.onAlarm.addListener(e=>{switch(console.log("Alarm triggered:",e.name),e.name){case"monitoring-check":break;case"cleanup-storage":be();break}}),chrome.alarms.create("cleanup-storage",{periodInMinutes:60})}function Ae(){chrome.notifications.onButtonClicked.addListener(async(e,t)=>{try{const o=e.match(/offer_(.+)_\d+/),n=o?o[1]:e;t===0?await C(n):t===1&&await z(n),chrome.notifications.clear(e)}catch(o){console.error("Error handling notification button click:",o)}}),chrome.runtime.onMessage.addListener((e,t,o)=>{(e.type==="ACCEPT_OFFER"||e.type==="REJECT_OFFER"||e.type==="IGNORE_OFFER")&&b.handleMessage(e,t)})}async function be(){try{console.log("Cleaning up storage...");const t=(await chrome.storage.local.get(["airtm_offers_timestamp"])).airtm_offers_timestamp;if(t){const o=Date.now()-new Date(t).getTime(),n=24*60*60*1e3;o>n&&(await chrome.storage.local.remove(["airtm_offers","airtm_offers_timestamp"]),console.log("Old offers cleaned up"))}}catch(e){console.error("Error cleaning up storage:",e)}}async function Te(){try{console.log("🔍 Checking for existing Airtm tabs...");const e=await chrome.tabs.query({url:"*://app.airtm.com/peer-transfers/available*"});if(e.length===0){console.log("📭 No existing Airtm tabs found");return}console.log(`🎯 Found ${e.length} existing Airtm tab(s)`),console.log("ℹ️ Content scripts will be automatically injected by Chrome based on manifest.json");for(const t of e)t.id&&t.url&&console.log(`📋 Found Airtm tab ${t.id}: ${t.url}`)}catch(e){console.error("❌ Error checking existing tabs:",e)}}function Se(){const e=()=>{var t;try{(t=chrome==null?void 0:chrome.runtime)!=null&&t.getPlatformInfo&&chrome.runtime.getPlatformInfo(()=>{chrome.runtime.lastError&&console.warn("Keep alive error:",chrome.runtime.lastError)})}catch(o){console.warn("Keep alive failed:",o)}};e(),setInterval(e,2e4)}S().then(()=>{Se()});chrome.runtime.onStartup.addListener(()=>{console.log("Service worker starting up..."),S()});chrome.runtime.onInstalled.addListener(e=>{console.log("Extension installed/updated:",e.reason),S()});
