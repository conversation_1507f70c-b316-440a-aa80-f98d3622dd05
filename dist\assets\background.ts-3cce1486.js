var W=Object.defineProperty;var Y=(t,e,o)=>e in t?W(t,e,{enumerable:!0,configurable:!0,writable:!0,value:o}):t[e]=o;var p=(t,e,o)=>(Y(t,typeof e!="symbol"?e+"":e,o),o);import{D as A}from"./index-357a2da0.js";const m=class m{constructor(){p(this,"activeNotifications",new Map);p(this,"settings",{autoCloseDelay:3e4,playSound:!0})}static getInstance(){return m.instance||(m.instance=new m),m.instance}updateSettings(e){this.settings={...this.settings,...e}}async showNotification(e,o){const r={...this.settings,...o};try{await this.showChromeNotification(e,r),await this.highlightOfferOnPage(e),r.playSound&&await this.playNotificationSound()}catch(n){console.error("Error showing notification:",n)}}async showChromeNotification(e,o){var a,c;if(typeof chrome>"u"||!chrome.notifications)throw new Error("Chrome notifications not available");const r=`offer_${e.id}_${Date.now()}`,n=`New ${e.operationType} Offer`,s=`${e.grossAmount} ${((a=e.currency)==null?void 0:a.symbol)||((c=e.walletCurrency)==null?void 0:c.symbol)}`;await chrome.notifications.create(r,{type:"basic",iconUrl:"icons/icon48.png",title:n,message:s,contextMessage:e.peer?`From: ${e.peer.firstName} ${e.peer.lastName}`:void 0,buttons:[{title:"Accept"},{title:"Ignore"}],requireInteraction:!0}),this.activeNotifications.set(e.id,r),setTimeout(()=>{this.closeNotification(e.id)},o.autoCloseDelay)}async highlightOfferOnPage(e){var o;try{const r=await chrome.tabs.query({active:!0,currentWindow:!0});(o=r[0])!=null&&o.id&&await chrome.tabs.sendMessage(r[0].id,{type:"HIGHLIGHT_OFFER",data:{offer:e}})}catch(r){console.log("Could not highlight offer on page:",r)}}closeNotification(e){const o=this.activeNotifications.get(e);if(o){try{typeof o=="string"&&o.startsWith("offer_")&&chrome.notifications.clear(o)}catch(r){console.error("Error closing notification:",r)}this.activeNotifications.delete(e)}}closeAllNotifications(){for(const[e]of this.activeNotifications)this.closeNotification(e)}async playNotificationSound(){var e;try{if(typeof chrome<"u"&&chrome.offscreen){try{await chrome.offscreen.createDocument({url:"sounds/beep.html",reasons:["AUDIO_PLAYBACK"],justification:"Play notification sound"})}catch(o){if(!((e=o.message)!=null&&e.includes("Only a single offscreen document")))throw o}chrome.runtime.sendMessage({type:"PLAY_SOUND"})}}catch(o){console.error("Error playing notification sound:",o)}}handleMessage(e,o){var r,n;switch(e.type){case"ACCEPT_OFFER":this.handleOfferAction("accept",(r=e.data)==null?void 0:r.offerId);break;case"IGNORE_OFFER":this.handleOfferAction("ignore",(n=e.data)==null?void 0:n.offerId);break}}async handleOfferAction(e,o){if(o)try{if(this.closeNotification(o),typeof chrome<"u"&&chrome.runtime){const r=e==="accept"?"ACCEPT_OFFER":"REJECT_OFFER";await chrome.runtime.sendMessage({type:r,data:{offerId:o}})}}catch(r){console.error(`Error handling ${e} action:`,r)}}static isSupported(){return typeof chrome<"u"&&!!chrome.notifications}getAvailableMethods(){const e=[];return typeof chrome<"u"&&chrome.notifications&&e.push("chrome"),e}};p(m,"instance");let O=m;const b=O.getInstance();class K{constructor(e={}){p(this,"config");p(this,"defaultAliases",{paypal:["paypal","pp","pay pal"],zelle:["zelle","zell","zele"],venmo:["venmo","venm","vemo"],cashapp:["cashapp","cash app","cash-app","$cashtag"],wise:["wise","transferwise","transfer wise"],revolut:["revolut","revolt","revol"],skrill:["skrill","skril","skrilla"],neteller:["neteller","neteler","netelr"],perfectmoney:["perfectmoney","perfect money","pm","perfect-money"],webmoney:["webmoney","web money","wm","web-money"],bitcoin:["bitcoin","btc","bit coin"],ethereum:["ethereum","eth","ether"],usdt:["usdt","tether","usd-t"],binance:["binance","bnb","binance pay"],bank:["bank","banking","bank transfer","wire"],credit:["credit","credit card","cc","visa","mastercard"],debit:["debit","debit card","dc"]});this.config={threshold:.7,enableAliases:!0,customAliases:{},...e}}normalizePaymentMethod(e){return e?e.replace(/^CATEGORY_TREE:AIRTM_/i,"").replace(/^E_TRANSFER_/i,"").replace(/^AIRTM_/i,"").replace(/[_-]/g," ").toLowerCase().trim().replace(/\s+/g," "):""}levenshteinDistance(e,o){const r=Array(o.length+1).fill(null).map(()=>Array(e.length+1).fill(null));for(let n=0;n<=e.length;n++)r[0][n]=n;for(let n=0;n<=o.length;n++)r[n][0]=n;for(let n=1;n<=o.length;n++)for(let s=1;s<=e.length;s++){const a=e[s-1]===o[n-1]?0:1;r[n][s]=Math.min(r[n][s-1]+1,r[n-1][s]+1,r[n-1][s-1]+a)}return r[o.length][e.length]}calculateSimilarity(e,o){if(e===o)return 1;if(!e||!o)return 0;const r=Math.max(e.length,o.length);return 1-this.levenshteinDistance(e,o)/r}wordBasedMatch(e,o){const r=e.split(" ").filter(a=>a.length>0),n=o.split(" ").filter(a=>a.length>0);if(n.length===0)return 0;let s=0;for(const a of n)r.some(c=>c.includes(a)||a.includes(c)||this.calculateSimilarity(c,a)>.8)&&s++;return s/n.length}getAliases(e){const o=this.normalizePaymentMethod(e),r=[o];if(this.config.enableAliases){for(const[,n]of Object.entries(this.config.customAliases))if(n.some(s=>this.normalizePaymentMethod(s)===o)){r.push(...n.map(s=>this.normalizePaymentMethod(s)));break}for(const[,n]of Object.entries(this.defaultAliases))if(n.some(s=>this.normalizePaymentMethod(s)===o)){r.push(...n.map(s=>this.normalizePaymentMethod(s)));break}}return[...new Set(r)]}matchPaymentMethod(e,o){const r=this.normalizePaymentMethod(e),n=this.getAliases(o);let s=0,a="";for(const c of n){if(r===c)return{isMatch:!0,score:1,matchedAlias:c};if(r.includes(c)||c.includes(r)){const l=Math.max(c.length/r.length,r.length/c.length)*.9;l>s&&(s=l,a=c)}const d=this.wordBasedMatch(r,c)*.85;d>s&&(s=d,a=c);const u=this.calculateSimilarity(r,c)*.8;u>s&&(s=u,a=c)}return{isMatch:s>=this.config.threshold,score:s,matchedAlias:s>=this.config.threshold?a:void 0}}matchAnyPaymentMethod(e,o){let r={isMatch:!1,score:0};for(const n of o){const s=this.matchPaymentMethod(e,n);if(s.score>r.score&&(r=s),s.score===1)break}return r}updateConfig(e){this.config={...this.config,...e}}getConfig(){return{...this.config}}}let i=A;const L=new K,f={totalOffers:0,newOffers:0,acceptedOffers:0,rejectedOffers:0,averageRate:0,lastUpdate:new Date().toISOString()};let g=new Map,F=Date.now();const H=2*60*1e3,J=60*60*1e3;async function q(){try{const t=await chrome.storage.local.get("notified_offers");t.notified_offers?(g=new Map(Object.entries(t.notified_offers)),console.log(`Loaded ${g.size} notified offers from storage`)):(g=new Map,console.log("No previously notified offers found in storage"))}catch(t){console.error("Error loading notified offers:",t),g=new Map}}async function z(){try{const t=Object.fromEntries(g);await chrome.storage.local.set({notified_offers:t})}catch(t){console.error("Error saving notified offers:",t)}}let U=!1;async function S(){var t,e;if(U){console.log("Background service worker already initialized, skipping...");return}console.log("Airtm Monitor Pro: Background service worker initializing...");try{if(typeof chrome>"u"||!chrome.runtime)throw new Error("Chrome runtime APIs not available");await q(),await we(),V(),Ae(),Oe(),await Te(),G(),U=!0,console.log("Background service worker initialized successfully")}catch(o){console.error("Error initializing background service worker:",o);try{(t=chrome.action)==null||t.setBadgeText({text:"!"}),(e=chrome.action)==null||e.setBadgeBackgroundColor({color:"#ff0000"})}catch(r){console.error("Could not set error badge:",r)}}}function V(){chrome.runtime.onMessage.hasListeners()&&chrome.runtime.onMessage.removeListener,chrome.runtime.onMessage.addListener((t,e,o)=>{var r,n,s;if(console.log("Background received message:",t.type,"from:",((r=e.tab)==null?void 0:r.url)||"popup/options"),!t||typeof t.type!="string"){console.error("Invalid message received:",t),o({success:!1,error:"Invalid message format"});return}try{switch(t.type){case"OFFERS_UPDATE":return te(t.data).then(()=>o({success:!0})).catch(a=>{console.error("Error handling offers update:",a),o({success:!1,error:a.message})}),!0;case"SETTINGS_UPDATE":return Ee(t.data).then(()=>o({success:!0})).catch(a=>{console.error("Error handling settings update:",a),o({success:!1,error:a.message})}),!0;case"ACCEPT_OFFER":return C((n=t.data)==null?void 0:n.offerId).then(a=>o({success:!0,result:a})).catch(a=>{console.error("Error handling accept offer:",a),o({success:!1,error:a.message})}),!0;case"REJECT_OFFER":return B((s=t.data)==null?void 0:s.offerId).then(a=>o({success:!0,result:a})).catch(a=>{console.error("Error handling reject offer:",a),o({success:!1,error:a.message})}),!0;case"OPERATION_DETAILS_UPDATE":return le(t.data).then(()=>o({success:!0})).catch(a=>{console.error("Error handling operation details update:",a),o({success:!1,error:a.message})}),!0;case"OPERATION_ACCEPTED":return fe(t.data).then(()=>o({success:!0})).catch(a=>{console.error("Error handling operation accepted:",a),o({success:!1,error:a.message})}),!0;case"OPERATION_NOT_AVAILABLE":return ge(t.data).then(()=>o({success:!0})).catch(a=>{console.error("Error handling operation not available:",a),o({success:!1,error:a.message})}),!0;case"OPERATION_ACCEPT_ERROR":return he(t.data).then(()=>o({success:!0})).catch(a=>{console.error("Error handling operation accept error:",a),o({success:!1,error:a.message})}),!0;case"SEND_TELEGRAM_OPERATION_UPDATE":return ye(t.data).then(()=>o({success:!0})).catch(a=>{console.error("Error sending Telegram operation update:",a),o({success:!1,error:a.message})}),!0;case"OPERATION_DECLINED":return me(t.data).then(()=>o({success:!0})).catch(a=>{console.error("Error handling operation declined:",a),o({success:!1,error:a.message})}),!0;case"OPERATION_DECLINE_ERROR":return pe(t.data).then(()=>o({success:!0})).catch(a=>{console.error("Error handling operation decline error:",a),o({success:!1,error:a.message})}),!0;case"GET_POPUP_DATA":return Z().then(a=>o({success:!0,data:a})).catch(a=>{console.error("Error handling get popup data:",a),o({success:!1,error:a.message})}),!0;case"PING":console.log("Ping received, responding with alive status"),o({success:!0,message:"Background script is alive",timestamp:new Date().toISOString()});break;default:console.warn("Unknown message type:",t.type),o({success:!1,error:"Unknown message type: "+t.type})}}catch(a){console.error("Error in message listener:",a),o({success:!1,error:"Internal error: "+(a instanceof Error?a.message:"Unknown error")})}}),chrome.runtime.onConnect.addListener(t=>{console.log("Port connected:",t.name),t.onDisconnect.addListener(()=>{console.log("Port disconnected:",t.name),chrome.runtime.lastError&&console.error("Port disconnect error:",chrome.runtime.lastError)})}),chrome.commands&&chrome.commands.onCommand&&chrome.commands.onCommand.addListener(t=>{console.log("Chrome command received:",t),chrome.tabs.query({url:"*://app.airtm.com/peer-transfers/available*"},e=>{e.length>0&&e[0].id?chrome.tabs.sendMessage(e[0].id,{type:"EXECUTE_COMMAND",command:t}).catch(o=>{console.log("Could not send command to content script:",o)}):console.log("No active Airtm tab found for command:",t)})})}async function Z(){try{const e=(await chrome.storage.local.get("current_offers")).current_offers||[];return console.log("Sending popup data:",{offersCount:e.length,settings:i,stats:f}),{offers:e,settings:i,stats:f}}catch(t){throw console.error("Error getting popup data:",t),t}}function Q(){const t=Date.now();if(t-F<H)return;const e=t-J;let o=0;for(const[r,n]of g.entries())n<e&&(g.delete(r),o++);F=t,o>0&&(console.log(`🧹 Cleaned up ${o} old notified offers (older than 1 hour)`),z())}function X(t){return g.has(t)}function ee(t){g.set(t,Date.now()),z()}async function te(t){try{const{offers:e,timestamp:o}=t;if(!Array.isArray(e))throw new Error("Invalid offers data");if(console.log("Processing "+e.length+" offers"),await chrome.storage.local.set({current_offers:e}),Q(),!i.monitoring){console.log("Monitoring disabled, skipping offer processing"),$(e),f.lastUpdate=o||new Date().toISOString();try{chrome.runtime.sendMessage({type:"OFFERS_PROCESSED",data:{offers:e,stats:f}}).catch(s=>{console.log("Could not notify popup (popup may be closed):",s.message)})}catch(s){console.log("Error sending OFFERS_PROCESSED message:",s)}return}$(e);const r=oe(e);console.log(r.length+" offers passed filters");const n=r.filter(s=>!X(s.id));console.log(n.length+" new offers (not previously notified)"),n.length>0&&i.soundEnabled&&i.notificationsEnabled&&(console.log("Playing sound notification once for "+n.length+" new offers"),await x());for(const s of n)ee(s.id),await ne(s,!1);f.lastUpdate=o||new Date().toISOString();try{chrome.runtime.sendMessage({type:"OFFERS_PROCESSED",data:{offers:e,stats:f}}).catch(s=>{console.log("Could not notify popup (popup may be closed):",s.message)})}catch(s){console.log("Error sending OFFERS_PROCESSED message:",s)}}catch(e){console.error("Error handling offers update:",e)}}function oe(t){return t.filter(e=>{var r,n,s,a,c,d;const o=typeof e.grossAmount=="string"?parseFloat(e.grossAmount):e.grossAmount;if(o<i.minAmount||o>i.maxAmount)return!1;if(i.preferredCurrencies.length>0){const u=((r=e.currency)==null?void 0:r.symbol)||((n=e.walletCurrency)==null?void 0:n.symbol)||"";if(!i.preferredCurrencies.includes(u))return!1}if(i.paymentMethods.length>0){const u=((c=(a=(s=e.makerPaymentMethod)==null?void 0:s.version)==null?void 0:a.category)==null?void 0:c.translationTag)||"";if(i.fuzzyMatching.enabled){const l=L.matchAnyPaymentMethod(u,i.paymentMethods);if(l.isMatch)console.log(`Offer ${e.id} matched payment method "${u}" with alias "${l.matchedAlias}" (score: ${l.score.toFixed(2)})`);else return console.log(`Offer ${e.id} filtered out: payment method "${u}" doesn't match any configured methods (best score: ${l.score.toFixed(2)})`),!1}else{let l=u;if(l.startsWith("CATEGORY_TREE:AIRTM_")&&(l=l.replace("CATEGORY_TREE:AIRTM_","")),l.startsWith("E_TRANSFER_")&&(l=l.replace("E_TRANSFER_","")),!i.paymentMethods.some(E=>l.includes(E)))return console.log(`Offer ${e.id} filtered out: payment method "${l}" doesn't match any configured methods (legacy matching)`),!1}}if(i.countries.length>0){const u=((d=e.peer)==null?void 0:d.country)||"";if(!i.countries.includes(u))return!1}if(i.keywords.length>0){const u=JSON.stringify(e).toLowerCase();if(!i.keywords.some(l=>u.includes(l.toLowerCase())))return!1}if(i.blacklistKeywords.length>0){const u=JSON.stringify(e).toLowerCase();if(i.blacklistKeywords.some(l=>u.includes(l.toLowerCase())))return console.log(`Offer ${e.id} filtered out by blacklist keyword`),!1}return!0})}async function re(t){try{const e=await chrome.tabs.query({url:"*://app.airtm.com/peer-transfers/available*"});if(e.length===0){console.log("No Airtm tab found, opening new tab");const r=await chrome.tabs.create({url:"https://app.airtm.com/peer-transfers/available",active:!0});if(await new Promise(n=>setTimeout(n,3e3)),r.windowId)try{await chrome.windows.update(r.windowId,{focused:!0,state:"maximized"}),console.log("✅ New window maximized and focused")}catch(n){console.warn("⚠️ Failed to maximize new window:",n);try{await chrome.windows.update(r.windowId,{focused:!0}),console.log("✅ New window focused (without maximizing)")}catch(s){console.error("❌ Failed to focus new window:",s)}}return}const o=e[0];if(o.windowId){try{await chrome.windows.update(o.windowId,{focused:!0,state:"maximized"}),console.log("✅ Window maximized and focused for offer:",t.id)}catch(n){console.warn("⚠️ Failed to maximize window:",n);try{await chrome.windows.update(o.windowId,{focused:!0}),console.log("✅ Window focused (without maximizing) for offer:",t.id)}catch(s){console.error("❌ Failed to focus window:",s)}}let r=!1;for(let n=1;n<=3;n++)try{await chrome.tabs.update(o.id,{active:!0}),console.log(`✅ Tab activated (attempt ${n}) for offer:`,t.id),r=!0;break}catch(s){console.warn(`⚠️ Tab activation attempt ${n} failed:`,s),n<3&&await new Promise(a=>setTimeout(a,500))}r||console.error("❌ Failed to activate tab after 3 attempts"),await new Promise(n=>setTimeout(n,1e3));try{await chrome.tabs.sendMessage(o.id,{type:"FOCUS_OFFER",data:{offerId:t.id}}),console.log("✅ Focus message sent to content script for offer:",t.id)}catch(n){console.log("⚠️ Could not send focus message to content script:",n)}}else console.error("❌ No window ID found for Airtm tab")}catch(e){console.error("❌ Error maximizing window and focusing offer:",e)}}async function ne(t,e=!0){try{console.log("Processing offer "+t.id),await re(t),i.notificationsEnabled&&await ae(t,e),console.log("Checking Telegram settings:",{botToken:i.telegramBotToken?"***SET***":"NOT SET",chatId:i.telegramChatId?"***SET***":"NOT SET"}),i.telegramBotToken&&i.telegramChatId?(console.log("Sending Telegram message for offer:",t.id),await w(t)):console.log("Telegram not configured - skipping message"),i.webhookUrl?(console.log("Sending webhook message for offer:",t.id),await se(t)):console.log("Webhook not configured - skipping webhook"),i.autoAccept&&await C(t.id)}catch(o){console.error("Error processing offer "+t.id+":",o)}}async function ae(t,e=!0){var o,r,n,s;try{b.updateSettings({autoCloseDelay:3e4,playSound:i.soundEnabled&&e}),await b.showNotification(t)}catch(a){console.error("Error sending notification:",a);try{const c="New "+t.operationType+" Offer",d=t.grossAmount+" "+(((o=t.currency)==null?void 0:o.symbol)||((r=t.walletCurrency)==null?void 0:r.symbol));await chrome.notifications.create({type:"basic",iconUrl:"icons/icon48.png",title:c,message:d,contextMessage:"From: "+(((n=t.peer)==null?void 0:n.firstName)||"")+" "+(((s=t.peer)==null?void 0:s.lastName)||""),buttons:[{title:"Accept"},{title:"Reject"}]}),i.soundEnabled&&e&&await x()}catch(c){console.error("Fallback notification also failed:",c)}}}async function w(t){try{const e=typeof t=="string"?t:ce(t),o="https://api.telegram.org/bot"+i.telegramBotToken+"/sendMessage";console.log("Sending Telegram message to:",i.telegramChatId),console.log("Message content:",e);const r=await fetch(o,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({chat_id:i.telegramChatId,text:e,parse_mode:"Markdown"})}),n=await r.json();r.ok?console.log("✅ Telegram message sent successfully:",n):console.error("❌ Telegram API error:",n)}catch(e){console.error("❌ Error sending Telegram message:",e)}}async function se(t){try{const e={timestamp:new Date().toISOString(),offer:t,event:"new_offer",metadata:{extensionVersion:"1.0.0",source:"airtm-monitor-pro"}};console.log("Sending webhook to:",i.webhookUrl),console.log("Webhook payload:",e);const o=await fetch(i.webhookUrl,{method:"POST",headers:{"Content-Type":"application/json","User-Agent":"Airtm-Monitor-Pro/1.0.0"},body:JSON.stringify(e)});o.ok?console.log("✅ Webhook sent successfully:",o.status):console.error("❌ Webhook failed with status:",o.status,await o.text())}catch(e){console.error("❌ Error sending webhook:",e)}}function ie(t){return{USA:"🇺🇸",US:"🇺🇸",IND:"🇮🇳",IN:"🇮🇳",GBR:"🇬🇧",GB:"🇬🇧",UK:"🇬🇧",CAN:"🇨🇦",CA:"🇨🇦",AUS:"🇦🇺",AU:"🇦🇺",DEU:"🇩🇪",DE:"🇩🇪",GER:"🇩🇪",FRA:"🇫🇷",FR:"🇫🇷",ESP:"🇪🇸",ES:"🇪🇸",ITA:"🇮🇹",IT:"🇮🇹",BRA:"🇧🇷",BR:"🇧🇷",MEX:"🇲🇽",MX:"🇲🇽",ARG:"🇦🇷",AR:"🇦🇷",COL:"🇨🇴",CO:"🇨🇴",VEN:"🇻🇪",VE:"🇻🇪",PER:"🇵🇪",PE:"🇵🇪",CHL:"🇨🇱",CL:"🇨🇱",URY:"🇺🇾",UY:"🇺🇾",ECU:"🇪🇨",EC:"🇪🇨",BOL:"🇧🇴",BO:"🇧🇴",PRY:"🇵🇾",PY:"🇵🇾",CHN:"🇨🇳",CN:"🇨🇳",JPN:"🇯🇵",JP:"🇯🇵",KOR:"🇰🇷",KR:"🇰🇷",RUS:"🇷🇺",RU:"🇷🇺",TUR:"🇹🇷",TR:"🇹🇷",EGY:"🇪🇬",EG:"🇪🇬",ZAF:"🇿🇦",ZA:"🇿🇦",NGA:"🇳🇬",NG:"🇳🇬",KEN:"🇰🇪",KE:"🇰🇪",GHA:"🇬🇭",GH:"🇬🇭",MAR:"🇲🇦",MA:"🇲🇦",TUN:"🇹🇳",TN:"🇹🇳",DZA:"🇩🇿",DZ:"🇩🇿",IDN:"🇮🇩",ID:"🇮🇩",THA:"🇹🇭",TH:"🇹🇭",VNM:"🇻🇳",VN:"🇻🇳",PHL:"🇵🇭",PH:"🇵🇭",MYS:"🇲🇾",MY:"🇲🇾",SGP:"🇸🇬",SG:"🇸🇬",PAK:"🇵🇰",PK:"🇵🇰",BGD:"🇧🇩",BD:"🇧🇩",LKA:"🇱🇰",LK:"🇱🇰",NPL:"🇳🇵",NP:"🇳🇵",AFG:"🇦🇫",AF:"🇦🇫",IRN:"🇮🇷",IR:"🇮🇷",IRQ:"🇮🇶",IQ:"🇮🇶",SAU:"🇸🇦",SA:"🇸🇦",ARE:"🇦🇪",AE:"🇦🇪",QAT:"🇶🇦",QA:"🇶🇦",KWT:"🇰🇼",KW:"🇰🇼",BHR:"🇧🇭",BH:"🇧🇭",OMN:"🇴🇲",OM:"🇴🇲",JOR:"🇯🇴",JO:"🇯🇴",LBN:"🇱🇧",LB:"🇱🇧",SYR:"🇸🇾",SY:"🇸🇾",ISR:"🇮🇱",IL:"🇮🇱",PSE:"🇵🇸",PS:"🇵🇸"}[t.toUpperCase()]||t}function ce(t){var u,l,E,_,N,I,M,P,k,v,D,R;const e=((u=t.currency)==null?void 0:u.symbol)||((l=t.walletCurrency)==null?void 0:l.symbol)||"Unknown",o=((((E=t.peer)==null?void 0:E.firstName)||"")+" "+(((_=t.peer)==null?void 0:_.lastName)||"")).trim(),r=((I=(N=t.peer)==null?void 0:N.numbers)==null?void 0:I.score)||0,n=((P=(M=t.peer)==null?void 0:M.numbers)==null?void 0:P.completedOperations)||0,s=((k=t.peer)==null?void 0:k.country)||"Unknown",a=ie(s);let d=((R=(D=(v=t.makerPaymentMethod)==null?void 0:v.version)==null?void 0:D.category)==null?void 0:R.translationTag)||"Unknown";return d.startsWith("CATEGORY_TREE:AIRTM_")&&(d=d.replace("CATEGORY_TREE:AIRTM_","")),d.startsWith("E_TRANSFER_")&&(d=d.replace("E_TRANSFER_","")),d=d.replace(/_/g," ").toLowerCase().replace(/\b\w/g,j=>j.toUpperCase()),`${t.grossAmount} ${e} : ${d} (${a})
👤 User: ${o} ${r}⭐(${n} trades)`}let h=!1,y=null;async function x(){return y||(y=(async()=>{var t;try{if(!h)try{await chrome.offscreen.createDocument({url:"sounds/beep.html",reasons:[chrome.offscreen.Reason.AUDIO_PLAYBACK],justification:"Play notification sound for new offers"}),h=!0}catch(e){if((t=e.message)!=null&&t.includes("Only a single offscreen document may be created"))h=!0;else throw e}try{await chrome.runtime.sendMessage({type:"PLAY_SOUND"})}catch(e){throw console.log("Message to offscreen document failed:",e),h=!1,e}}catch(e){console.error("Error playing notification sound:",e),h=!1}finally{try{h&&(await chrome.offscreen.closeDocument(),h=!1)}catch{h=!1}y=null}})(),y)}async function C(t){try{const e=await chrome.tabs.query({url:"*://app.airtm.com/peer-transfers/available*"});if(e.length===0)throw new Error("Airtm tab not found");const o=await chrome.tabs.sendMessage(e[0].id,{type:"ACCEPT_OFFER",data:{offerId:t}});if(o!=null&&o.success)return f.acceptedOffers++,console.log("Offer "+t+" accepted successfully"),de(t,e[0].id),!0;throw new Error((o==null?void 0:o.error)||"Failed to accept offer")}catch(e){throw console.error("Error accepting offer "+t+":",e),e}}async function le(t){try{console.log("📋 Processing operation details update:",t);const{operation:e,timestamp:o,source:r}=t;if(!e||!e.id){console.warn("Invalid operation data received");return}await chrome.storage.local.set({[`operation_${e.id}`]:{...e,lastUpdated:o,source:r}}),i.telegramBotToken&&i.telegramChatId&&await ue(e),console.log(`✅ Operation ${e.id} details updated - Status: ${e.status}`)}catch(e){console.error("❌ Error handling operation details update:",e)}}function de(t,e){console.log(`🔍 Starting URL monitoring for accepted offer: ${t}`);let o=0;const r=30,n=setInterval(async()=>{try{o++;const a=(await chrome.tabs.get(e)).url;if(console.log(`🔍 URL check ${o}/${r}: ${a}`),a&&/https:\/\/app\.airtm\.com\/operations\/[A-Z0-9]+/.test(a)){console.log("✅ Operation URL detected:",a);const d=a.split("/operations/")[1];i.telegramBotToken&&i.telegramChatId&&await w(`🎯 Offer ${t} accepted successfully!
📋 Operation ID: ${d}
🔗 URL: ${a}
⏰ Status: Accepted`),await chrome.storage.local.set({[`accepted_operation_${t}`]:{operationId:d,url:a,timestamp:new Date().toISOString(),status:"accepted"}}),clearInterval(n);return}o>=r&&(console.log("⚠️ Operation URL not detected within timeout"),i.telegramBotToken&&i.telegramChatId&&await w(`❌ Offer ${t} acceptance failed or timed out
⏰ Status: Not Available`),await chrome.storage.local.set({[`failed_operation_${t}`]:{timestamp:new Date().toISOString(),status:"not_available",reason:"url_not_detected"}}),clearInterval(n))}catch(s){console.error("❌ Error during URL monitoring:",s),clearInterval(n)}},100)}async function ue(t){try{const e=`📋 Operation Update
🆔 ID: ${t.id}
📊 Status: ${t.status}
💰 Amount: ${t.grossAmount||"N/A"}
🔄 Type: ${t.operationType||"N/A"}
⏰ Updated: ${new Date().toLocaleString()}`;await w(e)}catch(e){console.error("❌ Error sending Telegram operation update:",e)}}async function fe(t){try{console.log("✅ Processing operation acceptance:",t),await chrome.storage.local.set({[`accepted_operation_${t.operationId}`]:{...t,processedAt:new Date().toISOString()}}),f.acceptedOffers++,console.log("✅ Operation acceptance processed successfully")}catch(e){console.error("❌ Error handling operation accepted:",e)}}async function ge(t){try{console.log("🚫 Processing operation not available:",t),await chrome.storage.local.set({[`unavailable_operation_${t.operationId||"unknown"}`]:{...t,processedAt:new Date().toISOString()}}),console.log("🚫 Operation not available processed successfully")}catch(e){console.error("❌ Error handling operation not available:",e)}}async function he(t){try{console.log("⚠️ Processing operation accept error:",t),await chrome.storage.local.set({[`error_operation_${Date.now()}`]:{...t,processedAt:new Date().toISOString()}}),console.log("⚠️ Operation accept error processed successfully")}catch(e){console.error("❌ Error handling operation accept error:",e)}}async function me(t){try{console.log("🚫 Processing operation decline:",t),await chrome.storage.local.set({[`declined_operation_${Date.now()}`]:{...t,processedAt:new Date().toISOString()}}),f.rejectedOffers++,console.log("🚫 Operation decline processed successfully")}catch(e){console.error("❌ Error handling operation declined:",e)}}async function pe(t){try{console.log("⚠️ Processing operation decline error:",t),await chrome.storage.local.set({[`decline_error_operation_${Date.now()}`]:{...t,processedAt:new Date().toISOString()}}),console.log("⚠️ Operation decline error processed successfully")}catch(e){console.error("❌ Error handling operation decline error:",e)}}async function ye(t){try{console.log("📱 Sending Telegram operation status update:",t);const{operationId:e,status:o}=t;let r;o==="accepted"?r=`✅ *Operation Accepted*

🆔 Operation ID: ${e}
📊 Status: Successfully accepted
⏰ Time: ${new Date().toLocaleString()}

🎉 Great! The operation was accepted successfully.`:o==="Not Available"?r=`🚫 *Operation Not Available*

🆔 Operation ID: ${e}
📊 Status: No longer available
⏰ Time: ${new Date().toLocaleString()}

😔 The operation was taken by someone else or expired.`:r=`📋 *Operation Update*

🆔 Operation ID: ${e}
📊 Status: ${o}
⏰ Time: ${new Date().toLocaleString()}`,await w(r),console.log("📱 Telegram operation status update sent successfully")}catch(e){console.error("❌ Error sending Telegram operation status update:",e)}}async function B(t){try{const e=await chrome.tabs.query({url:"*://app.airtm.com/peer-transfers/available*"});if(e.length===0)throw new Error("Airtm tab not found");const o=await chrome.tabs.sendMessage(e[0].id,{type:"REJECT_OFFER",data:{offerId:t}});if(o!=null&&o.success)return f.rejectedOffers++,console.log("Offer "+t+" rejected successfully"),!0;throw new Error((o==null?void 0:o.error)||"Failed to reject offer")}catch(e){throw console.error("Error rejecting offer "+t+":",e),e}}function $(t){f.totalOffers=t.length,f.newOffers=t.filter(e=>{const o=new Date(e.createdAt),r=new Date(Date.now()-5*60*1e3);return o>r}).length,f.averageRate=0}async function we(){try{const t=await chrome.storage.sync.get("settings");i={...A,...t.settings},i.monitoring=!0,T(),await chrome.storage.sync.set({settings:i}),console.log("Settings loaded and monitoring enabled:",i)}catch(t){console.error("Error loading settings:",t),i={...A,monitoring:!0},T()}}async function Ee(t){try{i={...i,...t},t.fuzzyMatching&&T(),await chrome.storage.sync.set({settings:i}),console.log("Settings updated:",i),G()}catch(e){throw console.error("Error updating settings:",e),e}}function G(){i.monitoring?(chrome.action.setBadgeText({text:"ON"}),chrome.action.setBadgeBackgroundColor({color:"#10b981"})):(chrome.action.setBadgeText({text:"OFF"}),chrome.action.setBadgeBackgroundColor({color:"#6b7280"}))}function T(){try{const t=i.fuzzyMatching||{enabled:!1,threshold:.8,enableAliases:!0,customAliases:{}};L.updateConfig({threshold:t.threshold,enableAliases:t.enableAliases,customAliases:t.customAliases}),console.log("Fuzzy matcher configuration updated:",t)}catch(t){console.error("Error updating fuzzy matcher configuration:",t)}}function Ae(){chrome.alarms.onAlarm.addListener(t=>{switch(console.log("Alarm triggered:",t.name),t.name){case"monitoring-check":break;case"cleanup-storage":be();break}}),chrome.alarms.create("cleanup-storage",{periodInMinutes:60})}function Oe(){chrome.notifications.onButtonClicked.addListener(async(t,e)=>{try{const o=t.match(/offer_(.+)_\d+/),r=o?o[1]:t;e===0?await C(r):e===1&&await B(r),chrome.notifications.clear(t)}catch(o){console.error("Error handling notification button click:",o)}}),chrome.runtime.onMessage.addListener((t,e,o)=>{(t.type==="ACCEPT_OFFER"||t.type==="REJECT_OFFER"||t.type==="IGNORE_OFFER")&&b.handleMessage(t,e)})}async function be(){try{console.log("Cleaning up storage...");const e=(await chrome.storage.local.get(["airtm_offers_timestamp"])).airtm_offers_timestamp;if(e){const o=Date.now()-new Date(e).getTime(),r=24*60*60*1e3;o>r&&(await chrome.storage.local.remove(["airtm_offers","airtm_offers_timestamp"]),console.log("Old offers cleaned up"))}}catch(t){console.error("Error cleaning up storage:",t)}}async function Te(){try{console.log("🔍 Checking for existing Airtm tabs...");const t=await chrome.tabs.query({url:"*://app.airtm.com/peer-transfers/available*"});if(t.length===0){console.log("📭 No existing Airtm tabs found");return}console.log(`🎯 Found ${t.length} existing Airtm tab(s)`),console.log("ℹ️ Content scripts will be automatically injected by Chrome based on manifest.json");for(const e of t)e.id&&e.url&&console.log(`📋 Found Airtm tab ${e.id}: ${e.url}`)}catch(t){console.error("❌ Error checking existing tabs:",t)}}function Se(){const t=()=>{var e;try{(e=chrome==null?void 0:chrome.runtime)!=null&&e.getPlatformInfo&&chrome.runtime.getPlatformInfo(()=>{chrome.runtime.lastError&&console.warn("Keep alive error:",chrome.runtime.lastError)})}catch(o){console.warn("Keep alive failed:",o)}};t(),setInterval(t,2e4)}S().then(()=>{Se()});chrome.runtime.onStartup.addListener(()=>{console.log("Service worker starting up..."),S()});chrome.runtime.onInstalled.addListener(t=>{console.log("Extension installed/updated:",t.reason),S()});
