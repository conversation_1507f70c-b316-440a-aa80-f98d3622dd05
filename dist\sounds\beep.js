// Generate beep sound using Web Audio API
function playBeep() {
    const audio = document.getElementById('notificationSound');
    if (audio) {
        try {
            // Reset audio to beginning
            audio.currentTime = 0;
            
            // Check if audio is ready to play
            if (audio.readyState >= 2) { // HAVE_CURRENT_DATA or higher
                // Play the audio file
                const playPromise = audio.play();
                if (playPromise !== undefined) {
                    playPromise.catch(error => {
                        console.error('Error playing notification sound:', error);
                        // Try to enable audio context if needed
                        if (error.name === 'NotAllowedError') {
                            console.log('Audio playback requires user interaction');
                        }
                    });
                }
            } else {
                // Wait for audio to load
                audio.addEventListener('canplay', () => {
                    const playPromise = audio.play();
                    if (playPromise !== undefined) {
                        playPromise.catch(error => {
                            console.error('Error playing notification sound after load:', error);
                        });
                    }
                }, { once: true });
            }
        } catch (error) {
            console.error('Error in playBeep function:', error);
        }
    }
}

// Initialize audio element when page loads
window.addEventListener('DOMContentLoaded', () => {
    const audio = document.getElementById('notificationSound');
    if (audio) {
        try {
            audio.volume = 0.5; // Set volume to 50%
            
            // Add error handling for audio loading
            audio.addEventListener('error', (e) => {
                console.error('Audio loading error:', e);
                console.error('Audio error details:', {
                    error: audio.error,
                    networkState: audio.networkState,
                    readyState: audio.readyState
                });
            });
            
            audio.addEventListener('loadstart', () => {
                console.log('Audio loading started');
            });
            
            audio.addEventListener('canplay', () => {
                console.log('Audio can start playing');
            });
            
            // Load the audio
            audio.load();
        } catch (error) {
            console.error('Error initializing audio:', error);
        }
    } else {
        console.error('Audio element not found');
    }
});

// Listen for messages to play sound
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    if (message.type === 'PLAY_SOUND') {
        playBeep();
    }
});