import{r as h,j as e,c as S}from"./globals-b62df2d6.js";const o=({d:r,className:a="w-4 h-4"})=>e.jsx("svg",{className:a,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:r})}),c={refresh:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15",settings:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z M15 12a3 3 0 11-6 0 3 3 0 016 0z",close:"M6 18L18 6M6 6l12 12",alert:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z",play:"M14.828 14.828a4 4 0 01-5.656 0M9 10h1.586a1 1 0 01.707.293l2.414 2.414a1 1 0 00.707.293H15",pause:"M10 9v6m4-6v6",check:"M5 13l4 4L19 7",search:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"};function F(){var p,u,j,g,N,v,y,w,b,E,O,A,M;const[r,a]=h.useState({offers:[],settings:null,stats:null,selectedOffer:null,isLoading:!0,error:null,isConnected:!1}),m=h.useCallback(async()=>{var t;a(s=>({...s,isLoading:!0,error:null}));try{if(!((t=chrome==null?void 0:chrome.runtime)!=null&&t.id))throw new Error("Extension context not available");const s=await chrome.runtime.sendMessage({type:"GET_POPUP_DATA"});if(s!=null&&s.success&&s.data)a(n=>({...n,offers:s.data.offers||[],settings:s.data.settings||null,stats:s.data.stats||{totalOffers:0,newOffers:0,averageRate:0},isConnected:!0,isLoading:!1,error:null}));else throw new Error((s==null?void 0:s.error)||"Invalid response from background script")}catch(s){console.error("Popup data loading error:",s),a(n=>({...n,isLoading:!1,isConnected:!1,error:s instanceof Error?s.message:"Failed to load extension data"}))}},[]),C=h.useCallback(()=>{var s,n;const t=i=>{i.type==="OFFERS_UPDATED"?a(d=>({...d,offers:i.offers||[]})):i.type==="SETTINGS_UPDATED"?a(d=>({...d,settings:i.settings})):i.type==="STATS_UPDATED"&&a(d=>({...d,stats:i.stats}))};return(n=(s=chrome.runtime)==null?void 0:s.onMessage)==null||n.addListener(t),()=>{var i,d;return(d=(i=chrome.runtime)==null?void 0:i.onMessage)==null?void 0:d.removeListener(t)}},[]);h.useEffect(()=>(m(),C()),[]);const l=h.useMemo(()=>({refresh:()=>m(),openSettings:()=>{try{chrome.runtime.openOptionsPage()}catch(t){console.error("Failed to open settings:",t),a(s=>({...s,error:"Failed to open settings page"}))}},selectOffer:t=>{a(s=>({...s,selectedOffer:t}))},updateSettings:async t=>{try{const s=await chrome.runtime.sendMessage({type:"UPDATE_SETTINGS",settings:t});if(s!=null&&s.success)a(n=>({...n,settings:t}));else throw new Error((s==null?void 0:s.error)||"Failed to update settings")}catch(s){console.error("Settings update error:",s),a(n=>({...n,error:s instanceof Error?s.message:"Failed to update settings"}))}},acceptOffer:async t=>{try{const s=await chrome.runtime.sendMessage({type:"ACCEPT_OFFER",offerId:t.hash});if(!(s!=null&&s.success))throw new Error((s==null?void 0:s.error)||"Failed to accept offer")}catch(s){console.error("Accept offer error:",s),a(n=>({...n,error:s instanceof Error?s.message:"Failed to accept offer"}))}},rejectOffer:async t=>{try{const s=await chrome.runtime.sendMessage({type:"REJECT_OFFER",offerId:t.hash});if(!(s!=null&&s.success))throw new Error((s==null?void 0:s.error)||"Failed to reject offer")}catch(s){console.error("Reject offer error:",s),a(n=>({...n,error:s instanceof Error?s.message:"Failed to reject offer"}))}},dismissError:()=>{a(t=>({...t,error:null}))}}),[m]);return r.isLoading?e.jsx("div",{className:"modern-popup",children:e.jsxs("div",{className:"loading-state",children:[e.jsxs("div",{className:"loading-logo",children:[e.jsx("div",{className:"logo-circle",children:"A"}),e.jsx("div",{className:"loading-pulse"})]}),e.jsxs("div",{className:"loading-content",children:[e.jsx("h2",{className:"loading-title",children:"Airtm Monitor Pro"}),e.jsx("p",{className:"loading-text",children:"Connecting to extension..."}),e.jsx("div",{className:"loading-progress",children:e.jsx("div",{className:"progress-bar"})})]})]})}):e.jsxs("div",{className:"modern-popup",children:[e.jsxs("header",{className:"popup-header",children:[e.jsxs("div",{className:"header-brand",children:[e.jsxs("div",{className:"brand-logo",children:[e.jsx("div",{className:"logo-circle",children:"A"}),e.jsx("div",{className:`status-dot ${(p=r.settings)!=null&&p.monitoring?"active":"inactive"}`})]}),e.jsxs("div",{className:"brand-text",children:[e.jsx("h1",{className:"brand-title",children:"Airtm Monitor Pro"}),e.jsxs("p",{className:"brand-subtitle",children:[r.isConnected?"Connected":"Disconnected"," •",r.offers.length," offers"]})]})]}),e.jsxs("div",{className:"header-actions",children:[e.jsx("button",{onClick:l.refresh,className:"action-button secondary",title:"Refresh data",children:e.jsx(o,{d:c.refresh})}),e.jsx("button",{onClick:l.openSettings,className:"action-button primary",title:"Open settings",children:e.jsx(o,{d:c.settings})})]})]}),r.error&&e.jsxs("div",{className:"error-banner",children:[e.jsxs("div",{className:"error-content",children:[e.jsx(o,{d:c.alert,className:"error-icon"}),e.jsx("span",{className:"error-message",children:r.error})]}),e.jsx("button",{onClick:l.dismissError,className:"error-dismiss",title:"Dismiss error",children:e.jsx(o,{d:c.close})})]}),e.jsx("section",{className:"stats-section",children:e.jsxs("div",{className:"stats-grid",children:[e.jsxs("div",{className:"stat-card",children:[e.jsx("div",{className:"stat-value",children:((u=r.stats)==null?void 0:u.totalOffers)||0}),e.jsx("div",{className:"stat-label",children:"Total Offers"})]}),e.jsxs("div",{className:"stat-card",children:[e.jsx("div",{className:"stat-value",children:((j=r.stats)==null?void 0:j.newOffers)||0}),e.jsx("div",{className:"stat-label",children:"New Today"})]}),e.jsxs("div",{className:"stat-card",children:[e.jsx("div",{className:"stat-value",children:((g=r.stats)==null?void 0:g.acceptedOffers)||0}),e.jsx("div",{className:"stat-label",children:"Accepted"})]}),e.jsxs("div",{className:"stat-card",children:[e.jsx("div",{className:"stat-value",children:((N=r.stats)==null?void 0:N.rejectedOffers)||0}),e.jsx("div",{className:"stat-label",children:"Rejected"})]})]})}),e.jsxs("main",{className:"main-content",children:[e.jsxs("div",{className:"content-header",children:[e.jsx("h3",{className:"content-title",children:"Available Offers"}),e.jsx("div",{className:"content-meta",children:e.jsxs("div",{className:"live-status",children:[e.jsx("div",{className:"pulse-indicator"}),e.jsx("span",{children:"Live"})]})})]}),e.jsx("div",{className:"offers-container",children:r.offers.length===0?e.jsxs("div",{className:"empty-state",children:[e.jsx(o,{d:c.search,className:"empty-icon"}),e.jsx("h4",{className:"empty-title",children:"No offers available"}),e.jsx("p",{className:"empty-text",children:(v=r.settings)!=null&&v.monitoring?"Monitoring is active. New offers will appear here.":"Start monitoring to see offers."})]}):e.jsx("div",{className:"offers-list",children:r.offers.map(t=>{var s,n,i;return e.jsxs("div",{className:`offer-item ${((s=r.selectedOffer)==null?void 0:s.hash)===t.hash?"selected":""}`,onClick:()=>l.selectOffer(t),children:[e.jsxs("div",{className:"offer-header",children:[e.jsx("div",{className:"offer-currency",children:((n=t.currency)==null?void 0:n.symbol)||((i=t.walletCurrency)==null?void 0:i.symbol)||"N/A"}),e.jsx("div",{className:"offer-type",children:t.operationType})]}),e.jsxs("div",{className:"offer-details",children:[e.jsxs("div",{className:"offer-amount",children:["$",parseFloat(t.grossAmount||"0").toFixed(2)]}),e.jsxs("div",{className:"offer-rate",children:["Net: $",parseFloat(t.netAmount||"0").toFixed(2)]})]}),e.jsx("div",{className:"offer-peer",children:t.peer?`${t.peer.firstName} ${t.peer.lastName}`:"Unknown"})]},t.hash)})})})]}),e.jsxs("footer",{className:"control-panel",children:[r.selectedOffer&&e.jsxs("div",{className:"selected-offer",children:[e.jsxs("div",{className:"selected-info",children:[e.jsx("span",{className:"selected-label",children:"Selected:"}),e.jsxs("span",{className:"selected-details",children:[((y=r.selectedOffer.currency)==null?void 0:y.symbol)||((w=r.selectedOffer.walletCurrency)==null?void 0:w.symbol)||"N/A"," • $",parseFloat(r.selectedOffer.grossAmount||"0").toFixed(2)]})]}),e.jsxs("div",{className:"selected-actions",children:[e.jsxs("button",{onClick:()=>l.acceptOffer(r.selectedOffer),className:"action-button success",title:"Accept offer",children:[e.jsx(o,{d:c.check}),"Accept"]}),e.jsxs("button",{onClick:()=>l.rejectOffer(r.selectedOffer),className:"action-button danger",title:"Reject offer",children:[e.jsx(o,{d:c.close}),"Reject"]})]})]}),e.jsxs("div",{className:"main-controls",children:[e.jsxs("button",{onClick:()=>{var t;return l.updateSettings({...r.settings,monitoring:!((t=r.settings)!=null&&t.monitoring)})},className:`control-button ${(b=r.settings)!=null&&b.monitoring?"active":"inactive"}`,title:(E=r.settings)!=null&&E.monitoring?"Stop monitoring":"Start monitoring",children:[e.jsx(o,{d:(O=r.settings)!=null&&O.monitoring?c.pause:c.play}),e.jsx("span",{children:(A=r.settings)!=null&&A.monitoring?"Stop":"Start"})]}),e.jsxs("button",{onClick:()=>{var t;return l.updateSettings({...r.settings,autoAccept:!((t=r.settings)!=null&&t.autoAccept)})},className:`control-button ${(M=r.settings)!=null&&M.autoAccept?"active":"inactive"}`,title:"Toggle auto-accept",children:[e.jsx(o,{d:c.check}),e.jsx("span",{children:"Auto Accept"})]})]})]})]})}function f(){const r=document.documentElement,a=document.body,m=`
    width: 600px !important;
    height: 850px !important;
    min-width: 600px !important;
    min-height: 850px !important;
    max-width: 600px !important;
    max-height: 850px !important;
    margin: 0 !important;
    padding: 0 !important;
    overflow: hidden !important;
  `;r.style.cssText=m,a.style.cssText=m,a.className="popup",console.log("Popup dimensions enforced:",{htmlSize:`${r.offsetWidth}x${r.offsetHeight}`,bodySize:`${a.offsetWidth}x${a.offsetHeight}`,windowSize:`${window.innerWidth}x${window.innerHeight}`})}f();document.addEventListener("DOMContentLoaded",f);window.addEventListener("load",f);const x=document.getElementById("root");if(!x)throw new Error("Root element not found");x.style.cssText=`
  width: 600px !important;
  height: 850px !important;
  min-width: 600px !important;
  min-height: 850px !important;
  max-width: 600px !important;
  max-height: 850px !important;
  display: flex !important;
  flex-direction: column !important;
`;const T=S(x);T.render(e.jsx(F,{}));
