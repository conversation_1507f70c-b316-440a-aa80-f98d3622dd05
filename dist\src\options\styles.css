/**
 * Airtm Monitor Pro - Options Page Styles
 * Modern, accessible, and CSP-compliant design
 */

/* ===== CSS CUSTOM PROPERTIES ===== */
:root {
  /* Colors */
  --primary-50: #f0f9ff;
  --primary-100: #e0f2fe;
  --primary-500: #0ea5e9;
  --primary-600: #0284c7;
  --primary-700: #0369a1;
  --primary-900: #0c4a6e;
  
  --secondary-500: #8b5cf6;
  --secondary-600: #7c3aed;
  
  --success-500: #10b981;
  --success-600: #059669;
  
  --warning-500: #f59e0b;
  --warning-600: #d97706;
  
  --error-500: #ef4444;
  --error-600: #dc2626;
  
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  
  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-12: 3rem;
  --space-16: 4rem;
  
  /* Typography */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Border radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  
  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

/* ===== RESET & BASE STYLES ===== */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  height: 100%;
  scroll-behavior: smooth;
}

body {
  height: 100%;
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--gray-800);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow-x: hidden;
}

/* ===== LOADING SCREEN ===== */
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  transition: opacity var(--transition-slow), visibility var(--transition-slow);
}

.loading-screen.hidden {
  opacity: 0;
  visibility: hidden;
}

.loading-content {
  text-align: center;
  color: white;
}

.loading-logo {
  position: relative;
  width: 80px;
  height: 80px;
  margin: 0 auto var(--space-6);
}

.loading-spinner {
  width: 100%;
  height: 100%;
  color: rgba(255, 255, 255, 0.3);
}

.loading-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: var(--font-size-2xl);
}

.loading-content h2 {
  font-size: var(--font-size-2xl);
  font-weight: 600;
  margin-bottom: var(--space-2);
}

.loading-content p {
  font-size: var(--font-size-base);
  opacity: 0.8;
}

/* ===== MAIN APPLICATION ===== */
.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  transition: opacity var(--transition-slow);
}

.app.hidden {
  opacity: 0;
  visibility: hidden;
}

/* ===== HEADER ===== */
.app-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--gray-200);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: var(--space-4) var(--space-6);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-brand {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.brand-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--primary-500), var(--secondary-500));
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
  color: white;
  box-shadow: var(--shadow-md);
}

.brand-text h1 {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--space-1);
}

.brand-text p {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

.header-status {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-4);
  background: var(--gray-100);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: 500;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--gray-400);
  transition: background-color var(--transition-fast);
}

.status-indicator.connected .status-dot {
  background: var(--success-500);
}

.status-indicator.disconnected .status-dot {
  background: var(--error-500);
}

/* ===== MAIN CONTENT ===== */
.app-main {
  flex: 1;
  background: var(--gray-50);
}

.main-container {
  max-width: 1400px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 280px 1fr;
  min-height: calc(100vh - 80px);
}

/* ===== NAVIGATION ===== */
.app-nav {
  background: white;
  border-right: 1px solid var(--gray-200);
  padding: var(--space-6);
  overflow-y: auto;
}

.nav-section {
  margin-bottom: var(--space-8);
}

.nav-section h3 {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--gray-500);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: var(--space-4);
}

.nav-list {
  list-style: none;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-md);
  color: var(--gray-700);
  text-decoration: none;
  font-weight: 500;
  transition: all var(--transition-fast);
  margin-bottom: var(--space-1);
}

.nav-link:hover {
  background: var(--gray-100);
  color: var(--gray-900);
}

.nav-link.active {
  background: linear-gradient(135deg, var(--primary-500), var(--secondary-500));
  color: white;
  box-shadow: var(--shadow-md);
}

.nav-icon {
  font-size: var(--font-size-lg);
}

/* ===== CONTENT AREA ===== */
.app-content {
  padding: var(--space-6);
  overflow-y: auto;
}

.content-section {
  display: none;
}

.content-section.active {
  display: block;
}

.section-header {
  margin-bottom: var(--space-8);
}

.section-header h2 {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--space-2);
}

.section-header p {
  font-size: var(--font-size-lg);
  color: var(--gray-600);
}

/* ===== SETTINGS COMPONENTS ===== */
.settings-grid {
  display: grid;
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

.setting-card {
  background: white;
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  transition: all var(--transition-fast);
}

.setting-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.setting-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-3);
}

.setting-header h3 {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--gray-900);
}

.setting-card p {
  color: var(--gray-600);
  margin-bottom: var(--space-4);
}

/* ===== FORM CONTROLS ===== */
.toggle-input {
  display: none;
}

.toggle-label {
  position: relative;
  display: inline-block;
  width: 52px;
  height: 28px;
  cursor: pointer;
}

.toggle-slider {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gray-300);
  border-radius: 28px;
  transition: background-color var(--transition-fast);
}

.toggle-slider::before {
  content: '';
  position: absolute;
  height: 20px;
  width: 20px;
  left: 4px;
  bottom: 4px;
  background: white;
  border-radius: 50%;
  transition: transform var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.toggle-input:checked + .toggle-label .toggle-slider {
  background: linear-gradient(135deg, var(--primary-500), var(--secondary-500));
}

.toggle-input:checked + .toggle-label .toggle-slider::before {
  transform: translateX(24px);
}

.setting-status {
  margin-top: var(--space-3);
  padding: var(--space-2) var(--space-3);
  background: var(--gray-100);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--gray-700);
}

.setting-input-group {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.range-input {
  flex: 1;
  height: 6px;
  background: var(--gray-200);
  border-radius: 3px;
  outline: none;
  appearance: none;
}

.range-input::-webkit-slider-thumb {
  appearance: none;
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg, var(--primary-500), var(--secondary-500));
  border-radius: 50%;
  cursor: pointer;
  box-shadow: var(--shadow-md);
}

.range-input::-moz-range-thumb {
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg, var(--primary-500), var(--secondary-500));
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: var(--shadow-md);
}

.input-value {
  font-weight: 600;
  color: var(--primary-600);
  min-width: 40px;
  text-align: center;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  cursor: pointer;
  font-weight: 500;
}

.checkbox-label input[type="checkbox"] {
  display: none;
}

.checkbox-custom {
  width: 20px;
  height: 20px;
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-sm);
  position: relative;
  transition: all var(--transition-fast);
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom {
  background: linear-gradient(135deg, var(--primary-500), var(--secondary-500));
  border-color: var(--primary-500);
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: var(--font-size-sm);
  font-weight: 700;
}

/* ===== SAVE SECTION ===== */
.save-section {
  position: sticky;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-top: 1px solid var(--gray-200);
  padding: var(--space-6);
  margin: var(--space-6) calc(-1 * var(--space-6)) calc(-1 * var(--space-6));
}

.save-button {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-4) var(--space-6);
  background: linear-gradient(135deg, var(--success-500), var(--success-600));
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-md);
}

.save-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.save-button:active {
  transform: translateY(0);
}

.save-button.saving {
  background: var(--gray-400);
  cursor: not-allowed;
}

.button-icon {
  font-size: var(--font-size-lg);
}

.save-status {
  margin-top: var(--space-3);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: 500;
  transition: all var(--transition-fast);
}

.save-status.hidden {
  display: none;
}

.save-status.success {
  background: var(--success-50);
  color: var(--success-700);
  border: 1px solid var(--success-200);
}

.save-status.error {
  background: var(--error-50);
  color: var(--error-700);
  border: 1px solid var(--error-200);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
  .main-container {
    grid-template-columns: 240px 1fr;
  }
  
  .header-content {
    padding: var(--space-4);
  }
  
  .app-content {
    padding: var(--space-4);
  }
}

@media (max-width: 768px) {
  .main-container {
    grid-template-columns: 1fr;
  }
  
  .app-nav {
    display: none;
  }
  
  .header-content {
    flex-direction: column;
    gap: var(--space-4);
    text-align: center;
  }
  
  .brand-text h1 {
    font-size: var(--font-size-xl);
  }
  
  .section-header h2 {
    font-size: var(--font-size-2xl);
  }
}

/* ===== ADDITIONAL FORM CONTROLS ===== */
.amount-filters {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-4);
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.filter-group label {
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--gray-700);
}

.amount-input,
.text-input {
  padding: var(--space-3);
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  transition: border-color var(--transition-fast);
}

.amount-input:focus,
.text-input:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

.telegram-config {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.input-group label {
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--gray-700);
}

.test-button {
  align-self: flex-start;
  padding: var(--space-2) var(--space-4);
  background: var(--primary-500);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.test-button:hover {
  background: var(--primary-600);
  transform: translateY(-1px);
}

/* ===== HELP & ABOUT SECTIONS ===== */
.help-content,
.about-content {
  display: grid;
  gap: var(--space-6);
}

.help-card,
.about-card {
  background: white;
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
}

.help-card h3,
.about-card h3 {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--space-4);
}

.help-card ol,
.help-card ul,
.about-card ul {
  margin-left: var(--space-4);
  color: var(--gray-700);
}

.help-card li,
.about-card li {
  margin-bottom: var(--space-2);
  line-height: 1.6;
}

.about-card p {
  color: var(--gray-700);
  margin-bottom: var(--space-2);
}

.about-card strong {
  color: var(--gray-900);
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus styles */
.nav-link:focus,
.toggle-label:focus,
.save-button:focus,
.checkbox-label:focus,
.amount-input:focus,
.text-input:focus,
.test-button:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --gray-100: #ffffff;
    --gray-900: #000000;
  }
}

/* ===== RESPONSIVE IMPROVEMENTS ===== */
@media (max-width: 640px) {
  .amount-filters {
    grid-template-columns: 1fr;
  }

  .telegram-config {
    gap: var(--space-3);
  }

  .help-content,
  .about-content {
    gap: var(--space-4);
  }
}
