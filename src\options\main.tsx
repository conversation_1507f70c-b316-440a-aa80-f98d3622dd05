/**
 * Options Page Entry Point
 */

import { createRoot } from 'react-dom/client'
import { Options } from './Options'
import '../styles/globals.css'

// Enforce full window dimensions for options page
function enforceOptionsPageDimensions() {
  const html = document.documentElement
  const body = document.body

  // Set full window dimensions
  const style = `
    width: 100vw !important;
    height: 100vh !important;
    min-width: 100vw !important;
    min-height: 100vh !important;
    max-width: 100vw !important;
    max-height: none !important;
    margin: 0 !important;
    padding: 0 !important;
    overflow-x: hidden !important;
    overflow-y: auto !important;
  `

  html.style.cssText = style
  body.style.cssText = style

  // Ensure classes are set
  html.className = 'options-page'
  body.className = 'options'

  console.log('Options page dimensions enforced:', {
    htmlSize: `${html.offsetWidth}x${html.offsetHeight}`,
    bodySize: `${body.offsetWidth}x${body.offsetHeight}`,
    windowSize: `${window.innerWidth}x${window.innerHeight}`
  })
}

// Enforce dimensions immediately and on load
enforceOptionsPageDimensions()
document.addEventListener('DOMContentLoaded', enforceOptionsPageDimensions)
window.addEventListener('load', enforceOptionsPageDimensions)
window.addEventListener('resize', enforceOptionsPageDimensions)

const container = document.getElementById('root')
if (!container) {
  throw new Error('Root element not found')
}

// Enforce root container dimensions for full window
container.style.cssText = `
  width: 100% !important;
  height: 100% !important;
  min-height: 100vh !important;
  display: flex !important;
  flex-direction: column !important;
`

const root = createRoot(container)
root.render(<Options />)
