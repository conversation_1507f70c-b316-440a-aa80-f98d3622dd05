var K=Object.defineProperty;var Z=(e,t,o)=>t in e?K(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o;var C=(e,t,o)=>(Z(e,typeof t!="symbol"?t+"":t,o),o);import{D as P}from"./index-357a2da0.js";const T=class T{constructor(){C(this,"activeNotifications",new Map);C(this,"settings",{autoCloseDelay:3e4,playSound:!0})}static getInstance(){return T.instance||(T.instance=new T),T.instance}updateSettings(t){this.settings={...this.settings,...t}}async showNotification(t,o){const r={...this.settings,...o};try{await this.showChromeNotification(t,r),await this.highlightOfferOnPage(t),r.playSound&&await this.playNotificationSound()}catch(n){console.error("Error showing notification:",n)}}async showChromeNotification(t,o){var s,i;if(typeof chrome>"u"||!chrome.notifications)throw new Error("Chrome notifications not available");const r=`offer_${t.id}_${Date.now()}`,n=`New ${t.operationType} Offer`,a=`${t.grossAmount} ${((s=t.currency)==null?void 0:s.symbol)||((i=t.walletCurrency)==null?void 0:i.symbol)}`;await chrome.notifications.create(r,{type:"basic",iconUrl:"icons/icon48.png",title:n,message:a,contextMessage:t.peer?`From: ${t.peer.firstName} ${t.peer.lastName}`:void 0,buttons:[{title:"Accept"},{title:"Ignore"}],requireInteraction:!0}),this.activeNotifications.set(t.id,r),setTimeout(()=>{this.closeNotification(t.id)},o.autoCloseDelay)}async highlightOfferOnPage(t){var o;try{const r=await chrome.tabs.query({active:!0,currentWindow:!0});(o=r[0])!=null&&o.id&&await chrome.tabs.sendMessage(r[0].id,{type:"HIGHLIGHT_OFFER",data:{offer:t}})}catch(r){console.log("Could not highlight offer on page:",r)}}closeNotification(t){const o=this.activeNotifications.get(t);if(o){try{typeof o=="string"&&o.startsWith("offer_")&&chrome.notifications.clear(o)}catch(r){console.error("Error closing notification:",r)}this.activeNotifications.delete(t)}}closeAllNotifications(){for(const[t]of this.activeNotifications)this.closeNotification(t)}async playNotificationSound(){var t;try{if(typeof chrome<"u"&&chrome.offscreen){try{await chrome.offscreen.createDocument({url:"sounds/beep.html",reasons:["AUDIO_PLAYBACK"],justification:"Play notification sound"})}catch(o){if(!((t=o.message)!=null&&t.includes("Only a single offscreen document")))throw o}chrome.runtime.sendMessage({type:"PLAY_SOUND"})}}catch(o){console.error("Error playing notification sound:",o)}}handleMessage(t,o){var r,n;switch(t.type){case"ACCEPT_OFFER":this.handleOfferAction("accept",(r=t.data)==null?void 0:r.offerId);break;case"IGNORE_OFFER":this.handleOfferAction("ignore",(n=t.data)==null?void 0:n.offerId);break}}async handleOfferAction(t,o){if(o)try{if(this.closeNotification(o),typeof chrome<"u"&&chrome.runtime){const r=t==="accept"?"ACCEPT_OFFER":"REJECT_OFFER";await chrome.runtime.sendMessage({type:r,data:{offerId:o}})}}catch(r){console.error(`Error handling ${t} action:`,r)}}static isSupported(){return typeof chrome<"u"&&!!chrome.notifications}getAvailableMethods(){const t=[];return typeof chrome<"u"&&chrome.notifications&&t.push("chrome"),t}};C(T,"instance");let M=T;const $=M.getInstance();class H{constructor(t={}){C(this,"config");C(this,"defaultAliases",{paypal:["paypal","pp","pay pal"],zelle:["zelle","zell","zele"],venmo:["venmo","venm","vemo"],cashapp:["cashapp","cash app","cash-app","$cashtag"],wise:["wise","transferwise","transfer wise"],revolut:["revolut","revolt","revol"],skrill:["skrill","skril","skrilla"],neteller:["neteller","neteler","netelr"],perfectmoney:["perfectmoney","perfect money","pm","perfect-money"],webmoney:["webmoney","web money","wm","web-money"],bitcoin:["bitcoin","btc","bit coin"],ethereum:["ethereum","eth","ether"],usdt:["usdt","tether","usd-t"],binance:["binance","bnb","binance pay"],bank:["bank","banking","bank transfer","wire"],credit:["credit","credit card","cc","visa","mastercard"],debit:["debit","debit card","dc"]});this.config={threshold:.7,enableAliases:!0,customAliases:{},...t}}normalizePaymentMethod(t){return t?t.replace(/^CATEGORY_TREE:AIRTM_/i,"").replace(/^E_TRANSFER_/i,"").replace(/^AIRTM_/i,"").replace(/[_-]/g," ").toLowerCase().trim().replace(/\s+/g," "):""}levenshteinDistance(t,o){const r=Array(o.length+1).fill(null).map(()=>Array(t.length+1).fill(null));for(let n=0;n<=t.length;n++)r[0][n]=n;for(let n=0;n<=o.length;n++)r[n][0]=n;for(let n=1;n<=o.length;n++)for(let a=1;a<=t.length;a++){const s=t[a-1]===o[n-1]?0:1;r[n][a]=Math.min(r[n][a-1]+1,r[n-1][a]+1,r[n-1][a-1]+s)}return r[o.length][t.length]}calculateSimilarity(t,o){if(t===o)return 1;if(!t||!o)return 0;const r=Math.max(t.length,o.length);return 1-this.levenshteinDistance(t,o)/r}wordBasedMatch(t,o){const r=t.split(" ").filter(s=>s.length>0),n=o.split(" ").filter(s=>s.length>0);if(n.length===0)return 0;let a=0;for(const s of n)r.some(i=>i.includes(s)||s.includes(i)||this.calculateSimilarity(i,s)>.8)&&a++;return a/n.length}getAliases(t){const o=this.normalizePaymentMethod(t),r=[o];if(this.config.enableAliases){for(const[,n]of Object.entries(this.config.customAliases))if(n.some(a=>this.normalizePaymentMethod(a)===o)){r.push(...n.map(a=>this.normalizePaymentMethod(a)));break}for(const[,n]of Object.entries(this.defaultAliases))if(n.some(a=>this.normalizePaymentMethod(a)===o)){r.push(...n.map(a=>this.normalizePaymentMethod(a)));break}}return[...new Set(r)]}matchPaymentMethod(t,o){const r=this.normalizePaymentMethod(t),n=this.getAliases(o);let a=0,s="";for(const i of n){if(r===i)return{isMatch:!0,score:1,matchedAlias:i};if(r.includes(i)||i.includes(r)){const l=Math.max(i.length/r.length,r.length/i.length)*.9;l>a&&(a=l,s=i)}const d=this.wordBasedMatch(r,i)*.85;d>a&&(a=d,s=i);const u=this.calculateSimilarity(r,i)*.8;u>a&&(a=u,s=i)}return{isMatch:a>=this.config.threshold,score:a,matchedAlias:a>=this.config.threshold?s:void 0}}matchAnyPaymentMethod(t,o){let r={isMatch:!1,score:0};for(const n of o){const a=this.matchPaymentMethod(t,n);if(a.score>r.score&&(r=a),a.score===1)break}return r}updateConfig(t){this.config={...this.config,...t}}getConfig(){return{...this.config}}}let c=P;const B=new H,f={totalOffers:0,newOffers:0,acceptedOffers:0,rejectedOffers:0,averageRate:0,lastUpdate:new Date().toISOString()};let w=new Map,F=Date.now();const J=2*60*1e3,V=60*60*1e3;async function q(){try{const e=await chrome.storage.local.get("notified_offers");e.notified_offers?(w=new Map(Object.entries(e.notified_offers)),console.log(`Loaded ${w.size} notified offers from storage`)):(w=new Map,console.log("No previously notified offers found in storage"))}catch(e){console.error("Error loading notified offers:",e),w=new Map}}async function z(){try{const e=Object.fromEntries(w);await chrome.storage.local.set({notified_offers:e})}catch(e){console.error("Error saving notified offers:",e)}}let L=!1;async function I(){var e,t;if(L){console.log("Background service worker already initialized, skipping...");return}console.log("Airtm Monitor Pro: Background service worker initializing...");try{if(typeof chrome>"u"||!chrome.runtime)throw new Error("Chrome runtime APIs not available");await q(),await Ee(),Q(),Te(),Oe(),await be(),j(),L=!0,console.log("Background service worker initialized successfully"),ue()}catch(o){console.error("Error initializing background service worker:",o);try{(e=chrome.action)==null||e.setBadgeText({text:"!"}),(t=chrome.action)==null||t.setBadgeBackgroundColor({color:"#ff0000"})}catch(r){console.error("Could not set error badge:",r)}}}function Q(){chrome.runtime.onMessage.hasListeners()&&chrome.runtime.onMessage.removeListener,chrome.runtime.onMessage.addListener((e,t,o)=>{var r,n,a;if(console.log("Background received message:",e.type,"from:",((r=t.tab)==null?void 0:r.url)||"popup/options"),!e||typeof e.type!="string"){console.error("Invalid message received:",e),o({success:!1,error:"Invalid message format"});return}try{switch(e.type){case"OFFERS_UPDATE":return re(e.data).then(()=>o({success:!0})).catch(s=>{console.error("Error handling offers update:",s),o({success:!1,error:s.message})}),!0;case"SETTINGS_UPDATE":return _e(e.data).then(()=>o({success:!0})).catch(s=>{console.error("Error handling settings update:",s),o({success:!1,error:s.message})}),!0;case"ACCEPT_OFFER":return k((n=e.data)==null?void 0:n.offerId).then(s=>o({success:!0,result:s})).catch(s=>{console.error("Error handling accept offer:",s),o({success:!1,error:s.message})}),!0;case"REJECT_OFFER":return G((a=e.data)==null?void 0:a.offerId).then(s=>o({success:!0,result:s})).catch(s=>{console.error("Error handling reject offer:",s),o({success:!1,error:s.message})}),!0;case"OPERATION_DETAILS_UPDATE":return de(e.data).then(()=>o({success:!0})).catch(s=>{console.error("Error handling operation details update:",s),o({success:!1,error:s.message})}),!0;case"OPERATION_ACCEPTED":return fe(e.data).then(()=>o({success:!0})).catch(s=>{console.error("Error handling operation accepted:",s),o({success:!1,error:s.message})}),!0;case"OPERATION_NOT_AVAILABLE":return he(e.data).then(()=>o({success:!0})).catch(s=>{console.error("Error handling operation not available:",s),o({success:!1,error:s.message})}),!0;case"OPERATION_ACCEPT_ERROR":return pe(e.data).then(()=>o({success:!0})).catch(s=>{console.error("Error handling operation accept error:",s),o({success:!1,error:s.message})}),!0;case"SEND_TELEGRAM_OPERATION_UPDATE":return Ae(e.data).then(()=>o({success:!0})).catch(s=>{console.error("Error sending Telegram operation update:",s),o({success:!1,error:s.message})}),!0;case"OPERATION_DECLINED":return ye(e.data).then(()=>o({success:!0})).catch(s=>{console.error("Error handling operation declined:",s),o({success:!1,error:s.message})}),!0;case"OPERATION_DECLINE_ERROR":return we(e.data).then(()=>o({success:!0})).catch(s=>{console.error("Error handling operation decline error:",s),o({success:!1,error:s.message})}),!0;case"GET_POPUP_DATA":return X().then(s=>o({success:!0,data:s})).catch(s=>{console.error("Error handling get popup data:",s),o({success:!1,error:s.message})}),!0;case"PING":console.log("Ping received, responding with alive status"),o({success:!0,message:"Background script is alive",timestamp:new Date().toISOString()});break;default:console.warn("Unknown message type:",e.type),o({success:!1,error:"Unknown message type: "+e.type})}}catch(s){console.error("Error in message listener:",s),o({success:!1,error:"Internal error: "+(s instanceof Error?s.message:"Unknown error")})}}),chrome.runtime.onConnect.addListener(e=>{console.log("Port connected:",e.name),e.onDisconnect.addListener(()=>{console.log("Port disconnected:",e.name),chrome.runtime.lastError&&console.error("Port disconnect error:",chrome.runtime.lastError)})}),chrome.commands&&chrome.commands.onCommand&&chrome.commands.onCommand.addListener(e=>{console.log("Chrome command received:",e),chrome.tabs.query({url:"*://app.airtm.com/peer-transfers/available*"},t=>{t.length>0&&t[0].id?chrome.tabs.sendMessage(t[0].id,{type:"EXECUTE_COMMAND",command:e}).catch(o=>{console.log("Could not send command to content script:",o)}):console.log("No active Airtm tab found for command:",e)})})}async function X(){try{const t=(await chrome.storage.local.get("current_offers")).current_offers||[];return console.log("Sending popup data:",{offersCount:t.length,settings:c,stats:f}),{offers:t,settings:c,stats:f}}catch(e){throw console.error("Error getting popup data:",e),e}}function ee(){const e=Date.now();if(e-F<J)return;const t=e-V;let o=0;for(const[r,n]of w.entries())n<t&&(w.delete(r),o++);F=e,o>0&&(console.log(`🧹 Cleaned up ${o} old notified offers (older than 1 hour)`),z())}function te(e){return w.has(e)}function oe(e){w.set(e,Date.now()),z()}async function re(e){try{const{offers:t,timestamp:o}=e;if(!Array.isArray(t))throw new Error("Invalid offers data");if(console.log("Processing "+t.length+" offers"),await chrome.storage.local.set({current_offers:t}),ee(),!c.monitoring){console.log("Monitoring disabled, skipping offer processing"),x(t),f.lastUpdate=o||new Date().toISOString();try{chrome.runtime.sendMessage({type:"OFFERS_PROCESSED",data:{offers:t,stats:f}}).catch(a=>{console.log("Could not notify popup (popup may be closed):",a.message)})}catch(a){console.log("Error sending OFFERS_PROCESSED message:",a)}return}x(t);const r=ne(t);console.log(r.length+" offers passed filters");const n=r.filter(a=>!te(a.id));console.log(n.length+" new offers (not previously notified)"),n.length>0&&c.soundEnabled&&c.notificationsEnabled&&(console.log("Playing sound notification once for "+n.length+" new offers"),await Y());for(const a of n)oe(a.id),await se(a,!1);f.lastUpdate=o||new Date().toISOString();try{chrome.runtime.sendMessage({type:"OFFERS_PROCESSED",data:{offers:t,stats:f}}).catch(a=>{console.log("Could not notify popup (popup may be closed):",a.message)})}catch(a){console.log("Error sending OFFERS_PROCESSED message:",a)}}catch(t){console.error("Error handling offers update:",t)}}function ne(e){return e.filter(t=>{var r,n,a,s,i,d;const o=typeof t.grossAmount=="string"?parseFloat(t.grossAmount):t.grossAmount;if(o<c.minAmount||o>c.maxAmount)return!1;if(c.preferredCurrencies.length>0){const u=((r=t.currency)==null?void 0:r.symbol)||((n=t.walletCurrency)==null?void 0:n.symbol)||"";if(!c.preferredCurrencies.includes(u))return!1}if(c.paymentMethods.length>0){const u=((i=(s=(a=t.makerPaymentMethod)==null?void 0:a.version)==null?void 0:s.category)==null?void 0:i.translationTag)||"";if(c.fuzzyMatching.enabled){const l=B.matchAnyPaymentMethod(u,c.paymentMethods);if(l.isMatch)console.log(`Offer ${t.id} matched payment method "${u}" with alias "${l.matchedAlias}" (score: ${l.score.toFixed(2)})`);else return console.log(`Offer ${t.id} filtered out: payment method "${u}" doesn't match any configured methods (best score: ${l.score.toFixed(2)})`),!1}else{let l=u;if(l.startsWith("CATEGORY_TREE:AIRTM_")&&(l=l.replace("CATEGORY_TREE:AIRTM_","")),l.startsWith("E_TRANSFER_")&&(l=l.replace("E_TRANSFER_","")),!c.paymentMethods.some(g=>l.includes(g)))return console.log(`Offer ${t.id} filtered out: payment method "${l}" doesn't match any configured methods (legacy matching)`),!1}}if(c.countries.length>0){const u=((d=t.peer)==null?void 0:d.country)||"";if(!c.countries.includes(u))return!1}if(c.keywords.length>0){const u=JSON.stringify(t).toLowerCase();if(!c.keywords.some(l=>u.includes(l.toLowerCase())))return!1}if(c.blacklistKeywords.length>0){const u=JSON.stringify(t).toLowerCase();if(c.blacklistKeywords.some(l=>u.includes(l.toLowerCase())))return console.log(`Offer ${t.id} filtered out by blacklist keyword`),!1}return!0})}async function ae(e){try{const t=await chrome.tabs.query({url:"*://app.airtm.com/peer-transfers/available*"});if(t.length===0){console.log("No Airtm tab found, opening new tab");const r=await chrome.tabs.create({url:"https://app.airtm.com/peer-transfers/available",active:!0});if(await new Promise(n=>setTimeout(n,3e3)),r.windowId)try{await chrome.windows.update(r.windowId,{focused:!0,state:"maximized"}),console.log("✅ New window maximized and focused")}catch(n){console.warn("⚠️ Failed to maximize new window:",n);try{await chrome.windows.update(r.windowId,{focused:!0}),console.log("✅ New window focused (without maximizing)")}catch(a){console.error("❌ Failed to focus new window:",a)}}return}const o=t[0];if(o.windowId){try{await chrome.windows.update(o.windowId,{focused:!0,state:"maximized"}),console.log("✅ Window maximized and focused for offer:",e.id)}catch(n){console.warn("⚠️ Failed to maximize window:",n);try{await chrome.windows.update(o.windowId,{focused:!0}),console.log("✅ Window focused (without maximizing) for offer:",e.id)}catch(a){console.error("❌ Failed to focus window:",a)}}let r=!1;for(let n=1;n<=3;n++)try{await chrome.tabs.update(o.id,{active:!0}),console.log(`✅ Tab activated (attempt ${n}) for offer:`,e.id),r=!0;break}catch(a){console.warn(`⚠️ Tab activation attempt ${n} failed:`,a),n<3&&await new Promise(s=>setTimeout(s,500))}r||console.error("❌ Failed to activate tab after 3 attempts"),await new Promise(n=>setTimeout(n,1e3));try{await chrome.tabs.sendMessage(o.id,{type:"FOCUS_OFFER",data:{offerId:e.id}}),console.log("✅ Focus message sent to content script for offer:",e.id)}catch(n){console.log("⚠️ Could not send focus message to content script:",n)}}else console.error("❌ No window ID found for Airtm tab")}catch(t){console.error("❌ Error maximizing window and focusing offer:",t)}}async function se(e,t=!0){try{console.log("Processing offer "+e.id),await ae(e),c.notificationsEnabled&&await ce(e,t),console.log("Checking Telegram settings:",{botToken:c.telegramBotToken?"***SET***":"NOT SET",chatId:c.telegramChatId?"***SET***":"NOT SET"}),c.telegramBotToken&&c.telegramChatId?(console.log("Sending Telegram message for offer:",e.id),await N(e)):console.log("Telegram not configured - skipping message"),c.webhookUrl?(console.log("Sending webhook message for offer:",e.id),await ie(e)):console.log("Webhook not configured - skipping webhook"),c.autoAccept&&await k(e.id)}catch(o){console.error("Error processing offer "+e.id+":",o)}}async function ce(e,t=!0){var o,r;try{$.updateSettings({autoCloseDelay:3e4,playSound:c.soundEnabled&&t}),await $.showNotification(e)}catch(n){console.error("Error sending notification:",n);try{const a=b(e),s="New "+e.operationType+" Offer";let i=`${a.amount} ${a.currency}`;a.conversionNote&&(i+=` ${a.conversionNote}`),await chrome.notifications.create({type:"basic",iconUrl:"icons/icon48.png",title:s,message:i,contextMessage:"From: "+(((o=e.peer)==null?void 0:o.firstName)||"")+" "+(((r=e.peer)==null?void 0:r.lastName)||""),buttons:[{title:"Accept"},{title:"Reject"}]}),c.soundEnabled&&t&&await Y()}catch(a){console.error("Fallback notification also failed:",a)}}}async function N(e){try{const t=typeof e=="string"?e:R(e),o="https://api.telegram.org/bot"+c.telegramBotToken+"/sendMessage";console.log("Sending Telegram message to:",c.telegramChatId),console.log("Message content:",t);const r=await fetch(o,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({chat_id:c.telegramChatId,text:t,parse_mode:"Markdown"})}),n=await r.json();r.ok?console.log("✅ Telegram message sent successfully:",n):console.error("❌ Telegram API error:",n)}catch(t){console.error("❌ Error sending Telegram message:",t)}}async function ie(e){try{const t={timestamp:new Date().toISOString(),offer:e,event:"new_offer",metadata:{extensionVersion:"1.0.0",source:"airtm-monitor-pro"}};console.log("Sending webhook to:",c.webhookUrl),console.log("Webhook payload:",t);const o=await fetch(c.webhookUrl,{method:"POST",headers:{"Content-Type":"application/json","User-Agent":"Airtm-Monitor-Pro/1.0.0"},body:JSON.stringify(t)});o.ok?console.log("✅ Webhook sent successfully:",o.status):console.error("❌ Webhook failed with status:",o.status,await o.text())}catch(t){console.error("❌ Error sending webhook:",t)}}function le(e){return{USA:"🇺🇸",US:"🇺🇸",IND:"🇮🇳",IN:"🇮🇳",GBR:"🇬🇧",GB:"🇬🇧",UK:"🇬🇧",CAN:"🇨🇦",CA:"🇨🇦",AUS:"🇦🇺",AU:"🇦🇺",DEU:"🇩🇪",DE:"🇩🇪",GER:"🇩🇪",FRA:"🇫🇷",FR:"🇫🇷",ESP:"🇪🇸",ES:"🇪🇸",ITA:"🇮🇹",IT:"🇮🇹",BRA:"🇧🇷",BR:"🇧🇷",MEX:"🇲🇽",MX:"🇲🇽",ARG:"🇦🇷",AR:"🇦🇷",COL:"🇨🇴",CO:"🇨🇴",VEN:"🇻🇪",VE:"🇻🇪",PER:"🇵🇪",PE:"🇵🇪",CHL:"🇨🇱",CL:"🇨🇱",URY:"🇺🇾",UY:"🇺🇾",ECU:"🇪🇨",EC:"🇪🇨",BOL:"🇧🇴",BO:"🇧🇴",PRY:"🇵🇾",PY:"🇵🇾",CHN:"🇨🇳",CN:"🇨🇳",JPN:"🇯🇵",JP:"🇯🇵",KOR:"🇰🇷",KR:"🇰🇷",RUS:"🇷🇺",RU:"🇷🇺",TUR:"🇹🇷",TR:"🇹🇷",EGY:"🇪🇬",EG:"🇪🇬",ZAF:"🇿🇦",ZA:"🇿🇦",NGA:"🇳🇬",NG:"🇳🇬",KEN:"🇰🇪",KE:"🇰🇪",GHA:"🇬🇭",GH:"🇬🇭",MAR:"🇲🇦",MA:"🇲🇦",TUN:"🇹🇳",TN:"🇹🇳",DZA:"🇩🇿",DZ:"🇩🇿",MOZ:"🇲🇿",MZ:"🇲🇿",PAN:"🇵🇦",PA:"🇵🇦",IDN:"🇮🇩",ID:"🇮🇩",THA:"🇹🇭",TH:"🇹🇭",VNM:"🇻🇳",VN:"🇻🇳",PHL:"🇵🇭",PH:"🇵🇭",MYS:"🇲🇾",MY:"🇲🇾",SGP:"🇸🇬",SG:"🇸🇬",PAK:"🇵🇰",PK:"🇵🇰",BGD:"🇧🇩",BD:"🇧🇩",LKA:"🇱🇰",LK:"🇱🇰",NPL:"🇳🇵",NP:"🇳🇵",AFG:"🇦🇫",AF:"🇦🇫",IRN:"🇮🇷",IR:"🇮🇷",IRQ:"🇮🇶",IQ:"🇮🇶",SAU:"🇸🇦",SA:"🇸🇦",ARE:"🇦🇪",AE:"🇦🇪",QAT:"🇶🇦",QA:"🇶🇦",KWT:"🇰🇼",KW:"🇰🇼",BHR:"🇧🇭",BH:"🇧🇭",OMN:"🇴🇲",OM:"🇴🇲",JOR:"🇯🇴",JO:"🇯🇴",LBN:"🇱🇧",LB:"🇱🇧",SYR:"🇸🇾",SY:"🇸🇾",ISR:"🇮🇱",IL:"🇮🇱",PSE:"🇵🇸",PS:"🇵🇸"}[e.toUpperCase()]||e}function b(e){var t,o,r,n,a,s,i;try{const d=((t=e.walletCurrency)==null?void 0:t.symbol)||"$",u=((o=e.currency)==null?void 0:o.symbol)||((r=e.walletCurrency)==null?void 0:r.symbol)||"Unknown";if(d===u||!e.currency){const p=parseFloat(e.grossAmount||"0");return{amount:_(p),currency:d}}const l=e.rateInfo;if(l&&l.fundsToSendTaker&&l.fundsToReceiveTaker){let p,A;return e.operationType==="BUY"?(p=parseFloat(l.fundsToReceiveTaker),A=parseFloat(l.fundsToSendTaker)):(p=parseFloat(l.fundsToSendTaker),A=parseFloat(l.fundsToReceiveTaker)),console.log(`Using rateInfo amounts for ${e.operationType}: ${A} ${d} ↔ ${p} ${u}`),{amount:_(p),currency:u,originalAmount:_(A),originalCurrency:d,exchangeRate:(p/A).toFixed(4)}}let g,m=null,h="";if(e.netAmount&&parseFloat(e.netAmount)>0?g=parseFloat(e.netAmount):g=parseFloat(e.grossAmount||"0"),e.rate?(m=parseFloat(e.rate),h="direct"):(n=e.displayRate)!=null&&n.rate?(m=parseFloat(e.displayRate.rate),h="displayRate"):l&&l.exchangeRate&&(m=parseFloat(l.exchangeRate),h="rateInfo.exchangeRate"),!m||m<=0)return{amount:_(g),currency:d,conversionNote:"(rate pending)"};let y;const O=((a=e.displayRate)==null?void 0:a.direction)||"TO_LOCAL_CURRENCY";return O==="TO_LOCAL_CURRENCY"?y=g*m:O==="FROM_LOCAL_CURRENCY"?y=g/m:(e.operationType,y=g*m),console.log(`Currency conversion: ${g} ${d} → ${y} ${u} (rate: ${m}, source: ${h}, direction: ${O})`),{amount:_(y),currency:u,originalAmount:_(g),originalCurrency:d,exchangeRate:m.toString()}}catch(d){console.error("Error in currency conversion:",d);const u=parseFloat(e.grossAmount||"0"),l=((s=e.currency)==null?void 0:s.symbol)||((i=e.walletCurrency)==null?void 0:i.symbol)||"Unknown";return{amount:_(u),currency:l,conversionNote:"(conversion error)"}}}function _(e){return e>=1e3?e.toLocaleString("en-US",{minimumFractionDigits:0,maximumFractionDigits:2}):e.toFixed(2).replace(/\.?0+$/,"")}function ue(){var m,h;console.log("🧪 Testing currency conversion with afteraccepting.txt example...");const e={id:"0d77a20b-f2ab-4dd6-8924-9fd102a37652",hash:"F2ABQP4DD6DK8924",operationType:"SELL",status:"ACCEPTED",isMine:!1,createdAt:"2025-06-12T16:23:51.645Z",updatedAt:"2025-06-12T16:24:00.386Z",grossAmount:"11",netAmount:"10.38",metadata:{isForThirdPartyPaymentMethod:!0,localCurrencyPrecision:2,walletCurrencyPrecision:2},walletCurrency:{symbol:"$",id:"USD",name:"US Dollar",precision:2,__typename:"Catalogs__Currency"},currency:{symbol:"£",id:"EGP",name:"Egyptian Pound",precision:2,__typename:"Catalogs__Currency"},peer:{id:"90e670af-de7f-45ba-8de2-e2698558271d",firstName:"احمد",lastName:"محمد السيد عبدالحافظ",createdAt:"2022-04-23T18:23:27.468Z",country:"EGY",countryInfo:{id:"EGY",__typename:"Catalogs__Country"},numbers:{id:"90e670af-de7f-45ba-8de2-e2698558271d",score:4.98,completedOperations:162,__typename:"Numbers__User"},__typename:"Auth__OperationUser"},rate:"49.57",displayRate:{rate:"47.42396313364055299539",direction:"TO_LOCAL_CURRENCY",__typename:"Operations__DisplayRate"},rateInfo:{fundsToSendTaker:"514.55",fundsToReceiveTaker:"10.85",grossAmount:"545.28",netAmount:"514.55",exchangeRate:"49.5709"},__typename:"Operations__Sell"},t=b(e);console.log("📊 Currency Conversion Test Results:"),console.log(`   Input: ${e.grossAmount} ${(m=e.walletCurrency)==null?void 0:m.symbol} (gross)`),console.log(`   Input: ${e.netAmount} ${(h=e.walletCurrency)==null?void 0:h.symbol} (net)`),console.log("   Expected Local: 514.55 £"),console.log("   Expected Wallet: 10.85 $"),console.log(`   Actual Result: ${t.amount} ${t.currency}`),console.log(`   Original Amount: ${t.originalAmount} ${t.originalCurrency}`),console.log(`   Exchange Rate: ${t.exchangeRate}`),console.log(`   Conversion Note: ${t.conversionNote||"None"}`);const o=514.55,r=parseFloat(t.amount.replace(/,/g,"")),n=Math.abs(r-o)<.01;console.log(`✅ Test Result: ${n?"PASSED":"FAILED"}`),n||console.log(`   Expected: ${o}, Got: ${r}`);const a=R(e);console.log("📱 Telegram Message:"),console.log(a),console.log(`
🧪 Testing same-currency scenario (USD to USD)...`);const s={id:"055c2bee-d063-4ced-acac-27b623954fa5",hash:"D063YU4CEDVGACAC",operationType:"SELL",status:"CREATED",isMine:!1,createdAt:"2025-06-14T13:19:58.291Z",updatedAt:null,grossAmount:"62.16",netAmount:"58.87",metadata:{isForThirdPartyPaymentMethod:!0,localCurrencyPrecision:2,walletCurrencyPrecision:2},walletCurrency:{symbol:"$",id:"USD",name:"US Dollar",precision:2,__typename:"Catalogs__Currency"},currency:{symbol:"$",id:"USD",name:"US Dollar",precision:2,__typename:"Catalogs__Currency"},peer:{id:"8021567e-1d57-446e-ae65-ffe04f622241",firstName:"IRIS DANIELA",lastName:"S.",createdAt:"2025-03-17T00:20:06.523Z",country:"PAN",countryInfo:{id:"PAN",__typename:"Catalogs__Country"},numbers:{id:"8021567e-1d57-446e-ae65-ffe04f622241",score:4.47,completedOperations:32,__typename:"Numbers__User"},__typename:"Auth__OperationUser"},rate:"1",rateInfo:{fundsToReceiveTaker:"61.23",fundsToSendTaker:"58.86"},displayRate:{direction:"TO_WALLET_CURRENCY",rate:"1.04026503567787971458",__typename:"Operations__DisplayRate"},makerPaymentMethod:{version:{category:{translationTag:"CATEGORY_TREE:AIRTM_MOBILE_YAPPY"}}},__typename:"Operations__Sell"},i=b(s);console.log("📊 Same Currency Test Results:"),console.log("   Expected: 62.16 $ (no conversion)"),console.log(`   Actual Result: ${i.amount} ${i.currency}`),console.log(`   Original Amount: ${i.originalAmount||"None"}`),console.log(`   Conversion Note: ${i.conversionNote||"None"}`);const d=R(s);console.log("📱 Same Currency Telegram Message:"),console.log(d),console.log(`
🧪 Testing BUY operation scenario (CNY to USD)...`);const u={id:"8d0a8483-3ea0-43b9-9a93-c48f22abe919",hash:"3EA0YE43B9EC9A93",operationType:"BUY",status:"CREATED",isMine:!1,createdAt:"2025-06-14T13:49:43.184Z",updatedAt:null,grossAmount:"278.97",netAmount:"269.84",metadata:{isForThirdPartyPaymentMethod:null,localCurrencyPrecision:2,walletCurrencyPrecision:2},walletCurrency:{symbol:"$",id:"USD",name:"US Dollar",precision:2,__typename:"Catalogs__Currency"},currency:{symbol:"¥",id:"CNY",name:"Chinese Yuan",precision:2,__typename:"Catalogs__Currency"},peer:{id:"d37923bd-23c8-4f2c-99fc-15c665e090ea",firstName:"ALY ADRIANO",lastName:"S.",createdAt:"2025-02-11T17:11:19.413Z",country:"MOZ",countryInfo:{id:"MOZ",__typename:"Catalogs__Country"},numbers:{id:"d37923bd-23c8-4f2c-99fc-15c665e090ea",score:4.28,completedOperations:82,__typename:"Numbers__User"},__typename:"Auth__OperationUser"},rate:"7.1692",rateInfo:{fundsToReceiveTaker:"2000",fundsToSendTaker:"273.75",netAmount:"1934.55",grossAmount:"2000"},displayRate:{direction:"TO_LOCAL_CURRENCY",rate:"7.30593607305936073059",__typename:"Operations__DisplayRate"},makerPaymentMethod:{version:{category:{translationTag:"CATEGORY_TREE:AIRTM_E_TRANSFER_ALIPAY"}}},__typename:"Operations__Buy"},l=b(u);console.log("📊 BUY Operation Test Results:"),console.log("   Expected: 2,000 ¥ (273.75 $)"),console.log(`   Actual Result: ${l.amount} ${l.currency}`),console.log(`   Original Amount: ${l.originalAmount} ${l.originalCurrency}`),console.log(`   Exchange Rate: ${l.exchangeRate}`);const g=R(u);console.log("📱 BUY Operation Telegram Message:"),console.log(g),console.log(`
🧪 Currency conversion tests completed.`)}function R(e){var l,g,m,h,y,O,p,A,D,U;const t=b(e),o=((((l=e.peer)==null?void 0:l.firstName)||"")+" "+(((g=e.peer)==null?void 0:g.lastName)||"")).trim(),r=((h=(m=e.peer)==null?void 0:m.numbers)==null?void 0:h.score)||0,n=((O=(y=e.peer)==null?void 0:y.numbers)==null?void 0:O.completedOperations)||0,a=((p=e.peer)==null?void 0:p.country)||"Unknown",s=le(a);let d=((U=(D=(A=e.makerPaymentMethod)==null?void 0:A.version)==null?void 0:D.category)==null?void 0:U.translationTag)||"Unknown";d.startsWith("CATEGORY_TREE:AIRTM_")&&(d=d.replace("CATEGORY_TREE:AIRTM_","")),d.startsWith("E_TRANSFER_")&&(d=d.replace("E_TRANSFER_","")),d=d.replace(/_/g," ").toLowerCase().replace(/\b\w/g,W=>W.toUpperCase());let u=`${t.amount} ${t.currency}`;return t.conversionNote&&(u+=` ${t.conversionNote}`),t.originalAmount&&t.originalCurrency&&t.originalCurrency!==t.currency&&(u+=` (${t.originalAmount} ${t.originalCurrency})`),`${u} : ${d} (${s})
👤 User: ${o} ${r}⭐(${n} trades)`}let E=!1,S=null;async function Y(){return S||(S=(async()=>{var e;try{if(!E)try{await chrome.offscreen.createDocument({url:"sounds/beep.html",reasons:[chrome.offscreen.Reason.AUDIO_PLAYBACK],justification:"Play notification sound for new offers"}),E=!0}catch(t){if((e=t.message)!=null&&e.includes("Only a single offscreen document may be created"))E=!0;else throw t}try{await chrome.runtime.sendMessage({type:"PLAY_SOUND"})}catch(t){throw console.log("Message to offscreen document failed:",t),E=!1,t}}catch(t){console.error("Error playing notification sound:",t),E=!1}finally{try{E&&(await chrome.offscreen.closeDocument(),E=!1)}catch{E=!1}S=null}})(),S)}async function k(e){try{const t=await chrome.tabs.query({url:"*://app.airtm.com/peer-transfers/available*"});if(t.length===0)throw new Error("Airtm tab not found");const o=await chrome.tabs.sendMessage(t[0].id,{type:"ACCEPT_OFFER",data:{offerId:e}});if(o!=null&&o.success)return f.acceptedOffers++,console.log("Offer "+e+" accepted successfully"),me(e,t[0].id),!0;throw new Error((o==null?void 0:o.error)||"Failed to accept offer")}catch(t){throw console.error("Error accepting offer "+e+":",t),t}}async function de(e){try{console.log("📋 Processing operation details update:",e);const{operation:t,timestamp:o,source:r}=e;if(!t||!t.id){console.warn("Invalid operation data received");return}await chrome.storage.local.set({[`operation_${t.id}`]:{...t,lastUpdated:o,source:r}}),c.telegramBotToken&&c.telegramChatId&&await ge(t),console.log(`✅ Operation ${t.id} details updated - Status: ${t.status}`)}catch(t){console.error("❌ Error handling operation details update:",t)}}function me(e,t){console.log(`🔍 Starting URL monitoring for accepted offer: ${e}`);let o=0;const r=30,n=setInterval(async()=>{try{o++;const s=(await chrome.tabs.get(t)).url;if(console.log(`🔍 URL check ${o}/${r}: ${s}`),s&&/https:\/\/app\.airtm\.com\/operations\/[A-Z0-9]+/.test(s)){console.log("✅ Operation URL detected:",s);const d=s.split("/operations/")[1];c.telegramBotToken&&c.telegramChatId&&await N(`🎯 Offer ${e} accepted successfully!
📋 Operation ID: ${d}
🔗 URL: ${s}
⏰ Status: Accepted`),await chrome.storage.local.set({[`accepted_operation_${e}`]:{operationId:d,url:s,timestamp:new Date().toISOString(),status:"accepted"}}),clearInterval(n);return}o>=r&&(console.log("⚠️ Operation URL not detected within timeout"),c.telegramBotToken&&c.telegramChatId&&await N(`❌ Offer ${e} acceptance failed or timed out
⏰ Status: Not Available`),await chrome.storage.local.set({[`failed_operation_${e}`]:{timestamp:new Date().toISOString(),status:"not_available",reason:"url_not_detected"}}),clearInterval(n))}catch(a){console.error("❌ Error during URL monitoring:",a),clearInterval(n)}},100)}async function ge(e){var t,o,r,n;try{const a=(t=e.metadata)==null?void 0:t.displayRateInfo,s={id:e.id||"",hash:e.hash||"",operationType:e.operationType||"UNKNOWN",status:e.status||"UNKNOWN",isMine:e.isMine||!1,createdAt:e.createdAt||"",updatedAt:e.updatedAt||null,grossAmount:e.grossAmount||"0",netAmount:e.netAmount||"0",metadata:e.metadata||{},walletCurrency:e.walletCurrency||{symbol:"$"},peer:e.secondParty||e.peer||{firstName:"Unknown",lastName:""},rate:e.rate,displayRate:e.displayRate,rateInfo:{...e.rateInfo,fundsToSendTaker:(a==null?void 0:a.fundsToReceive)||((o=e.rateInfo)==null?void 0:o.fundsToSendTaker),fundsToReceiveTaker:(a==null?void 0:a.fundsToSend)||((r=e.rateInfo)==null?void 0:r.fundsToReceiveTaker),exchangeRate:(a==null?void 0:a.exchangeRate)||((n=e.rateInfo)==null?void 0:n.exchangeRate)},currency:e.currency,makerPaymentMethod:e.makerPaymentMethod,__typename:e.__typename||"Operations__Unknown"},i=b(s),d=e.secondParty?`${e.secondParty.firstName||""} ${e.secondParty.lastName||""}`.trim():e.peer?`${e.peer.firstName||""} ${e.peer.lastName||""}`.trim():"Unknown";let u=`${i.amount} ${i.currency}`;i.conversionNote&&(u+=` ${i.conversionNote}`),i.originalAmount&&i.originalCurrency&&i.originalCurrency!==i.currency&&(u+=` (${i.originalAmount} ${i.originalCurrency})`);const l=`📋 Operation Update
🆔 ID: ${e.hash||e.id}
📊 Status: ${e.status}
💰 Amount: ${u}
🔄 Type: ${e.operationType}
👤 Partner: ${d}
⏰ Updated: ${new Date().toLocaleString()}`;await N(l)}catch(a){console.error("❌ Error sending Telegram operation update:",a)}}async function fe(e){try{console.log("✅ Processing operation acceptance:",e),await chrome.storage.local.set({[`accepted_operation_${e.operationId}`]:{...e,processedAt:new Date().toISOString()}}),f.acceptedOffers++,console.log("✅ Operation acceptance processed successfully")}catch(t){console.error("❌ Error handling operation accepted:",t)}}async function he(e){try{console.log("🚫 Processing operation not available:",e),await chrome.storage.local.set({[`unavailable_operation_${e.operationId||"unknown"}`]:{...e,processedAt:new Date().toISOString()}}),console.log("🚫 Operation not available processed successfully")}catch(t){console.error("❌ Error handling operation not available:",t)}}async function pe(e){try{console.log("⚠️ Processing operation accept error:",e),await chrome.storage.local.set({[`error_operation_${Date.now()}`]:{...e,processedAt:new Date().toISOString()}}),console.log("⚠️ Operation accept error processed successfully")}catch(t){console.error("❌ Error handling operation accept error:",t)}}async function ye(e){try{console.log("🚫 Processing operation decline:",e),await chrome.storage.local.set({[`declined_operation_${Date.now()}`]:{...e,processedAt:new Date().toISOString()}}),f.rejectedOffers++,console.log("🚫 Operation decline processed successfully")}catch(t){console.error("❌ Error handling operation declined:",t)}}async function we(e){try{console.log("⚠️ Processing operation decline error:",e),await chrome.storage.local.set({[`decline_error_operation_${Date.now()}`]:{...e,processedAt:new Date().toISOString()}}),console.log("⚠️ Operation decline error processed successfully")}catch(t){console.error("❌ Error handling operation decline error:",t)}}async function Ae(e){try{console.log("📱 Sending Telegram operation status update:",e);const{operationId:t,status:o}=e;let r;o==="accepted"?r=`✅ *Operation Accepted*

🆔 Operation ID: ${t}
📊 Status: Successfully accepted
⏰ Time: ${new Date().toLocaleString()}

🎉 Great! The operation was accepted successfully.`:o==="Not Available"?r=`🚫 *Operation Not Available*

🆔 Operation ID: ${t}
📊 Status: No longer available
⏰ Time: ${new Date().toLocaleString()}

😔 The operation was taken by someone else or expired.`:r=`📋 *Operation Update*

🆔 Operation ID: ${t}
📊 Status: ${o}
⏰ Time: ${new Date().toLocaleString()}`,await N(r),console.log("📱 Telegram operation status update sent successfully")}catch(t){console.error("❌ Error sending Telegram operation status update:",t)}}async function G(e){try{const t=await chrome.tabs.query({url:"*://app.airtm.com/peer-transfers/available*"});if(t.length===0)throw new Error("Airtm tab not found");const o=await chrome.tabs.sendMessage(t[0].id,{type:"REJECT_OFFER",data:{offerId:e}});if(o!=null&&o.success)return f.rejectedOffers++,console.log("Offer "+e+" rejected successfully"),!0;throw new Error((o==null?void 0:o.error)||"Failed to reject offer")}catch(t){throw console.error("Error rejecting offer "+e+":",t),t}}function x(e){f.totalOffers=e.length,f.newOffers=e.filter(t=>{const o=new Date(t.createdAt),r=new Date(Date.now()-5*60*1e3);return o>r}).length,f.averageRate=0}async function Ee(){try{const e=await chrome.storage.sync.get("settings");c={...P,...e.settings},c.monitoring=!0,v(),await chrome.storage.sync.set({settings:c}),console.log("Settings loaded and monitoring enabled:",c)}catch(e){console.error("Error loading settings:",e),c={...P,monitoring:!0},v()}}async function _e(e){try{c={...c,...e},e.fuzzyMatching&&v(),await chrome.storage.sync.set({settings:c}),console.log("Settings updated:",c),j()}catch(t){throw console.error("Error updating settings:",t),t}}function j(){c.monitoring?(chrome.action.setBadgeText({text:"ON"}),chrome.action.setBadgeBackgroundColor({color:"#10b981"})):(chrome.action.setBadgeText({text:"OFF"}),chrome.action.setBadgeBackgroundColor({color:"#6b7280"}))}function v(){try{const e=c.fuzzyMatching||{enabled:!1,threshold:.8,enableAliases:!0,customAliases:{}};B.updateConfig({threshold:e.threshold,enableAliases:e.enableAliases,customAliases:e.customAliases}),console.log("Fuzzy matcher configuration updated:",e)}catch(e){console.error("Error updating fuzzy matcher configuration:",e)}}function Te(){chrome.alarms.onAlarm.addListener(e=>{switch(console.log("Alarm triggered:",e.name),e.name){case"monitoring-check":break;case"cleanup-storage":Ce();break}}),chrome.alarms.create("cleanup-storage",{periodInMinutes:60})}function Oe(){chrome.notifications.onButtonClicked.addListener(async(e,t)=>{try{const o=e.match(/offer_(.+)_\d+/),r=o?o[1]:e;t===0?await k(r):t===1&&await G(r),chrome.notifications.clear(e)}catch(o){console.error("Error handling notification button click:",o)}}),chrome.runtime.onMessage.addListener((e,t,o)=>{(e.type==="ACCEPT_OFFER"||e.type==="REJECT_OFFER"||e.type==="IGNORE_OFFER")&&$.handleMessage(e,t)})}async function Ce(){try{console.log("Cleaning up storage...");const t=(await chrome.storage.local.get(["airtm_offers_timestamp"])).airtm_offers_timestamp;if(t){const o=Date.now()-new Date(t).getTime(),r=24*60*60*1e3;o>r&&(await chrome.storage.local.remove(["airtm_offers","airtm_offers_timestamp"]),console.log("Old offers cleaned up"))}}catch(e){console.error("Error cleaning up storage:",e)}}async function be(){try{console.log("🔍 Checking for existing Airtm tabs...");const e=await chrome.tabs.query({url:"*://app.airtm.com/peer-transfers/available*"});if(e.length===0){console.log("📭 No existing Airtm tabs found");return}console.log(`🎯 Found ${e.length} existing Airtm tab(s)`),console.log("ℹ️ Content scripts will be automatically injected by Chrome based on manifest.json");for(const t of e)t.id&&t.url&&console.log(`📋 Found Airtm tab ${t.id}: ${t.url}`)}catch(e){console.error("❌ Error checking existing tabs:",e)}}function Se(){const e=()=>{var t;try{(t=chrome==null?void 0:chrome.runtime)!=null&&t.getPlatformInfo&&chrome.runtime.getPlatformInfo(()=>{chrome.runtime.lastError&&console.warn("Keep alive error:",chrome.runtime.lastError)})}catch(o){console.warn("Keep alive failed:",o)}};e(),setInterval(e,2e4)}I().then(()=>{Se()});chrome.runtime.onStartup.addListener(()=>{console.log("Service worker starting up..."),I()});chrome.runtime.onInstalled.addListener(e=>{console.log("Extension installed/updated:",e.reason),I()});
