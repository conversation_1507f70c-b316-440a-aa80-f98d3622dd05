class h{constructor(){this.settings={monitoring:!1,refreshInterval:10,monitorBuyPage:!0,monitorSellPage:!0,autoAccept:!1,telegramBotToken:"",telegramChatId:"",minAmount:0,maxAmount:1e4,preferredCurrencies:["USD"],blacklistedUsers:[]},this.isConnected=!1,this.saveTimeout=null,this.init()}async init(){try{this.showLoading(),await this.loadSettings(),await this.checkConnection(),this.setupEventListeners(),this.updateUI(),setTimeout(()=>{this.hideLoading()},1500)}catch(e){console.error("Failed to initialize options app:",e),this.showError("Failed to initialize application",e.message)}}showLoading(){const e=document.getElementById("loading-screen"),s=document.getElementById("app");e.classList.remove("hidden"),s.classList.add("hidden")}hideLoading(){const e=document.getElementById("loading-screen"),s=document.getElementById("app");e.classList.add("hidden"),s.classList.remove("hidden")}async loadSettings(){try{if(chrome?.storage?.sync){const e=await chrome.storage.sync.get("settings");e.settings&&(this.settings={...this.settings,...e.settings})}}catch(e){throw console.error("Failed to load settings:",e),new Error("Unable to load settings from storage")}}async saveSettings(){try{if(chrome?.storage?.sync)await chrome.storage.sync.set({settings:this.settings}),this.showSaveStatus("Settings saved successfully!","success");else throw new Error("Chrome storage not available")}catch(e){console.error("Failed to save settings:",e),this.showSaveStatus("Failed to save settings","error")}}async checkConnection(){try{this.isConnected=!!chrome?.runtime?.id,this.updateConnectionStatus()}catch{this.isConnected=!1,this.updateConnectionStatus()}}updateConnectionStatus(){const e=document.getElementById("connection-status"),s=e.querySelector("span");this.isConnected?(e.classList.add("connected"),e.classList.remove("disconnected"),s.textContent="Connected"):(e.classList.add("disconnected"),e.classList.remove("connected"),s.textContent="Disconnected")}setupEventListeners(){document.querySelectorAll(".nav-link").forEach(n=>{n.addEventListener("click",d=>{d.preventDefault();const p=n.dataset.section;this.showSection(p),this.setActiveNavLink(n)})}),document.getElementById("monitoring-enabled").addEventListener("change",n=>{this.settings.monitoring=n.target.checked,this.updateMonitoringStatus(),this.debouncedSave()});const t=document.getElementById("refresh-interval"),o=document.getElementById("refresh-interval-value");t.addEventListener("input",n=>{const d=parseInt(n.target.value);this.settings.refreshInterval=d,o.textContent=`${d}s`,this.debouncedSave()});const a=document.getElementById("monitor-buy-page"),i=document.getElementById("monitor-sell-page");a.addEventListener("change",n=>{this.settings.monitorBuyPage=n.target.checked,this.debouncedSave()}),i.addEventListener("change",n=>{this.settings.monitorSellPage=n.target.checked,this.debouncedSave()}),document.getElementById("auto-accept-enabled").addEventListener("change",n=>{this.settings.autoAccept=n.target.checked,this.updateAutoAcceptStatus(),this.debouncedSave()});const u=document.getElementById("min-amount"),m=document.getElementById("max-amount");u.addEventListener("input",n=>{this.settings.minAmount=parseFloat(n.target.value)||0,this.debouncedSave()}),m.addEventListener("input",n=>{this.settings.maxAmount=parseFloat(n.target.value)||1e4,this.debouncedSave()});const g=document.getElementById("telegram-bot-token"),c=document.getElementById("telegram-chat-id"),v=document.getElementById("test-telegram");g.addEventListener("input",n=>{this.settings.telegramBotToken=n.target.value,this.debouncedSave()}),c.addEventListener("input",n=>{this.settings.telegramChatId=n.target.value,this.debouncedSave()}),v.addEventListener("click",()=>{this.testTelegramConnection()}),document.getElementById("save-settings").addEventListener("click",()=>{this.saveSettings()})}showSection(e){document.querySelectorAll(".content-section").forEach(o=>{o.classList.remove("active")});const t=document.getElementById(`${e}-section`);t&&t.classList.add("active")}setActiveNavLink(e){document.querySelectorAll(".nav-link").forEach(t=>{t.classList.remove("active")}),e.classList.add("active")}updateUI(){const e=document.getElementById("monitoring-enabled");e.checked=this.settings.monitoring;const s=document.getElementById("refresh-interval"),t=document.getElementById("refresh-interval-value");s.value=this.settings.refreshInterval,t.textContent=`${this.settings.refreshInterval}s`;const o=document.getElementById("monitor-buy-page"),a=document.getElementById("monitor-sell-page");o.checked=this.settings.monitorBuyPage,a.checked=this.settings.monitorSellPage;const i=document.getElementById("auto-accept-enabled");i.checked=this.settings.autoAccept;const r=document.getElementById("min-amount"),u=document.getElementById("max-amount");r.value=this.settings.minAmount,u.value=this.settings.maxAmount;const m=document.getElementById("telegram-bot-token"),g=document.getElementById("telegram-chat-id");m.value=this.settings.telegramBotToken,g.value=this.settings.telegramChatId;const c=document.getElementById("last-updated");c&&(c.textContent=new Date().toLocaleDateString()),this.updateMonitoringStatus(),this.updateAutoAcceptStatus()}updateMonitoringStatus(){const e=document.getElementById("monitoring-status-text");e.textContent=this.settings.monitoring?"Active":"Disabled",e.style.color=this.settings.monitoring?"var(--success-600)":"var(--gray-600)"}updateAutoAcceptStatus(){const e=document.getElementById("auto-accept-status-text");e.textContent=this.settings.autoAccept?"Enabled":"Disabled",e.style.color=this.settings.autoAccept?"var(--success-600)":"var(--gray-600)"}async testTelegramConnection(){const e=document.getElementById("test-telegram"),s=e.textContent;try{if(e.textContent="Testing...",e.disabled=!0,!this.settings.telegramBotToken||!this.settings.telegramChatId)throw new Error("Please enter both bot token and chat ID");await new Promise(t=>setTimeout(t,2e3)),this.showSaveStatus("Telegram connection successful!","success")}catch(t){this.showSaveStatus(`Telegram test failed: ${t.message}`,"error")}finally{e.textContent=s,e.disabled=!1}}debouncedSave(){this.saveTimeout&&clearTimeout(this.saveTimeout),this.saveTimeout=setTimeout(()=>{this.saveSettings()},1e3)}showSaveStatus(e,s){const t=document.getElementById("save-status");t.textContent=e,t.className=`save-status ${s}`,t.classList.remove("hidden"),setTimeout(()=>{t.classList.add("hidden")},3e3)}showError(e,s){const t=document.createElement("div");t.className="error-overlay",t.innerHTML=`
      <div class="error-modal">
        <div class="error-header">
          <h3>${e}</h3>
          <button class="error-close">&times;</button>
        </div>
        <div class="error-content">
          <p>${s}</p>
          <div class="error-actions">
            <button class="error-retry">Retry</button>
            <button class="error-dismiss">Dismiss</button>
          </div>
        </div>
      </div>
    `,document.body.appendChild(t);const o=t.querySelector(".error-close"),a=t.querySelector(".error-retry"),i=t.querySelector(".error-dismiss"),r=()=>{document.body.removeChild(t)};o.addEventListener("click",r),i.addEventListener("click",r),a.addEventListener("click",()=>{r(),this.init()})}}function y(){const l=document.createElement("style");l.textContent=`
    .error-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10000;
    }
    
    .error-modal {
      background: white;
      border-radius: var(--radius-xl);
      box-shadow: var(--shadow-xl);
      max-width: 500px;
      width: 90%;
      max-height: 80vh;
      overflow: hidden;
    }
    
    .error-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: var(--space-6);
      border-bottom: 1px solid var(--gray-200);
      background: var(--error-50);
    }
    
    .error-header h3 {
      color: var(--error-700);
      font-size: var(--font-size-xl);
      font-weight: 600;
    }
    
    .error-close {
      background: none;
      border: none;
      font-size: var(--font-size-2xl);
      color: var(--error-500);
      cursor: pointer;
      padding: var(--space-1);
    }
    
    .error-content {
      padding: var(--space-6);
    }
    
    .error-content p {
      color: var(--gray-700);
      margin-bottom: var(--space-6);
      line-height: 1.6;
    }
    
    .error-actions {
      display: flex;
      gap: var(--space-3);
      justify-content: flex-end;
    }
    
    .error-retry,
    .error-dismiss {
      padding: var(--space-3) var(--space-4);
      border-radius: var(--radius-md);
      font-weight: 500;
      cursor: pointer;
      transition: all var(--transition-fast);
    }
    
    .error-retry {
      background: var(--primary-500);
      color: white;
      border: none;
    }
    
    .error-retry:hover {
      background: var(--primary-600);
    }
    
    .error-dismiss {
      background: var(--gray-100);
      color: var(--gray-700);
      border: 1px solid var(--gray-300);
    }
    
    .error-dismiss:hover {
      background: var(--gray-200);
    }
  `,document.head.appendChild(l)}document.addEventListener("DOMContentLoaded",()=>{y(),new h});document.addEventListener("visibilitychange",()=>{document.hidden||window.airtmApp&&window.airtmApp.checkConnection()});window.AirtmOptionsApp=h;
